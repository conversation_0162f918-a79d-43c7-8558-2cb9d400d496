/**
 * Custom Reports Function
 * Handles custom report generation and management
 * Migrated from old-arch/src/analytics-service/reports/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { BlobServiceClient } from "@azure/storage-blob";
import { v4 as uuidv4 } from "uuid";
import * as <PERSON><PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { eventService } from '../shared/services/event';

// Report types and enums
enum ReportType {
  DOCUMENT_ACTIVITY = 'DOCUMENT_ACTIVITY',
  USER_ACTIVITY = 'USER_ACTIVITY',
  ORGANIZATION_OVERVIEW = 'ORGANIZATION_OVERVIEW',
  PROJECT_PERFORMANCE = 'PROJECT_PERFORMANCE',
  STORAGE_USAGE = 'STORAGE_USAGE',
  WORKFLOW_ANALYTICS = 'WORKFLOW_ANALYTICS',
  SECURITY_AUDIT = 'SECURITY_AUDIT',
  COMPLIANCE_REPORT = 'COMPLIANCE_REPORT',
  CUSTOM = 'CUSTOM'
}

enum ReportFormat {
  PDF = 'PDF',
  EXCEL = 'EXCEL',
  CSV = 'CSV',
  JSON = 'JSON'
}

enum ReportStatus {
  PENDING = 'PENDING',
  GENERATING = 'GENERATING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  EXPIRED = 'EXPIRED'
}

// Validation schemas
const createReportSchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  description: Joi.string().max(500).optional(),
  type: Joi.string().valid(...Object.values(ReportType)).required(),
  format: Joi.string().valid(...Object.values(ReportFormat)).default(ReportFormat.PDF),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional(),
  parameters: Joi.object({
    dateRange: Joi.object({
      startDate: Joi.string().isoDate().required(),
      endDate: Joi.string().isoDate().required()
    }).optional(),
    filters: Joi.object({
      userIds: Joi.array().items(Joi.string().uuid()).optional(),
      documentTypes: Joi.array().items(Joi.string()).optional(),
      categories: Joi.array().items(Joi.string()).optional(),
      tags: Joi.array().items(Joi.string()).optional(),
      status: Joi.array().items(Joi.string()).optional()
    }).optional(),
    groupBy: Joi.array().items(Joi.string().valid('user', 'date', 'category', 'type', 'project')).optional(),
    metrics: Joi.array().items(Joi.string()).optional(),
    includeCharts: Joi.boolean().default(true),
    includeDetails: Joi.boolean().default(true)
  }).optional(),
  schedule: Joi.object({
    enabled: Joi.boolean().default(false),
    frequency: Joi.string().valid('daily', 'weekly', 'monthly', 'quarterly').optional(),
    dayOfWeek: Joi.number().min(0).max(6).optional(),
    dayOfMonth: Joi.number().min(1).max(31).optional(),
    time: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
    recipients: Joi.array().items(Joi.string().email()).optional()
  }).optional()
});

interface CreateReportRequest {
  name: string;
  description?: string;
  type: ReportType;
  format?: ReportFormat;
  organizationId: string;
  projectId?: string;
  parameters?: {
    dateRange?: {
      startDate: string;
      endDate: string;
    };
    filters?: {
      userIds?: string[];
      documentTypes?: string[];
      categories?: string[];
      tags?: string[];
      status?: string[];
    };
    groupBy?: string[];
    metrics?: string[];
    includeCharts?: boolean;
    includeDetails?: boolean;
  };
  schedule?: {
    enabled?: boolean;
    frequency?: string;
    dayOfWeek?: number;
    dayOfMonth?: number;
    time?: string;
    recipients?: string[];
  };
}

interface CustomReport {
  id: string;
  name: string;
  description?: string;
  type: ReportType;
  format: ReportFormat;
  status: ReportStatus;
  organizationId: string;
  projectId?: string;
  parameters: any;
  schedule?: any;
  results?: {
    downloadUrl?: string;
    fileSize?: number;
    recordCount?: number;
    generatedAt?: string;
    expiresAt?: string;
  };
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  tenantId: string;
}

/**
 * Create custom report handler
 */
export async function createCustomReport(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create custom report started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createReportSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const reportRequest: CreateReportRequest = value;

    // Check organization access
    const hasAccess = await checkOrganizationAccess(reportRequest.organizationId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Check report creation limits
    const canCreate = await checkReportCreationLimits(reportRequest.organizationId);
    if (!canCreate.allowed) {
      return addCorsHeaders({
        status: 429,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: canCreate.reason }
      }, request);
    }

    // Create report
    const reportId = uuidv4();
    const now = new Date().toISOString();

    const report: CustomReport = {
      id: reportId,
      name: reportRequest.name,
      description: reportRequest.description,
      type: reportRequest.type,
      format: reportRequest.format || ReportFormat.PDF,
      status: ReportStatus.PENDING,
      organizationId: reportRequest.organizationId,
      projectId: reportRequest.projectId,
      parameters: reportRequest.parameters || {},
      schedule: reportRequest.schedule,
      createdBy: user.id,
      createdAt: now,
      updatedAt: now,
      tenantId: user.tenantId || user.id
    };

    await db.createItem('custom-reports', report);

    // Start report generation asynchronously
    await generateReportAsync(report);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "custom_report_created",
      userId: user.id,
      organizationId: reportRequest.organizationId,
      projectId: reportRequest.projectId,
      timestamp: now,
      details: {
        reportId,
        reportName: reportRequest.name,
        reportType: reportRequest.type,
        reportFormat: reportRequest.format,
        isScheduled: !!reportRequest.schedule?.enabled
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'CustomReportCreated',
      aggregateId: reportId,
      aggregateType: 'CustomReport',
      version: 1,
      data: {
        report,
        createdBy: user.id
      },
      userId: user.id,
      organizationId: reportRequest.organizationId,
      tenantId: user.tenantId
    });

    logger.info("Custom report created successfully", {
      correlationId,
      reportId,
      reportName: reportRequest.name,
      reportType: reportRequest.type,
      createdBy: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: reportId,
        name: reportRequest.name,
        type: reportRequest.type,
        format: reportRequest.format,
        status: ReportStatus.PENDING,
        estimatedDuration: estimateReportDuration(reportRequest.type, reportRequest.parameters),
        createdAt: now,
        message: "Custom report created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create custom report failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Get report status handler
 */
export async function getReportStatus(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const reportId = request.params.reportId;

  logger.info("Get report status started", { correlationId, reportId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    if (!reportId) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Report ID is required" }
      }, request);
    }

    // Get report
    const report = await db.readItem('custom-reports', reportId, reportId);
    if (!report) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Report not found" }
      }, request);
    }

    const reportData = report as any;

    // Check access
    const hasAccess = await checkOrganizationAccess(reportData.organizationId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to report" }
      }, request);
    }

    logger.info("Report status retrieved successfully", {
      correlationId,
      reportId,
      status: reportData.status,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: reportData.id,
        name: reportData.name,
        type: reportData.type,
        format: reportData.format,
        status: reportData.status,
        results: reportData.results,
        createdAt: reportData.createdAt,
        updatedAt: reportData.updatedAt
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get report status failed", {
      correlationId,
      reportId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkOrganizationAccess(organizationId: string, userId: string): Promise<boolean> {
  try {
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
    return memberships.length > 0;
  } catch (error) {
    logger.error('Failed to check organization access', { error, organizationId, userId });
    return false;
  }
}

async function checkReportCreationLimits(organizationId: string): Promise<{ allowed: boolean; reason?: string }> {
  try {
    // Get organization to check tier
    const organization = await db.readItem('organizations', organizationId, organizationId);
    if (!organization) {
      return { allowed: false, reason: 'Organization not found' };
    }

    const orgData = organization as any;
    const tier = orgData.tier || 'FREE';

    // Define tier limits
    const limits: { [key: string]: { maxReports: number; maxScheduledReports: number } } = {
      'FREE': { maxReports: 5, maxScheduledReports: 1 },
      'PROFESSIONAL': { maxReports: 50, maxScheduledReports: 10 },
      'ENTERPRISE': { maxReports: -1, maxScheduledReports: -1 } // Unlimited
    };

    const limit = limits[tier] || limits['FREE'];

    if (limit.maxReports === -1) {
      return { allowed: true };
    }

    // Check current report count
    const reportCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.status != @expired';
    const countResult = await db.queryItems('custom-reports', reportCountQuery, [organizationId, ReportStatus.EXPIRED]);
    const currentCount = Number(countResult[0]) || 0;

    if (currentCount >= limit.maxReports) {
      return {
        allowed: false,
        reason: `Report limit reached (${limit.maxReports})`
      };
    }

    return { allowed: true };

  } catch (error) {
    logger.error('Failed to check report creation limits', { error, organizationId });
    return { allowed: false, reason: 'Failed to check limits' };
  }
}

function estimateReportDuration(reportType: ReportType, parameters: any): string {
  // Simplified estimation - in production, use historical data
  const baseTimeMinutes = {
    [ReportType.DOCUMENT_ACTIVITY]: 5,
    [ReportType.USER_ACTIVITY]: 3,
    [ReportType.ORGANIZATION_OVERVIEW]: 10,
    [ReportType.PROJECT_PERFORMANCE]: 7,
    [ReportType.STORAGE_USAGE]: 2,
    [ReportType.WORKFLOW_ANALYTICS]: 8,
    [ReportType.SECURITY_AUDIT]: 15,
    [ReportType.COMPLIANCE_REPORT]: 20,
    [ReportType.CUSTOM]: 10
  };

  const baseTime = baseTimeMinutes[reportType] || 10;

  // Adjust based on date range
  if (parameters?.dateRange) {
    const start = new Date(parameters.dateRange.startDate);
    const end = new Date(parameters.dateRange.endDate);
    const daysDiff = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));

    if (daysDiff > 365) {
      return `${baseTime * 3} minutes`;
    } else if (daysDiff > 90) {
      return `${baseTime * 2} minutes`;
    }
  }

  return `${baseTime} minutes`;
}

async function generateReportAsync(report: CustomReport): Promise<void> {
  try {
    // Update status to generating
    const updatedReport = {
      ...report,
      id: report.id,
      status: ReportStatus.GENERATING,
      updatedAt: new Date().toISOString()
    };
    await db.updateItem('custom-reports', updatedReport);

    // Simulate report generation (in production, this would be actual report generation)
    setTimeout(async () => {
      try {
        const reportData = await generateReportData(report);
        const downloadUrl = await uploadReportToStorage(report, reportData);

        const completedReport = {
          ...updatedReport,
          id: report.id,
          status: ReportStatus.COMPLETED,
          results: {
            downloadUrl,
            fileSize: reportData.length,
            recordCount: reportData.recordCount || 0,
            generatedAt: new Date().toISOString(),
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
          },
          updatedAt: new Date().toISOString()
        };

        await db.updateItem('custom-reports', completedReport);

        logger.info('Report generated successfully', { reportId: report.id });

      } catch (error) {
        const failedReport = {
          ...updatedReport,
          id: report.id,
          status: ReportStatus.FAILED,
          updatedAt: new Date().toISOString()
        };
        await db.updateItem('custom-reports', failedReport);

        logger.error('Report generation failed', { reportId: report.id, error });
      }
    }, 5000); // 5 second delay for demo

  } catch (error) {
    logger.error('Failed to start report generation', { reportId: report.id, error });
  }
}

async function generateReportData(report: CustomReport): Promise<any> {
  // Simplified report data generation
  const mockData = {
    reportId: report.id,
    reportName: report.name,
    generatedAt: new Date().toISOString(),
    data: `Mock report data for ${report.type}`,
    recordCount: Math.floor(Math.random() * 1000) + 100
  };

  return Buffer.from(JSON.stringify(mockData, null, 2));
}

async function uploadReportToStorage(report: CustomReport, data: Buffer): Promise<string> {
  try {
    const blobServiceClient = new BlobServiceClient(process.env.AZURE_STORAGE_CONNECTION_STRING || "");
    const containerClient = blobServiceClient.getContainerClient("reports");

    const fileName = `${report.id}-${Date.now()}.${report.format.toLowerCase()}`;
    const blobClient = containerClient.getBlobClient(fileName);

    await blobClient.getBlockBlobClient().upload(data, data.length);

    return blobClient.url;
  } catch (error) {
    logger.error('Failed to upload report to storage', { reportId: report.id, error });
    throw error;
  }
}

// Register functions
app.http('custom-report-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'reports/custom',
  handler: createCustomReport
});

app.http('custom-report-status', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'reports/custom/{reportId}/status',
  handler: getReportStatus
});

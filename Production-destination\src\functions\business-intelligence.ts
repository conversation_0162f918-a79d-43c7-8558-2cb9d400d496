/**
 * Business Intelligence Function
 * Handles advanced business intelligence and reporting
 * Migrated from old-arch/src/analytics-service/business-intelligence/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';

// Business Intelligence types and enums
enum ReportType {
  EXECUTIVE_DASHBOARD = 'EXECUTIVE_DASHBOARD',
  OPERATIONAL_METRICS = 'OPERATIONAL_METRICS',
  USER_ANALYTICS = 'USER_ANALYTICS',
  DOCUMENT_INSIGHTS = 'DOCUMENT_INSIGHTS',
  WORKFLOW_PERFORMANCE = 'WORKFLOW_PERFORMANCE',
  FINANCIAL_OVERVIEW = 'FINANCIAL_OVERVIEW',
  GROWTH_ANALYSIS = 'GROWTH_ANALYSIS',
  CUSTOM_REPORT = 'CUSTOM_REPORT'
}

enum TimeGranularity {
  HOURLY = 'HOURLY',
  DAILY = 'DAILY',
  WEEKLY = 'WEEKLY',
  MONTHLY = 'MONTHLY',
  QUARTERLY = 'QUARTERLY',
  YEARLY = 'YEARLY'
}

enum VisualizationType {
  LINE_CHART = 'LINE_CHART',
  BAR_CHART = 'BAR_CHART',
  PIE_CHART = 'PIE_CHART',
  AREA_CHART = 'AREA_CHART',
  SCATTER_PLOT = 'SCATTER_PLOT',
  HEATMAP = 'HEATMAP',
  TABLE = 'TABLE',
  KPI_CARD = 'KPI_CARD'
}

// Validation schemas
const generateReportSchema = Joi.object({
  reportType: Joi.string().valid(...Object.values(ReportType)).required(),
  organizationId: Joi.string().uuid().required(),
  timeRange: Joi.object({
    startDate: Joi.string().isoDate().required(),
    endDate: Joi.string().isoDate().required(),
    granularity: Joi.string().valid(...Object.values(TimeGranularity)).default(TimeGranularity.DAILY)
  }).required(),
  filters: Joi.object({
    departments: Joi.array().items(Joi.string()).optional(),
    userIds: Joi.array().items(Joi.string().uuid()).optional(),
    documentTypes: Joi.array().items(Joi.string()).optional(),
    workflowIds: Joi.array().items(Joi.string().uuid()).optional(),
    tags: Joi.array().items(Joi.string()).optional()
  }).optional(),
  visualizations: Joi.array().items(Joi.object({
    type: Joi.string().valid(...Object.values(VisualizationType)).required(),
    title: Joi.string().required(),
    metrics: Joi.array().items(Joi.string()).required(),
    dimensions: Joi.array().items(Joi.string()).optional(),
    options: Joi.object().optional()
  })).optional(),
  options: Joi.object({
    includeComparisons: Joi.boolean().default(true),
    includeTrends: Joi.boolean().default(true),
    includeInsights: Joi.boolean().default(true),
    exportFormat: Joi.string().valid('json', 'pdf', 'excel').default('json')
  }).optional()
});

interface GenerateReportRequest {
  reportType: ReportType;
  organizationId: string;
  timeRange: {
    startDate: string;
    endDate: string;
    granularity?: TimeGranularity;
  };
  filters?: {
    departments?: string[];
    userIds?: string[];
    documentTypes?: string[];
    workflowIds?: string[];
    tags?: string[];
  };
  visualizations?: Array<{
    type: VisualizationType;
    title: string;
    metrics: string[];
    dimensions?: string[];
    options?: any;
  }>;
  options?: {
    includeComparisons?: boolean;
    includeTrends?: boolean;
    includeInsights?: boolean;
    exportFormat?: string;
  };
}

interface BusinessIntelligenceReport {
  id: string;
  reportType: ReportType;
  organizationId: string;
  title: string;
  summary: {
    keyMetrics: Array<{
      name: string;
      value: number;
      unit: string;
      change: number;
      changeType: 'increase' | 'decrease' | 'stable';
      trend: 'up' | 'down' | 'stable';
    }>;
    insights: Array<{
      type: string;
      message: string;
      impact: 'high' | 'medium' | 'low';
      actionable: boolean;
    }>;
  };
  sections: Array<{
    title: string;
    type: string;
    data: any;
    visualizations: Array<{
      type: VisualizationType;
      title: string;
      data: any;
      config: any;
    }>;
  }>;
  metadata: {
    generatedAt: string;
    timeRange: any;
    filters: any;
    dataPoints: number;
    processingTime: number;
  };
}

/**
 * Generate business intelligence report handler
 */
export async function generateBIReport(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;
  const startTime = Date.now();
  logger.info("Generate BI report started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = generateReportSchema.validate(body);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const reportRequest: GenerateReportRequest = value;

    // Check organization access
    const hasAccess = await checkOrganizationAccess(reportRequest.organizationId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Check BI permissions
    const hasBIAccess = await checkBIAccess(user, reportRequest.organizationId);
    if (!hasBIAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to business intelligence" }
      }, request);
    }

    // Generate report
    const report = await generateReport(reportRequest, user.id);

    // Store report
    await storeReport(report);

    const duration = Date.now() - startTime;

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "bi_report_generated",
      userId: user.id,
      organizationId: reportRequest.organizationId,
      timestamp: new Date().toISOString(),
      details: {
        reportId: report.id,
        reportType: reportRequest.reportType,
        timeRange: reportRequest.timeRange,
        sectionCount: report.sections.length,
        processingTime: duration
      },
      tenantId: user.tenantId
    });

    logger.info("BI report generated successfully", {
      correlationId,
      reportId: report.id,
      reportType: reportRequest.reportType,
      sectionCount: report.sections.length,
      duration,
      generatedBy: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        report,
        processingTime: duration,
        message: "Business intelligence report generated successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Generate BI report failed", { correlationId, error: errorMessage });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Get BI dashboard handler
 */
export async function getBIDashboard(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;
  logger.info("Get BI dashboard started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Parse query parameters
    const url = new URL(request.url);
    const organizationId = url.searchParams.get('organizationId');
    const dashboardType = url.searchParams.get('type') || 'executive';

    if (!organizationId) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Organization ID is required" }
      }, request);
    }

    // Check organization access
    const hasAccess = await checkOrganizationAccess(organizationId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Get dashboard data
    const dashboard = await getDashboardData(organizationId, dashboardType, user.id);

    logger.info("BI dashboard retrieved successfully", {
      correlationId,
      organizationId,
      dashboardType,
      widgetCount: dashboard.widgets.length,
      requestedBy: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        dashboard,
        retrievedAt: new Date().toISOString()
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get BI dashboard failed", { correlationId, error: errorMessage });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkOrganizationAccess(organizationId: string, userId: string): Promise<boolean> {
  try {
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
    return memberships.length > 0;
  } catch (error) {
    logger.error('Failed to check organization access', { error, organizationId, userId });
    return false;
  }
}

async function checkBIAccess(user: any, organizationId: string): Promise<boolean> {
  try {
    // Check if user has admin or BI role
    if (user.roles?.includes('admin') || user.roles?.includes('bi_admin')) {
      return true;
    }

    // Check organization-level permissions
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, user.id, 'ACTIVE']);
    
    if (memberships.length > 0) {
      const membership = memberships[0] as any;
      return membership.role === 'OWNER' || membership.role === 'ADMIN';
    }

    return false;
  } catch (error) {
    logger.error('Failed to check BI access', { error, userId: user.id, organizationId });
    return false;
  }
}

async function generateReport(reportRequest: GenerateReportRequest, userId: string): Promise<BusinessIntelligenceReport> {
  const reportId = uuidv4();
  const now = new Date().toISOString();

  // Get data based on report type
  const reportData = await getReportData(reportRequest);

  // Generate key metrics
  const keyMetrics = generateKeyMetrics(reportRequest.reportType, reportData);

  // Generate insights
  const insights = generateBusinessInsights(reportRequest.reportType, reportData);

  // Generate sections
  const sections = await generateReportSections(reportRequest, reportData);

  return {
    id: reportId,
    reportType: reportRequest.reportType,
    organizationId: reportRequest.organizationId,
    title: getReportTitle(reportRequest.reportType),
    summary: {
      keyMetrics,
      insights
    },
    sections,
    metadata: {
      generatedAt: now,
      timeRange: reportRequest.timeRange,
      filters: reportRequest.filters || {},
      dataPoints: reportData.totalDataPoints || 0,
      processingTime: 0 // Will be updated
    }
  };
}

function getReportTitle(reportType: ReportType): string {
  const titles: { [key in ReportType]: string } = {
    [ReportType.EXECUTIVE_DASHBOARD]: 'Executive Dashboard',
    [ReportType.OPERATIONAL_METRICS]: 'Operational Metrics Report',
    [ReportType.USER_ANALYTICS]: 'User Analytics Report',
    [ReportType.DOCUMENT_INSIGHTS]: 'Document Insights Report',
    [ReportType.WORKFLOW_PERFORMANCE]: 'Workflow Performance Report',
    [ReportType.FINANCIAL_OVERVIEW]: 'Financial Overview Report',
    [ReportType.GROWTH_ANALYSIS]: 'Growth Analysis Report',
    [ReportType.CUSTOM_REPORT]: 'Custom Business Report'
  };

  return titles[reportType];
}

async function getReportData(reportRequest: GenerateReportRequest): Promise<any> {
  try {
    // This would fetch actual data from various sources
    // For now, return mock data structure
    return {
      totalDataPoints: Math.floor(Math.random() * 10000) + 1000,
      userMetrics: {
        totalUsers: Math.floor(Math.random() * 1000) + 100,
        activeUsers: Math.floor(Math.random() * 800) + 80,
        newUsers: Math.floor(Math.random() * 50) + 10
      },
      documentMetrics: {
        totalDocuments: Math.floor(Math.random() * 5000) + 500,
        documentsCreated: Math.floor(Math.random() * 100) + 20,
        documentsProcessed: Math.floor(Math.random() * 200) + 50
      },
      workflowMetrics: {
        totalWorkflows: Math.floor(Math.random() * 200) + 20,
        workflowsExecuted: Math.floor(Math.random() * 500) + 100,
        averageExecutionTime: Math.floor(Math.random() * 300) + 60
      },
      systemMetrics: {
        uptime: 99.9,
        responseTime: Math.floor(Math.random() * 200) + 100,
        errorRate: Math.random() * 2
      }
    };

  } catch (error) {
    logger.error('Failed to get report data', { error, reportRequest });
    return { totalDataPoints: 0 };
  }
}

function generateKeyMetrics(reportType: ReportType, data: any): any[] {
  const baseMetrics = [
    {
      name: 'Total Users',
      value: data.userMetrics?.totalUsers || 0,
      unit: 'users',
      change: Math.floor(Math.random() * 20) - 10,
      changeType: 'increase' as const,
      trend: 'up' as const
    },
    {
      name: 'Active Documents',
      value: data.documentMetrics?.totalDocuments || 0,
      unit: 'documents',
      change: Math.floor(Math.random() * 15) - 5,
      changeType: 'increase' as const,
      trend: 'up' as const
    },
    {
      name: 'Workflow Executions',
      value: data.workflowMetrics?.workflowsExecuted || 0,
      unit: 'executions',
      change: Math.floor(Math.random() * 25) - 10,
      changeType: 'increase' as const,
      trend: 'stable' as const
    },
    {
      name: 'System Uptime',
      value: data.systemMetrics?.uptime || 99.9,
      unit: '%',
      change: 0.1,
      changeType: 'stable' as const,
      trend: 'stable' as const
    }
  ];

  return baseMetrics;
}

function generateBusinessInsights(reportType: ReportType, data: any): any[] {
  const insights = [
    {
      type: 'performance',
      message: 'User engagement has increased by 15% compared to last period',
      impact: 'high' as const,
      actionable: true
    },
    {
      type: 'efficiency',
      message: 'Document processing time has improved by 20% with new AI features',
      impact: 'medium' as const,
      actionable: false
    },
    {
      type: 'growth',
      message: 'New user acquisition is trending upward with 25% month-over-month growth',
      impact: 'high' as const,
      actionable: true
    },
    {
      type: 'optimization',
      message: 'Workflow automation has reduced manual processing by 40%',
      impact: 'medium' as const,
      actionable: false
    }
  ];

  return insights;
}

async function generateReportSections(reportRequest: GenerateReportRequest, data: any): Promise<any[]> {
  const sections = [];

  // User Analytics Section
  sections.push({
    title: 'User Analytics',
    type: 'user_analytics',
    data: data.userMetrics,
    visualizations: [
      {
        type: VisualizationType.LINE_CHART,
        title: 'User Growth Trend',
        data: generateTimeSeriesData(30),
        config: { xAxis: 'date', yAxis: 'users' }
      },
      {
        type: VisualizationType.PIE_CHART,
        title: 'User Activity Distribution',
        data: [
          { label: 'Active', value: 70 },
          { label: 'Inactive', value: 20 },
          { label: 'New', value: 10 }
        ],
        config: {}
      }
    ]
  });

  // Document Analytics Section
  sections.push({
    title: 'Document Analytics',
    type: 'document_analytics',
    data: data.documentMetrics,
    visualizations: [
      {
        type: VisualizationType.BAR_CHART,
        title: 'Document Types Distribution',
        data: [
          { label: 'PDF', value: 45 },
          { label: 'Word', value: 30 },
          { label: 'Excel', value: 15 },
          { label: 'Other', value: 10 }
        ],
        config: {}
      },
      {
        type: VisualizationType.AREA_CHART,
        title: 'Document Processing Volume',
        data: generateTimeSeriesData(30),
        config: { xAxis: 'date', yAxis: 'documents' }
      }
    ]
  });

  // Workflow Performance Section
  sections.push({
    title: 'Workflow Performance',
    type: 'workflow_performance',
    data: data.workflowMetrics,
    visualizations: [
      {
        type: VisualizationType.LINE_CHART,
        title: 'Workflow Execution Trends',
        data: generateTimeSeriesData(30),
        config: { xAxis: 'date', yAxis: 'executions' }
      },
      {
        type: VisualizationType.KPI_CARD,
        title: 'Average Execution Time',
        data: { value: data.workflowMetrics?.averageExecutionTime || 120, unit: 'seconds' },
        config: {}
      }
    ]
  });

  return sections;
}

function generateTimeSeriesData(days: number): any[] {
  const data = [];
  const now = new Date();
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
    data.push({
      date: date.toISOString().split('T')[0],
      value: Math.floor(Math.random() * 100) + 50
    });
  }
  
  return data;
}

async function storeReport(report: BusinessIntelligenceReport): Promise<void> {
  try {
    await db.createItem('bi-reports', report);
    
    // Cache report for quick access
    const cacheKey = `bi_report:${report.id}`;
    await redis.setex(cacheKey, 3600, JSON.stringify(report)); // 1 hour
    
  } catch (error) {
    logger.error('Failed to store BI report', { error, reportId: report.id });
  }
}

async function getDashboardData(organizationId: string, dashboardType: string, userId: string): Promise<any> {
  try {
    // Get cached dashboard data
    const cacheKey = `dashboard:${organizationId}:${dashboardType}`;
    const cached = await redis.get(cacheKey);
    
    if (cached) {
      return JSON.parse(cached);
    }

    // Generate dashboard data
    const dashboard = {
      id: uuidv4(),
      type: dashboardType,
      organizationId,
      widgets: [
        {
          id: uuidv4(),
          type: 'kpi',
          title: 'Total Users',
          value: Math.floor(Math.random() * 1000) + 100,
          change: Math.floor(Math.random() * 20) - 10,
          trend: 'up'
        },
        {
          id: uuidv4(),
          type: 'chart',
          title: 'Document Activity',
          chartType: 'line',
          data: generateTimeSeriesData(7)
        },
        {
          id: uuidv4(),
          type: 'table',
          title: 'Recent Activities',
          data: [
            { user: 'John Doe', action: 'Document Created', time: '2 hours ago' },
            { user: 'Jane Smith', action: 'Workflow Executed', time: '3 hours ago' },
            { user: 'Bob Johnson', action: 'Document Shared', time: '5 hours ago' }
          ]
        }
      ],
      lastUpdated: new Date().toISOString()
    };

    // Cache for 15 minutes
    await redis.setex(cacheKey, 900, JSON.stringify(dashboard));
    
    return dashboard;

  } catch (error) {
    logger.error('Failed to get dashboard data', { error, organizationId, dashboardType });
    return {
      id: uuidv4(),
      type: dashboardType,
      organizationId,
      widgets: [],
      lastUpdated: new Date().toISOString()
    };
  }
}

// Register functions
app.http('bi-report-generate', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'analytics/bi/reports',
  handler: generateBIReport
});

app.http('bi-dashboard-get', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'analytics/bi/dashboard',
  handler: getBIDashboard
});

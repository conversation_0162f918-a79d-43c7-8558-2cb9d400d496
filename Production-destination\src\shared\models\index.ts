/**
 * Shared Models Index
 * Exports all shared models and interfaces
 */

// Document models
export * from './document';

// User models
export * from './user';

// Workflow models
export * from './workflow';

// Organization models
export * from './organization';

// Common interfaces and types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
  correlationId?: string;
}

export interface PaginatedResponse<T = any> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
  totalPages: number;
}

export interface SearchFilters {
  [key: string]: any;
}

export interface SortOptions {
  field: string;
  direction: 'asc' | 'desc';
}

export interface PaginationOptions {
  page: number;
  limit: number;
}

export interface DateRange {
  start: string;
  end: string;
}

export interface Notification {
  id: string;
  userId: string;
  type: string;
  title: string;
  message: string;
  resourceType?: string;
  resourceId?: string;
  organizationId?: string;
  projectId?: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  status: 'unread' | 'read' | 'archived';
  createdAt: string;
  readAt?: string;
  archivedAt?: string;
  metadata?: any;
  tenantId: string;
}

export interface Activity {
  id: string;
  type: string;
  userId: string;
  userName?: string;
  organizationId?: string;
  projectId?: string;
  resourceType?: string;
  resourceId?: string;
  timestamp: string;
  details: any;
  ipAddress?: string;
  userAgent?: string;
  tenantId: string;
}

export interface AuditLog {
  id: string;
  action: string;
  resourceType: string;
  resourceId: string;
  userId: string;
  userName: string;
  organizationId?: string;
  projectId?: string;
  timestamp: string;
  changes?: {
    field: string;
    oldValue: any;
    newValue: any;
  }[];
  metadata?: any;
  ipAddress?: string;
  userAgent?: string;
  tenantId: string;
}

export interface Permission {
  id: string;
  name: string;
  description: string;
  resourceType: string;
  actions: string[];
  conditions?: PermissionCondition[];
  isSystemPermission: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface PermissionCondition {
  field: string;
  operator: string;
  value: any;
}

export interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  organizationId?: string;
  projectId?: string;
  isSystemRole: boolean;
  createdBy: string;
  createdAt: string;
  updatedBy: string;
  updatedAt: string;
  tenantId: string;
}

export interface Template {
  id: string;
  name: string;
  description?: string;
  type: 'document' | 'workflow' | 'project' | 'organization';
  category: string;
  content: any;
  isPublic: boolean;
  createdBy: string;
  createdAt: string;
  updatedBy: string;
  updatedAt: string;
  version: string;
  tags?: string[];
  usageCount: number;
  rating?: number;
  organizationId?: string;
  tenantId: string;
}

export interface Integration {
  id: string;
  name: string;
  type: string;
  provider: string;
  configuration: any;
  status: 'connected' | 'disconnected' | 'error' | 'pending';
  organizationId: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  lastSyncAt?: string;
  syncStatus?: 'success' | 'error' | 'in_progress';
  errorMessage?: string;
  tenantId: string;
}

export interface Webhook {
  id: string;
  name: string;
  url: string;
  events: string[];
  secret: string;
  enabled: boolean;
  organizationId: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  lastTriggeredAt?: string;
  successCount: number;
  failureCount: number;
  tenantId: string;
}

export interface ApiKey {
  id: string;
  name: string;
  keyHash: string;
  keyPrefix: string;
  permissions: string[];
  organizationId: string;
  createdBy: string;
  createdAt: string;
  lastUsedAt?: string;
  expiresAt?: string;
  enabled: boolean;
  usageCount: number;
  tenantId: string;
}

export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  services: ServiceHealth[];
  metrics: SystemMetrics;
}

export interface ServiceHealth {
  name: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime?: number;
  lastChecked: string;
  error?: string;
}

export interface SystemMetrics {
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
  activeConnections: number;
  requestsPerMinute: number;
  errorRate: number;
}

export interface SearchResult<T = any> {
  items: T[];
  total: number;
  facets?: {
    [key: string]: { value: string; count: number }[];
  };
  suggestions?: string[];
  processingTime: number;
  query: string;
}

export interface AnalyticsData {
  metric: string;
  value: number;
  timestamp: string;
  dimensions?: { [key: string]: string };
}

export interface TimeSeriesPoint {
  timestamp: string;
  value: number;
  label?: string;
}

export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string;
    borderColor?: string;
  }[];
}

export interface ReportData {
  id: string;
  name: string;
  type: string;
  data: any;
  generatedAt: string;
  generatedBy: string;
  organizationId: string;
  filters?: any;
  format: 'json' | 'csv' | 'pdf' | 'excel';
  tenantId: string;
}

// Error types
export interface ApiError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
  correlationId?: string;
}

export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

// Request/Response types
export interface BaseRequest {
  organizationId?: string;
  projectId?: string;
  userId?: string;
}

export interface BaseResponse {
  success: boolean;
  message?: string;
  timestamp: string;
  correlationId?: string;
}

// Event types
export interface DomainEvent {
  id: string;
  type: string;
  aggregateId: string;
  aggregateType: string;
  version: number;
  data: any;
  metadata?: any;
  timestamp: string;
  userId?: string;
  organizationId?: string;
  tenantId?: string;
}

export interface EventHandler {
  eventType: string;
  handle(event: DomainEvent): Promise<void>;
}

// Configuration types
export interface AppConfig {
  environment: string;
  version: string;
  features: { [key: string]: boolean };
  limits: { [key: string]: number };
  integrations: { [key: string]: any };
}

export interface TenantConfig {
  tenantId: string;
  settings: { [key: string]: any };
  features: { [key: string]: boolean };
  limits: { [key: string]: number };
  customizations: { [key: string]: any };
}

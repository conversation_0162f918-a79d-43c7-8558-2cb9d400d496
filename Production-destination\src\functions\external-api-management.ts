/**
 * External API Management Function
 * Handles external API connections, authentication, and management
 * Migrated from old-arch/src/integration-service/external-apis/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { eventService } from '../shared/services/event';

// API types and enums
enum ApiType {
  REST = 'REST',
  GRAPHQL = 'GRAPHQL',
  SOAP = 'SOAP',
  WEBHOOK = 'WEBHOOK'
}

enum AuthType {
  NONE = 'NONE',
  API_KEY = 'API_KEY',
  BEARER_TOKEN = 'BEARER_TOKEN',
  BASIC_AUTH = 'BASIC_AUTH',
  OAUTH2 = 'OAUTH2',
  CUSTOM = 'CUSTOM'
}

enum ApiStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  ERROR = 'ERROR',
  TESTING = 'TESTING'
}

// Validation schemas
const createApiConnectionSchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  description: Joi.string().max(500).optional(),
  type: Joi.string().valid(...Object.values(ApiType)).required(),
  baseUrl: Joi.string().uri().required(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional(),
  authentication: Joi.object({
    type: Joi.string().valid(...Object.values(AuthType)).required(),
    credentials: Joi.object({
      apiKey: Joi.string().optional(),
      token: Joi.string().optional(),
      username: Joi.string().optional(),
      password: Joi.string().optional(),
      clientId: Joi.string().optional(),
      clientSecret: Joi.string().optional(),
      scope: Joi.string().optional(),
      tokenUrl: Joi.string().uri().optional(),
      customHeaders: Joi.object().optional()
    }).optional()
  }).required(),
  configuration: Joi.object({
    timeout: Joi.number().min(1000).max(300000).default(30000),
    retryAttempts: Joi.number().min(0).max(5).default(3),
    retryDelay: Joi.number().min(100).max(10000).default(1000),
    rateLimit: Joi.object({
      requests: Joi.number().min(1).required(),
      window: Joi.number().min(1000).required()
    }).optional(),
    headers: Joi.object().optional(),
    queryParams: Joi.object().optional()
  }).optional(),
  endpoints: Joi.array().items(Joi.object({
    name: Joi.string().required(),
    path: Joi.string().required(),
    method: Joi.string().valid('GET', 'POST', 'PUT', 'DELETE', 'PATCH').required(),
    description: Joi.string().optional(),
    parameters: Joi.array().items(Joi.object({
      name: Joi.string().required(),
      type: Joi.string().valid('query', 'path', 'header', 'body').required(),
      required: Joi.boolean().default(false),
      description: Joi.string().optional()
    })).optional()
  })).optional()
});

const testApiConnectionSchema = Joi.object({
  connectionId: Joi.string().uuid().required(),
  endpoint: Joi.string().optional(),
  method: Joi.string().valid('GET', 'POST', 'PUT', 'DELETE', 'PATCH').default('GET'),
  parameters: Joi.object().optional()
});

interface CreateApiConnectionRequest {
  name: string;
  description?: string;
  type: ApiType;
  baseUrl: string;
  organizationId: string;
  projectId?: string;
  authentication: {
    type: AuthType;
    credentials?: {
      apiKey?: string;
      token?: string;
      username?: string;
      password?: string;
      clientId?: string;
      clientSecret?: string;
      scope?: string;
      tokenUrl?: string;
      customHeaders?: any;
    };
  };
  configuration?: {
    timeout?: number;
    retryAttempts?: number;
    retryDelay?: number;
    rateLimit?: {
      requests: number;
      window: number;
    };
    headers?: any;
    queryParams?: any;
  };
  endpoints?: Array<{
    name: string;
    path: string;
    method: string;
    description?: string;
    parameters?: Array<{
      name: string;
      type: string;
      required?: boolean;
      description?: string;
    }>;
  }>;
}

interface ApiConnection {
  id: string;
  name: string;
  description?: string;
  type: ApiType;
  baseUrl: string;
  status: ApiStatus;
  organizationId: string;
  projectId?: string;
  authentication: {
    type: AuthType;
    credentials?: any; // Encrypted in production
  };
  configuration: {
    timeout: number;
    retryAttempts: number;
    retryDelay: number;
    rateLimit?: any;
    headers?: any;
    queryParams?: any;
  };
  endpoints: any[];
  metrics: {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageResponseTime: number;
    lastUsed?: string;
  };
  createdBy: string;
  createdAt: string;
  updatedBy: string;
  updatedAt: string;
  tenantId: string;
}

/**
 * Create API connection handler
 */
export async function createApiConnection(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create API connection started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createApiConnectionSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const connectionRequest: CreateApiConnectionRequest = value;

    // Check organization access
    const hasAccess = await checkOrganizationAccess(connectionRequest.organizationId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Check API connection limits
    const canCreate = await checkApiConnectionLimits(connectionRequest.organizationId);
    if (!canCreate.allowed) {
      return addCorsHeaders({
        status: 429,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: canCreate.reason }
      }, request);
    }

    // Create API connection
    const connectionId = uuidv4();
    const now = new Date().toISOString();

    const apiConnection: ApiConnection = {
      id: connectionId,
      name: connectionRequest.name,
      description: connectionRequest.description,
      type: connectionRequest.type,
      baseUrl: connectionRequest.baseUrl,
      status: ApiStatus.TESTING,
      organizationId: connectionRequest.organizationId,
      projectId: connectionRequest.projectId,
      authentication: {
        type: connectionRequest.authentication.type,
        credentials: await encryptCredentials(connectionRequest.authentication.credentials)
      },
      configuration: {
        timeout: 30000,
        retryAttempts: 3,
        retryDelay: 1000,
        ...connectionRequest.configuration
      },
      endpoints: connectionRequest.endpoints || [],
      metrics: {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        averageResponseTime: 0
      },
      createdBy: user.id,
      createdAt: now,
      updatedBy: user.id,
      updatedAt: now,
      tenantId: user.tenantId || user.id
    };

    await db.createItem('api-connections', apiConnection);

    // Test the connection
    const testResult = await testApiConnectionInternal(apiConnection);

    // Update status based on test result
    const updatedConnection = {
      ...apiConnection,
      id: connectionId,
      status: testResult.success ? ApiStatus.ACTIVE : ApiStatus.ERROR,
      updatedAt: new Date().toISOString()
    };
    await db.updateItem('api-connections', updatedConnection);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "api_connection_created",
      userId: user.id,
      organizationId: connectionRequest.organizationId,
      projectId: connectionRequest.projectId,
      timestamp: now,
      details: {
        connectionId,
        connectionName: connectionRequest.name,
        apiType: connectionRequest.type,
        baseUrl: connectionRequest.baseUrl,
        authType: connectionRequest.authentication.type,
        endpointCount: connectionRequest.endpoints?.length || 0,
        testResult: testResult.success
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'ApiConnectionCreated',
      aggregateId: connectionId,
      aggregateType: 'ApiConnection',
      version: 1,
      data: {
        connection: {
          ...updatedConnection,
          authentication: { type: updatedConnection.authentication.type } // Don't include credentials in event
        },
        testResult,
        createdBy: user.id
      },
      userId: user.id,
      organizationId: connectionRequest.organizationId,
      tenantId: user.tenantId
    });

    logger.info("API connection created successfully", {
      correlationId,
      connectionId,
      connectionName: connectionRequest.name,
      apiType: connectionRequest.type,
      status: updatedConnection.status,
      createdBy: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: connectionId,
        name: connectionRequest.name,
        type: connectionRequest.type,
        status: updatedConnection.status,
        baseUrl: connectionRequest.baseUrl,
        endpointCount: connectionRequest.endpoints?.length || 0,
        testResult,
        createdAt: now,
        message: "API connection created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create API connection failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Test API connection handler
 */
export async function testApiConnection(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Test API connection started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = testApiConnectionSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const testRequest = value;

    // Get API connection
    const connection = await db.readItem('api-connections', testRequest.connectionId, testRequest.connectionId);
    if (!connection) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "API connection not found" }
      }, request);
    }

    const connectionData = connection as any;

    // Check access
    const hasAccess = await checkOrganizationAccess(connectionData.organizationId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to API connection" }
      }, request);
    }

    // Test the connection
    const testResult = await testApiConnectionInternal(connectionData, testRequest.endpoint, testRequest.method, testRequest.parameters);

    // Update connection metrics
    await updateConnectionMetrics(testRequest.connectionId, testResult);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "api_connection_tested",
      userId: user.id,
      organizationId: connectionData.organizationId,
      projectId: connectionData.projectId,
      timestamp: new Date().toISOString(),
      details: {
        connectionId: testRequest.connectionId,
        connectionName: connectionData.name,
        endpoint: testRequest.endpoint,
        method: testRequest.method,
        success: testResult.success,
        responseTime: testResult.responseTime,
        statusCode: testResult.statusCode
      },
      tenantId: user.tenantId
    });

    logger.info("API connection tested successfully", {
      correlationId,
      connectionId: testRequest.connectionId,
      connectionName: connectionData.name,
      success: testResult.success,
      responseTime: testResult.responseTime,
      testedBy: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        connectionId: testRequest.connectionId,
        connectionName: connectionData.name,
        testResult,
        testedAt: new Date().toISOString(),
        message: testResult.success ? "API connection test successful" : "API connection test failed"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Test API connection failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkOrganizationAccess(organizationId: string, userId: string): Promise<boolean> {
  try {
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
    return memberships.length > 0;
  } catch (error) {
    logger.error('Failed to check organization access', { error, organizationId, userId });
    return false;
  }
}

async function checkApiConnectionLimits(organizationId: string): Promise<{ allowed: boolean; reason?: string }> {
  try {
    // Get organization to check tier
    const organization = await db.readItem('organizations', organizationId, organizationId);
    if (!organization) {
      return { allowed: false, reason: 'Organization not found' };
    }

    const orgData = organization as any;
    const tier = orgData.tier || 'FREE';

    // Define tier limits
    const limits: { [key: string]: { maxConnections: number } } = {
      'FREE': { maxConnections: 3 },
      'PROFESSIONAL': { maxConnections: 25 },
      'ENTERPRISE': { maxConnections: -1 } // Unlimited
    };

    const limit = limits[tier] || limits['FREE'];

    if (limit.maxConnections === -1) {
      return { allowed: true };
    }

    // Check current connection count
    const connectionCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.status != @inactive';
    const countResult = await db.queryItems('api-connections', connectionCountQuery, [organizationId, ApiStatus.INACTIVE]);
    const currentCount = Number(countResult[0]) || 0;

    if (currentCount >= limit.maxConnections) {
      return {
        allowed: false,
        reason: `API connection limit reached (${limit.maxConnections})`
      };
    }

    return { allowed: true };

  } catch (error) {
    logger.error('Failed to check API connection limits', { error, organizationId });
    return { allowed: false, reason: 'Failed to check limits' };
  }
}

async function encryptCredentials(credentials: any): Promise<any> {
  // In production, this would encrypt sensitive credentials
  // For now, we'll just return them as-is (not recommended for production)
  return credentials;
}

async function testApiConnectionInternal(connection: any, endpoint?: string, method?: string, parameters?: any): Promise<any> {
  const startTime = Date.now();

  try {
    // Build test URL
    const testUrl = endpoint ? `${connection.baseUrl}${endpoint}` : connection.baseUrl;
    const testMethod = method || 'GET';

    // Prepare headers
    const headers: any = {
      'Content-Type': 'application/json',
      'User-Agent': 'DocuContext-API-Client/1.0',
      ...connection.configuration.headers
    };

    // Add authentication headers
    if (connection.authentication.type === AuthType.API_KEY && connection.authentication.credentials?.apiKey) {
      headers['X-API-Key'] = connection.authentication.credentials.apiKey;
    } else if (connection.authentication.type === AuthType.BEARER_TOKEN && connection.authentication.credentials?.token) {
      headers['Authorization'] = `Bearer ${connection.authentication.credentials.token}`;
    } else if (connection.authentication.type === AuthType.BASIC_AUTH && connection.authentication.credentials?.username) {
      const auth = Buffer.from(`${connection.authentication.credentials.username}:${connection.authentication.credentials.password || ''}`).toString('base64');
      headers['Authorization'] = `Basic ${auth}`;
    }

    // Make test request (simplified - in production, use proper HTTP client)
    const response = await fetch(testUrl, {
      method: testMethod,
      headers,
      body: testMethod !== 'GET' && parameters ? JSON.stringify(parameters) : undefined,
      signal: AbortSignal.timeout(connection.configuration.timeout || 30000)
    });

    const responseTime = Date.now() - startTime;
    const responseData = await response.text();

    return {
      success: response.ok,
      statusCode: response.status,
      statusText: response.statusText,
      responseTime,
      responseSize: responseData.length,
      headers: Object.fromEntries(response.headers.entries()),
      data: responseData.substring(0, 1000), // First 1000 chars
      error: response.ok ? null : `HTTP ${response.status}: ${response.statusText}`
    };

  } catch (error) {
    const responseTime = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : String(error);

    return {
      success: false,
      statusCode: 0,
      statusText: 'Connection Failed',
      responseTime,
      responseSize: 0,
      headers: {},
      data: null,
      error: errorMessage
    };
  }
}

async function updateConnectionMetrics(connectionId: string, testResult: any): Promise<void> {
  try {
    const connection = await db.readItem('api-connections', connectionId, connectionId);
    if (!connection) return;

    const connectionData = connection as any;
    const metrics = connectionData.metrics || {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0
    };

    // Update metrics
    metrics.totalRequests += 1;
    if (testResult.success) {
      metrics.successfulRequests += 1;
    } else {
      metrics.failedRequests += 1;
    }

    // Update average response time
    metrics.averageResponseTime = Math.round(
      (metrics.averageResponseTime * (metrics.totalRequests - 1) + testResult.responseTime) / metrics.totalRequests
    );

    metrics.lastUsed = new Date().toISOString();

    // Update connection
    const updatedConnection = {
      ...connectionData,
      id: connectionId,
      metrics,
      updatedAt: new Date().toISOString()
    };

    await db.updateItem('api-connections', updatedConnection);

  } catch (error) {
    logger.error('Failed to update connection metrics', { error, connectionId });
  }
}

// Register functions
app.http('api-connection-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'integrations/api-connections',
  handler: createApiConnection
});

app.http('api-connection-test', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'integrations/api-connections/test',
  handler: testApiConnection
});

/**
 * User Models and Interfaces
 * Defines the structure for user-related data models
 */

export interface User {
  id: string;
  email: string;
  passwordHash?: string;
  firstName: string;
  lastName: string;
  displayName: string;
  avatar?: string;
  status: UserStatus;
  systemRoles: SystemRole[];
  roles: string[];
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
  organizationIds: string[];
  defaultOrganizationId?: string;
  tenantId?: string;
  tenantIds: string[];
  preferences: UserPreferences;
  profile?: UserProfile;
  settings?: UserSettings;
  metadata?: UserMetadata;
}

export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED',
  PENDING_VERIFICATION = 'PENDING_VERIFICATION',
  DELETED = 'DELETED'
}

export enum SystemRole {
  SUPER_ADMIN = 'SUPER_ADMIN',
  ADMIN = 'ADMIN',
  USER = 'USER',
  GUEST = 'GUEST'
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  timezone: string;
  dateFormat: string;
  timeFormat?: '12h' | '24h';
  notifications: NotificationPreferences;
  privacy?: PrivacySettings;
  accessibility?: AccessibilitySettings;
}

export interface NotificationPreferences {
  email: boolean;
  inApp: boolean;
  push?: boolean;
  documentUploaded: boolean;
  documentProcessed: boolean;
  commentAdded: boolean;
  mentionedInComment: boolean;
  projectInvitation: boolean;
  organizationInvitation: boolean;
  workflowAssigned: boolean;
  workflowCompleted: boolean;
  systemUpdates: boolean;
  securityAlerts: boolean;
  marketingEmails?: boolean;
}

export interface PrivacySettings {
  profileVisibility: 'public' | 'organization' | 'private';
  showOnlineStatus: boolean;
  allowDirectMessages: boolean;
  shareActivityStatus: boolean;
  dataProcessingConsent: boolean;
  analyticsConsent: boolean;
}

export interface AccessibilitySettings {
  highContrast: boolean;
  largeText: boolean;
  screenReader: boolean;
  keyboardNavigation: boolean;
  reducedMotion: boolean;
}

export interface UserProfile {
  bio?: string;
  title?: string;
  department?: string;
  location?: string;
  phone?: string;
  website?: string;
  socialLinks?: {
    linkedin?: string;
    twitter?: string;
    github?: string;
  };
  skills?: string[];
  interests?: string[];
  expertise?: string[];
}

export interface UserSettings {
  defaultProjectView: 'list' | 'grid' | 'kanban';
  defaultDocumentView: 'list' | 'grid' | 'preview';
  autoSaveInterval: number; // in seconds
  defaultSharePermissions: string[];
  workflowNotificationFrequency: 'immediate' | 'hourly' | 'daily';
  documentRetentionDays?: number;
  twoFactorEnabled: boolean;
  sessionTimeout: number; // in minutes
}

export interface UserMetadata {
  lastActiveAt?: string;
  loginCount: number;
  documentsCreated: number;
  workflowsCompleted: number;
  collaborationsCount: number;
  storageUsed: number; // in bytes
  apiCallsCount?: number;
  lastPasswordChange?: string;
  accountCreationSource?: string;
  referralCode?: string;
  [key: string]: any;
}

export interface UserSession {
  id: string;
  userId: string;
  sessionToken: string;
  accessToken?: string;
  refreshToken?: string;
  ipAddress: string;
  userAgent: string;
  createdAt: string;
  expiresAt: string;
  lastActiveAt: string;
  isActive: boolean;
  deviceInfo: {
    type: string;
    os: string;
    browser: string;
    location?: string;
  };
}

export interface UserActivity {
  id: string;
  userId: string;
  type: ActivityType;
  resourceType?: string;
  resourceId?: string;
  organizationId?: string;
  projectId?: string;
  timestamp: string;
  details: any;
  ipAddress?: string;
  userAgent?: string;
  tenantId?: string;
}

export enum ActivityType {
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  DOCUMENT_CREATED = 'DOCUMENT_CREATED',
  DOCUMENT_UPDATED = 'DOCUMENT_UPDATED',
  DOCUMENT_DELETED = 'DOCUMENT_DELETED',
  DOCUMENT_SHARED = 'DOCUMENT_SHARED',
  DOCUMENT_SIGNED = 'DOCUMENT_SIGNED',
  WORKFLOW_CREATED = 'WORKFLOW_CREATED',
  WORKFLOW_STARTED = 'WORKFLOW_STARTED',
  WORKFLOW_COMPLETED = 'WORKFLOW_COMPLETED',
  PROJECT_CREATED = 'PROJECT_CREATED',
  PROJECT_JOINED = 'PROJECT_JOINED',
  ORGANIZATION_JOINED = 'ORGANIZATION_JOINED',
  COMMENT_ADDED = 'COMMENT_ADDED',
  SEARCH_PERFORMED = 'SEARCH_PERFORMED',
  SETTINGS_UPDATED = 'SETTINGS_UPDATED',
  PASSWORD_CHANGED = 'PASSWORD_CHANGED',
  TWO_FACTOR_ENABLED = 'TWO_FACTOR_ENABLED',
  API_KEY_CREATED = 'API_KEY_CREATED'
}

export interface UserPermission {
  id: string;
  userId: string;
  resourceType: string;
  resourceId: string;
  permissions: string[];
  grantedBy: string;
  grantedAt: string;
  expiresAt?: string;
  organizationId?: string;
  projectId?: string;
  tenantId: string;
}

export interface UserRole {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  organizationId?: string;
  projectId?: string;
  isSystemRole: boolean;
  createdBy: string;
  createdAt: string;
  updatedBy: string;
  updatedAt: string;
  tenantId: string;
}

export interface UserInvitation {
  id: string;
  email: string;
  organizationId?: string;
  projectId?: string;
  role: string;
  permissions?: string[];
  invitedBy: string;
  invitedAt: string;
  expiresAt: string;
  status: 'PENDING' | 'ACCEPTED' | 'DECLINED' | 'EXPIRED';
  message?: string;
  acceptedAt?: string;
  tenantId: string;
}

export interface PersonalizationSettings {
  id: string;
  userId: string;
  personalizationStrength: number; // 0-1
  defaultSearchMode: 'basic' | 'advanced' | 'semantic' | 'hybrid';
  preferredSortField: string;
  preferredSortDirection: 'asc' | 'desc';
  defaultResultsPerPage: number;
  preferredCategories: string[];
  preferredContentTypes: string[];
  preferredAuthors: <AUTHORS>
  preferredTags: string[];
  useFacetedSearch: boolean;
  defaultFacets: string[];
  clusterResults: boolean;
  maxClusters: number;
  minClusterSize: number;
  additionalSettings: any;
  createdAt: string;
  updatedAt: string;
}

export interface UserAuthRequest {
  email: string;
  password: string;
  organizationId?: string;
  rememberMe?: boolean;
}

export interface UserAuthResponse {
  success: boolean;
  user?: User;
  accessToken?: string;
  refreshToken?: string;
  expiresIn?: number;
  error?: string;
}

export interface UserRegistrationRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  organizationId?: string;
  tenantId?: string;
  invitationCode?: string;
}

export interface UserUpdateRequest {
  firstName?: string;
  lastName?: string;
  displayName?: string;
  avatar?: string;
  preferences?: Partial<UserPreferences>;
  profile?: Partial<UserProfile>;
  settings?: Partial<UserSettings>;
}

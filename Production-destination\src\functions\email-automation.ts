/**
 * Email Automation Function
 * Handles automated email sending, templates, and campaigns
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Email types enum
enum EmailType {
  WELCOME = 'WELCOME',
  NOTIFICATION = 'NOTIFICATION',
  REMINDER = 'REMINDER',
  INVITATION = 'INVITATION',
  REPORT = 'REPORT',
  MARKETING = 'MARKETING',
  TRANSACTIONAL = 'TRANSACTIONAL',
  SYSTEM = 'SYSTEM'
}

// Email priority enum
enum EmailPriority {
  LOW = 'LOW',
  NORMAL = 'NORMAL',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

// Email status enum
enum EmailStatus {
  PENDING = 'PENDING',
  SENDING = 'SENDING',
  SENT = 'SENT',
  DELIVERED = 'DELIVERED',
  FAILED = 'FAILED',
  BOUNCED = 'BOUNCED',
  OPENED = 'OPENED',
  CLICKED = 'CLICKED'
}

// Validation schemas
const sendEmailSchema = Joi.object({
  to: Joi.array().items(
    Joi.object({
      email: Joi.string().email().required(),
      name: Joi.string().optional()
    })
  ).min(1).required(),
  cc: Joi.array().items(
    Joi.object({
      email: Joi.string().email().required(),
      name: Joi.string().optional()
    })
  ).optional(),
  bcc: Joi.array().items(
    Joi.object({
      email: Joi.string().email().required(),
      name: Joi.string().optional()
    })
  ).optional(),
  subject: Joi.string().required().max(200),
  content: Joi.object({
    text: Joi.string().optional(),
    html: Joi.string().optional()
  }).or('text', 'html').required(),
  templateId: Joi.string().uuid().optional(),
  templateVariables: Joi.object().optional(),
  type: Joi.string().valid(...Object.values(EmailType)).default(EmailType.TRANSACTIONAL),
  priority: Joi.string().valid(...Object.values(EmailPriority)).default(EmailPriority.NORMAL),
  scheduledAt: Joi.date().iso().optional(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional(),
  attachments: Joi.array().items(
    Joi.object({
      filename: Joi.string().required(),
      content: Joi.string().required(),
      contentType: Joi.string().required()
    })
  ).optional(),
  trackOpens: Joi.boolean().default(true),
  trackClicks: Joi.boolean().default(true),
  metadata: Joi.object().optional()
});

const createTemplateSchema = Joi.object({
  name: Joi.string().required().min(2).max(100),
  description: Joi.string().max(500).optional(),
  subject: Joi.string().required().max(200),
  content: Joi.object({
    text: Joi.string().optional(),
    html: Joi.string().optional()
  }).or('text', 'html').required(),
  type: Joi.string().valid(...Object.values(EmailType)).required(),
  variables: Joi.array().items(
    Joi.object({
      name: Joi.string().required(),
      type: Joi.string().valid('text', 'number', 'date', 'boolean').required(),
      label: Joi.string().required(),
      required: Joi.boolean().default(false),
      defaultValue: Joi.any().optional()
    })
  ).default([]),
  organizationId: Joi.string().uuid().required(),
  isActive: Joi.boolean().default(true)
});

const listEmailsSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  organizationId: Joi.string().uuid().required(),
  status: Joi.string().valid(...Object.values(EmailStatus)).optional(),
  type: Joi.string().valid(...Object.values(EmailType)).optional(),
  dateFrom: Joi.date().iso().optional(),
  dateTo: Joi.date().iso().optional(),
  search: Joi.string().max(100).optional()
});

/**
 * Send email handler
 */
export async function sendEmail(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Send email started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = sendEmailSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const emailData = value;

    // Check organization access
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, emailData.organizationId, 'active']);

    if (memberships.length === 0) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Process template if provided
    let finalContent = emailData.content;
    let finalSubject = emailData.subject;

    if (emailData.templateId) {
      const template = await db.readItem('email-templates', emailData.templateId, emailData.templateId);
      if (template) {
        finalContent = applyTemplateVariables((template as any).content, emailData.templateVariables || {});
        finalSubject = applyTemplateVariables((template as any).subject, emailData.templateVariables || {});
      }
    }

    // Create email records for each recipient
    const emailIds: string[] = [];
    const recipients = [
      ...emailData.to,
      ...(emailData.cc || []),
      ...(emailData.bcc || [])
    ];

    for (const recipient of recipients) {
      const emailId = uuidv4();
      const email = {
        id: emailId,
        to: recipient.email,
        toName: recipient.name,
        cc: emailData.cc?.map((r: { email: string; name?: string }) => r.email) || [],
        bcc: emailData.bcc?.map((r: { email: string; name?: string }) => r.email) || [],
        subject: finalSubject,
        content: finalContent,
        templateId: emailData.templateId,
        templateVariables: emailData.templateVariables,
        type: emailData.type,
        priority: emailData.priority,
        status: emailData.scheduledAt ? EmailStatus.PENDING : EmailStatus.SENDING,
        scheduledAt: emailData.scheduledAt,
        sentBy: user.id,
        createdAt: new Date().toISOString(),
        sentAt: emailData.scheduledAt ? null : new Date().toISOString(),
        deliveredAt: null,
        openedAt: null,
        clickedAt: null,
        failureReason: null,
        organizationId: emailData.organizationId,
        projectId: emailData.projectId,
        attachments: emailData.attachments || [],
        trackOpens: emailData.trackOpens,
        trackClicks: emailData.trackClicks,
        metadata: emailData.metadata || {},
        tenantId: user.tenantId
      };

      await db.createItem('emails', email);
      emailIds.push(emailId);

      // Send email immediately if not scheduled
      if (!emailData.scheduledAt) {
        await processEmailDelivery(email);
      }
    }

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "email_sent",
      userId: user.id,
      organizationId: emailData.organizationId,
      projectId: emailData.projectId,
      timestamp: new Date().toISOString(),
      details: {
        emailType: emailData.type,
        recipientCount: recipients.length,
        subject: finalSubject,
        isScheduled: !!emailData.scheduledAt,
        scheduledAt: emailData.scheduledAt,
        hasAttachments: (emailData.attachments || []).length > 0
      },
      tenantId: user.tenantId
    });

    logger.info("Email sent successfully", {
      correlationId,
      emailIds,
      userId: user.id,
      organizationId: emailData.organizationId,
      recipientCount: recipients.length,
      type: emailData.type,
      isScheduled: !!emailData.scheduledAt
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        emailIds,
        recipientCount: recipients.length,
        type: emailData.type,
        priority: emailData.priority,
        scheduledAt: emailData.scheduledAt,
        message: emailData.scheduledAt ? "Email scheduled successfully" : "Email sent successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Send email failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Create email template handler
 */
export async function createEmailTemplate(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create email template started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createTemplateSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const templateData = value;

    // Check organization access
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, templateData.organizationId, 'active']);

    if (memberships.length === 0 || (memberships[0] as any).role !== 'ADMIN') {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Only organization admins can create email templates" }
      }, request);
    }

    // Create template
    const templateId = uuidv4();
    const template = {
      id: templateId,
      name: templateData.name,
      description: templateData.description || "",
      subject: templateData.subject,
      content: templateData.content,
      type: templateData.type,
      variables: templateData.variables,
      organizationId: templateData.organizationId,
      isActive: templateData.isActive,
      createdBy: user.id,
      createdAt: new Date().toISOString(),
      updatedBy: user.id,
      updatedAt: new Date().toISOString(),
      usageCount: 0,
      tenantId: user.tenantId
    };

    await db.createItem('email-templates', template);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "email_template_created",
      userId: user.id,
      organizationId: templateData.organizationId,
      templateId,
      timestamp: new Date().toISOString(),
      details: {
        templateName: template.name,
        templateType: template.type,
        variableCount: template.variables.length
      },
      tenantId: user.tenantId
    });

    logger.info("Email template created successfully", {
      correlationId,
      templateId,
      userId: user.id,
      organizationId: templateData.organizationId,
      type: templateData.type
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: templateId,
        name: template.name,
        type: template.type,
        variables: template.variables,
        message: "Email template created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create email template failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * List emails handler
 */
export async function listEmails(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("List emails started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate query parameters
    const queryParams = Object.fromEntries(request.query.entries());
    const { error, value } = listEmailsSchema.validate(queryParams);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { page, limit, organizationId, status, type, dateFrom, dateTo, search } = value;

    // Check organization access
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, organizationId, 'active']);

    if (memberships.length === 0) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Build query
    let queryText = 'SELECT * FROM c WHERE c.organizationId = @orgId';
    const parameters: any[] = [organizationId];

    if (status) {
      queryText += ' AND c.status = @status';
      parameters.push(status);
    }

    if (type) {
      queryText += ' AND c.type = @type';
      parameters.push(type);
    }

    if (dateFrom) {
      queryText += ' AND c.createdAt >= @dateFrom';
      parameters.push(dateFrom);
    }

    if (dateTo) {
      queryText += ' AND c.createdAt <= @dateTo';
      parameters.push(dateTo);
    }

    if (search) {
      queryText += ' AND (CONTAINS(LOWER(c.subject), LOWER(@search)) OR CONTAINS(LOWER(c.to), LOWER(@search)))';
      parameters.push(search);
    }

    // Add ordering
    queryText += ' ORDER BY c.createdAt DESC';

    // Get total count
    const countQuery = queryText.replace('SELECT *', 'SELECT VALUE COUNT(1)');
    const countResult = await db.queryItems('emails', countQuery, parameters);
    const total = Number(countResult[0]) || 0;

    // Add pagination
    const offset = (page - 1) * limit;
    const paginatedQuery = `${queryText} OFFSET ${offset} LIMIT ${limit}`;

    // Execute query
    const emails = await db.queryItems('emails', paginatedQuery, parameters);

    // Remove sensitive content for non-admins
    const isAdmin = (memberships[0] as any).role === 'ADMIN';
    const sanitizedEmails = emails.map((email: any) => {
      const sanitized = { ...email };
      if (!isAdmin) {
        delete sanitized.content;
        delete sanitized.bcc;
      }
      return sanitized;
    });

    logger.info("Emails listed successfully", {
      correlationId,
      userId: user.id,
      organizationId,
      count: emails.length,
      page,
      limit
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        organizationId,
        items: sanitizedEmails,
        total,
        page,
        limit,
        hasMore: page * limit < total,
        filters: { status, type, dateFrom, dateTo, search }
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("List emails failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Apply template variables to content
 */
function applyTemplateVariables(content: any, variables: any): any {
  if (typeof content === 'string') {
    let result = content;
    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{{${key}}}`;
      result = result.replace(new RegExp(placeholder, 'g'), String(value));
    });
    return result;
  }

  if (typeof content === 'object' && content !== null) {
    const result: any = {};
    Object.entries(content).forEach(([key, value]) => {
      result[key] = applyTemplateVariables(value, variables);
    });
    return result;
  }

  return content;
}

/**
 * Process email delivery (simplified implementation)
 */
async function processEmailDelivery(email: any): Promise<void> {
  try {
    // In production, integrate with email service provider (SendGrid, AWS SES, etc.)
    logger.info("Processing email delivery", {
      emailId: email.id,
      to: email.to,
      subject: email.subject,
      type: email.type
    });

    // Simulate email sending
    const success = Math.random() > 0.05; // 95% success rate

    // Update email status
    const updatedEmail = {
      ...email,
      id: email.id,
      status: success ? EmailStatus.SENT : EmailStatus.FAILED,
      sentAt: success ? new Date().toISOString() : null,
      failureReason: success ? null : 'Simulated delivery failure'
    };

    await db.updateItem('emails', updatedEmail);

    logger.info("Email delivery processed", {
      emailId: email.id,
      success,
      status: updatedEmail.status
    });

  } catch (error) {
    logger.error("Email delivery failed", {
      emailId: email.id,
      error: error instanceof Error ? error.message : String(error)
    });

    // Update email status to failed
    const failedEmail = {
      ...email,
      id: email.id,
      status: EmailStatus.FAILED,
      failureReason: error instanceof Error ? error.message : String(error)
    };

    await db.updateItem('emails', failedEmail);
  }
}

// Register functions
app.http('email-automation-send', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'emails/automation/send',
  handler: sendEmail
});

app.http('email-automation-template-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'emails/automation/templates',
  handler: createEmailTemplate
});

app.http('email-list', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'emails',
  handler: listEmails
});

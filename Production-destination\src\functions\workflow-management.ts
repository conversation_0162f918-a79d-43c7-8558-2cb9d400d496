/**
 * Workflow Management Functions
 * Handles workflow creation, retrieval, and basic operations
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Workflow enums
export enum WorkflowStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  PAUSED = 'paused'
}

export enum StepStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  SKIPPED = 'skipped',
  FAILED = 'failed'
}

export enum StepType {
  REVIEW = 'review',
  APPROVAL = 'approval',
  PROCESSING = 'processing',
  NOTIFICATION = 'notification',
  CUSTOM = 'custom'
}

// Validation schemas
const createWorkflowSchema = Joi.object({
  name: Joi.string().required().max(255),
  description: Joi.string().optional().max(1000),
  projectId: Joi.string().uuid().required(),
  organizationId: Joi.string().uuid().required(),
  documentId: Joi.string().uuid().optional(),
  templateId: Joi.string().uuid().optional(),
  steps: Joi.array().items(Joi.object({
    name: Joi.string().required(),
    description: Joi.string().optional(),
    type: Joi.string().valid(...Object.values(StepType)).required(),
    order: Joi.number().integer().min(1).required(),
    assigneeId: Joi.string().uuid().optional(),
    dueDate: Joi.date().iso().optional(),
    required: Joi.boolean().default(true)
  })).min(1).required(),
  priority: Joi.string().valid('low', 'medium', 'high', 'urgent').default('medium'),
  dueDate: Joi.date().iso().optional()
});

const listWorkflowsSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  projectId: Joi.string().uuid().optional(),
  status: Joi.string().valid(...Object.values(WorkflowStatus)).optional(),
  assigneeId: Joi.string().uuid().optional()
});

interface WorkflowStep {
  id: string;
  name: string;
  description?: string;
  type: StepType;
  order: number;
  status: StepStatus;
  assigneeId?: string;
  dueDate?: string;
  required: boolean;
  createdAt: string;
  completedAt?: string;
  completedBy?: string;
}

interface Workflow {
  id: string;
  name: string;
  description?: string;
  status: WorkflowStatus;
  projectId: string;
  organizationId: string;
  documentId?: string;
  templateId?: string;
  steps: WorkflowStep[];
  currentStepId?: string;
  priority: string;
  dueDate?: string;
  createdBy: string;
  createdAt: string;
  updatedBy: string;
  updatedAt: string;
  completedAt?: string;
  tenantId?: string;
}

/**
 * Create workflow handler
 */
export async function createWorkflow(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Workflow creation started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createWorkflowSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const workflowData = value;

    // Create workflow steps with IDs and default status
    const steps: WorkflowStep[] = workflowData.steps.map((step: any) => ({
      id: uuidv4(),
      name: step.name,
      description: step.description,
      type: step.type,
      order: step.order,
      status: StepStatus.PENDING,
      assigneeId: step.assigneeId,
      dueDate: step.dueDate,
      required: step.required,
      createdAt: new Date().toISOString()
    }));

    // Sort steps by order
    steps.sort((a, b) => a.order - b.order);

    // Create workflow object
    const workflow: Workflow = {
      id: uuidv4(),
      name: workflowData.name,
      description: workflowData.description,
      status: WorkflowStatus.DRAFT,
      projectId: workflowData.projectId,
      organizationId: workflowData.organizationId,
      documentId: workflowData.documentId,
      templateId: workflowData.templateId,
      steps,
      currentStepId: steps.length > 0 ? steps[0].id : undefined,
      priority: workflowData.priority,
      dueDate: workflowData.dueDate,
      createdBy: user.id,
      createdAt: new Date().toISOString(),
      updatedBy: user.id,
      updatedAt: new Date().toISOString(),
      tenantId: user.tenantId
    };

    // Save to database
    await db.createItem('workflows', workflow);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "workflow_created",
      userId: user.id,
      projectId: workflow.projectId,
      workflowId: workflow.id,
      timestamp: new Date().toISOString(),
      details: {
        workflowName: workflow.name,
        documentId: workflow.documentId
      }
    });

    logger.info("Workflow created successfully", {
      correlationId,
      workflowId: workflow.id,
      userId: user.id,
      stepsCount: steps.length
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: workflow.id,
        name: workflow.name,
        status: workflow.status,
        message: "Workflow created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Workflow creation failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Failed to create workflow" }
    }, request);
  }
}

/**
 * Get workflow handler
 */
export async function getWorkflow(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const workflowId = request.params.id;

  if (!workflowId) {
    return addCorsHeaders({
      status: 400,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: 'Workflow ID is required' }
    }, request);
  }

  logger.info("Workflow retrieval started", { correlationId, workflowId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Get workflow from database
    const workflow = await db.readItem('workflows', workflowId, workflowId);

    if (!workflow) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Workflow not found" }
      }, request);
    }

    // Check access permissions
    const hasAccess = (
      (workflow as any).createdBy === user.id ||
      (workflow as any).organizationId === user.tenantId ||
      (workflow as any).steps.some((step: WorkflowStep) => step.assigneeId === user.id) ||
      user.roles?.includes('admin')
    );

    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied" }
      }, request);
    }

    // Add HATEOAS links
    const workflowWithLinks = {
      ...workflow,
      _links: {
        self: { href: `/workflows/${(workflow as any).id}` },
        start: { href: `/workflows/${(workflow as any).id}/start` },
        steps: { href: `/workflows/${(workflow as any).id}/steps` }
      }
    };

    logger.info("Workflow retrieved successfully", {
      correlationId,
      workflowId,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: workflowWithLinks
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Workflow retrieval failed", {
      correlationId,
      workflowId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * List workflows handler
 */
export async function listWorkflows(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Workflow list started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate query parameters
    const queryParams = Object.fromEntries(request.query.entries());
    const { error, value } = listWorkflowsSchema.validate(queryParams);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { page, limit, projectId, status, assigneeId } = value;

    // Build query
    let query = 'SELECT * FROM c WHERE 1=1';
    const parameters: any[] = [];

    // Add user access filter
    if (user.tenantId) {
      query += ' AND (c.organizationId = @tenantId OR c.createdBy = @userId)';
      parameters.push(user.tenantId, user.id);
    } else {
      query += ' AND c.createdBy = @userId';
      parameters.push(user.id);
    }

    // Add filters
    if (projectId) {
      query += ' AND c.projectId = @projectId';
      parameters.push(projectId);
    }

    if (status) {
      query += ' AND c.status = @status';
      parameters.push(status);
    }

    if (assigneeId) {
      query += ' AND ARRAY_CONTAINS(c.steps, {"assigneeId": @assigneeId}, true)';
      parameters.push(assigneeId);
    }

    query += ' ORDER BY c.createdAt DESC';

    // Execute query with pagination
    const offset = (page - 1) * limit;
    query += ` OFFSET ${offset} LIMIT ${limit}`;

    const workflows = await db.queryItems('workflows', query, parameters);

    logger.info("Workflows listed successfully", {
      correlationId,
      userId: user.id,
      count: workflows.length,
      page,
      limit
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        workflows,
        pagination: {
          page,
          limit,
          hasMore: workflows.length === limit
        }
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Workflow list failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Combined workflows handler
 */
async function handleWorkflows(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const method = request.method.toUpperCase();

  switch (method) {
    case 'POST':
      return await createWorkflow(request, context);
    case 'GET':
      return await listWorkflows(request, context);
    case 'OPTIONS':
      return handlePreflight(request) || { status: 200 };
    default:
      return addCorsHeaders({
        status: 405,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Method not allowed' }
      }, request);
  }
}

// Register functions
app.http('workflows', {
  methods: ['GET', 'POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'workflows',
  handler: handleWorkflows
});

app.http('workflow-get', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'workflows/{id}',
  handler: getWorkflow
});

[{"changedTime": "2025-02-02T14:34:48.750993+00:00", "createdTime": "2025-02-02T14:24:23.469893+00:00", "extendedLocation": null, "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.Storage/storageAccounts/stdocucontex900520441468", "identity": null, "kind": "StorageV2", "location": "eastus", "managedBy": null, "name": "stdocucontex900520441468", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "DocuContext", "sku": {"capacity": null, "family": null, "model": null, "name": "Standard_LRS", "size": null, "tier": "Standard"}, "tags": {}, "type": "Microsoft.Storage/storageAccounts"}, {"changedTime": "2025-02-02T14:35:11.883634+00:00", "createdTime": "2025-02-02T14:24:23.492704+00:00", "extendedLocation": null, "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.CognitiveServices/accounts/ai-docucontext900520441468", "identity": null, "kind": "AIServices", "location": "eastus", "managedBy": null, "name": "ai-docucontext900520441468", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "DocuContext", "sku": {"capacity": null, "family": null, "model": null, "name": "S0", "size": null, "tier": null}, "systemData": {"createdAt": "2025-02-02T14:24:23.5044569Z", "createdBy": "<EMAIL>", "createdByType": "User", "lastModifiedAt": "2025-02-02T14:24:23.5044569Z", "lastModifiedBy": "<EMAIL>", "lastModifiedByType": "User"}, "tags": null, "type": "Microsoft.CognitiveServices/accounts"}, {"changedTime": "2025-02-27T04:28:59.207224+00:00", "createdTime": "2025-02-02T14:24:23.495497+00:00", "extendedLocation": null, "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.KeyVault/vaults/kv-docucont900520441468", "identity": null, "kind": null, "location": "eastus", "managedBy": null, "name": "kv-docucont900520441468", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "DocuContext", "sku": null, "tags": {}, "type": "Microsoft.KeyVault/vaults"}, {"changedTime": "2025-02-02T14:35:48.729927+00:00", "createdTime": "2025-02-02T14:25:07.936867+00:00", "extendedLocation": null, "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.MachineLearningServices/workspaces/docucontext", "identity": {"principalId": "02d377f5-ea72-4045-be1e-044209cc1072", "tenantId": "c61b730f-d35e-4417-9ba1-7e8d566a99fe", "type": "SystemAssigned", "userAssignedIdentities": null}, "kind": "<PERSON><PERSON>", "location": "eastus", "managedBy": null, "name": "docucontext", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "DocuContext", "sku": {"capacity": null, "family": null, "model": null, "name": "Basic", "size": null, "tier": "Basic"}, "systemData": {"createdAt": "2025-02-02T14:25:07.9749762Z", "createdBy": "<EMAIL>", "createdByType": "User", "lastModifiedAt": "2025-02-02T14:25:07.9749762Z", "lastModifiedBy": "<EMAIL>", "lastModifiedByType": "User"}, "tags": {}, "type": "Microsoft.MachineLearningServices/workspaces"}, {"changedTime": "2025-02-02T14:38:21.490436+00:00", "createdTime": "2025-02-02T14:27:49.933246+00:00", "extendedLocation": null, "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.MachineLearningServices/workspaces/docucontexthepz", "identity": {"principalId": "bdee8fa9-9a9c-47d6-be60-9c9dfc450212", "tenantId": "c61b730f-d35e-4417-9ba1-7e8d566a99fe", "type": "SystemAssigned", "userAssignedIdentities": null}, "kind": "Project", "location": "eastus", "managedBy": null, "name": "docucontexthepz", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "DocuContext", "sku": {"capacity": null, "family": null, "model": null, "name": "Basic", "size": null, "tier": "Basic"}, "systemData": {"createdAt": "2025-02-02T14:27:49.9521345Z", "createdBy": "0736f41a-0425-4b46-bdb5-1563eff02385", "createdByType": "Application", "lastModifiedAt": "2025-02-02T14:27:49.9521345Z", "lastModifiedBy": "0736f41a-0425-4b46-bdb5-1563eff02385", "lastModifiedByType": "Application"}, "tags": {}, "type": "Microsoft.MachineLearningServices/workspaces"}, {"changedTime": "2025-02-02T14:40:46.333020+00:00", "createdTime": "2025-02-02T14:29:18.868550+00:00", "extendedLocation": null, "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.MachineLearningServices/workspaces/docuContextHepz/serverlessEndpoints/DeepSeek-R1-pvcnl", "identity": null, "kind": null, "location": "eastus", "managedBy": null, "name": "docuContextHepz/DeepSeek-R1-pvcnl", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "DocuContext", "sku": {"capacity": null, "family": null, "model": null, "name": "Consumption", "size": null, "tier": null}, "systemData": {"createdAt": "2025-02-02T14:29:19.0934352Z", "createdBy": "<EMAIL>", "createdByType": "User", "lastModifiedAt": "2025-02-02T14:29:19.0934352Z", "lastModifiedBy": "<EMAIL>", "lastModifiedByType": "User"}, "tags": null, "type": "Microsoft.MachineLearningServices/workspaces/serverlessEndpoints"}, {"changedTime": "2025-02-02T14:55:31.558720+00:00", "createdTime": "2025-02-02T14:44:50.370087+00:00", "extendedLocation": null, "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.CognitiveServices/accounts/DoucuContextDocuIntell", "identity": {"principalId": null, "tenantId": null, "type": "None", "userAssignedIdentities": null}, "kind": "FormRecognizer", "location": "eastus", "managedBy": null, "name": "DoucuContextDocuIntell", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "DocuContext", "sku": {"capacity": null, "family": null, "model": null, "name": "S0", "size": null, "tier": null}, "systemData": {"createdAt": "2025-02-02T14:44:50.3925991Z", "createdBy": "<EMAIL>", "createdByType": "User", "lastModifiedAt": "2025-02-02T14:44:50.3925991Z", "lastModifiedBy": "<EMAIL>", "lastModifiedByType": "User"}, "tags": {}, "type": "Microsoft.CognitiveServices/accounts"}, {"changedTime": "2025-02-27T08:33:35.089326+00:00", "createdTime": "2025-02-02T14:55:15.505700+00:00", "extendedLocation": null, "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.CognitiveServices/accounts/DocuContextOCR", "identity": {"principalId": null, "tenantId": null, "type": "None", "userAssignedIdentities": null}, "kind": "ComputerVision", "location": "eastus", "managedBy": null, "name": "DocuContextOCR", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "DocuContext", "sku": {"capacity": null, "family": null, "model": null, "name": "F0", "size": null, "tier": null}, "systemData": {"createdAt": "2025-02-02T14:55:15.5218912Z", "createdBy": "<EMAIL>", "createdByType": "User", "lastModifiedAt": "2025-02-27T08:23:35.0291081Z", "lastModifiedBy": "<EMAIL>", "lastModifiedByType": "User"}, "tags": {}, "type": "Microsoft.CognitiveServices/accounts"}, {"changedTime": "2025-02-02T15:09:20.891594+00:00", "createdTime": "2025-02-02T14:58:39.733997+00:00", "extendedLocation": null, "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.CognitiveServices/accounts/DocuContextOpenAI", "identity": null, "kind": "OpenAI", "location": "eastus", "managedBy": null, "name": "DocuContextOpenAI", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "DocuContext", "sku": {"capacity": null, "family": null, "model": null, "name": "S0", "size": null, "tier": null}, "systemData": {"createdAt": "2025-02-02T14:58:39.7518181Z", "createdBy": "<EMAIL>", "createdByType": "User", "lastModifiedAt": "2025-02-02T14:58:39.7518181Z", "lastModifiedBy": "<EMAIL>", "lastModifiedByType": "User"}, "tags": {}, "type": "Microsoft.CognitiveServices/accounts"}, {"changedTime": "2025-02-02T15:09:37.166467+00:00", "createdTime": "2025-02-02T14:58:58.302082+00:00", "extendedLocation": null, "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.MachineLearningServices/workspaces/docuContextHepz/serverlessEndpoints/Llama-3-3-70B-Instruct-finkl", "identity": null, "kind": null, "location": "eastus", "managedBy": null, "name": "docuContextHepz/Llama-3-3-70B-Instruct-finkl", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "DocuContext", "sku": {"capacity": null, "family": null, "model": null, "name": "Consumption", "size": null, "tier": null}, "systemData": {"createdAt": "2025-02-02T14:58:58.5024426Z", "createdBy": "<EMAIL>", "createdByType": "User", "lastModifiedAt": "2025-02-02T14:58:58.5024426Z", "lastModifiedBy": "<EMAIL>", "lastModifiedByType": "User"}, "tags": null, "type": "Microsoft.MachineLearningServices/workspaces/serverlessEndpoints"}, {"changedTime": "2025-02-02T15:17:47.813981+00:00", "createdTime": "2025-02-02T15:07:27.004631+00:00", "extendedLocation": null, "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.MachineLearningServices/workspaces/docuContextHepz/serverlessEndpoints/Cohere-embed-v3-multilingual-nxn", "identity": null, "kind": null, "location": "eastus", "managedBy": null, "name": "docuContextHepz/Cohere-embed-v3-multilingual-nxn", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "DocuContext", "sku": {"capacity": null, "family": null, "model": null, "name": "Consumption", "size": null, "tier": null}, "systemData": {"createdAt": "2025-02-02T15:07:27.196854Z", "createdBy": "<EMAIL>", "createdByType": "User", "lastModifiedAt": "2025-02-02T15:07:27.196854Z", "lastModifiedBy": "<EMAIL>", "lastModifiedByType": "User"}, "tags": null, "type": "Microsoft.MachineLearningServices/workspaces/serverlessEndpoints"}, {"changedTime": "2025-02-02T15:41:37.388289+00:00", "createdTime": "2025-02-02T15:30:09.211128+00:00", "extendedLocation": null, "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.AzureActiveDirectory/b2cDirectories/hepzdocs.onmicrosoft.com", "identity": null, "kind": null, "location": "unitedstates", "managedBy": null, "name": "hepzdocs.onmicrosoft.com", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "DocuContext", "sku": {"capacity": null, "family": null, "model": null, "name": "PremiumP1", "size": null, "tier": "A0"}, "systemData": {"createdAt": "2025-02-02T15:30:09.4154067Z", "createdBy": "<EMAIL>", "createdByType": "User", "lastModifiedAt": "2025-02-02T15:30:09.4154067Z", "lastModifiedBy": "<EMAIL>", "lastModifiedByType": "User"}, "tags": null, "type": "Microsoft.AzureActiveDirectory/b2cDirectories"}, {"changedTime": "2025-02-06T23:08:12.959587+00:00", "createdTime": "2025-02-06T22:58:16.520892+00:00", "extendedLocation": null, "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.Web/staticSites/hepztechlanding", "identity": null, "kind": null, "location": "eastus2", "managedBy": null, "name": "hepztechlanding", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "DocuContext", "sku": {"capacity": null, "family": null, "model": null, "name": "Standard", "size": null, "tier": "Standard"}, "tags": {}, "type": "Microsoft.Web/staticSites"}, {"changedTime": "2025-02-07T00:26:48.291264+00:00", "createdTime": "2025-02-07T00:16:47.488249+00:00", "extendedLocation": null, "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/microsoft.insights/components/hepzlandinginsights", "identity": null, "kind": "web", "location": "eastus", "managedBy": null, "name": "hepzlandinginsights", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "DocuContext", "sku": null, "tags": {}, "type": "microsoft.insights/components"}, {"changedTime": "2025-02-07T00:36:52.727826+00:00", "createdTime": "2025-02-07T00:26:52.509425+00:00", "extendedLocation": null, "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/microsoft.insights/actiongroups/Application Insights Smart Detection", "identity": null, "kind": null, "location": "global", "managedBy": null, "name": "Application Insights Smart Detection", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "DocuContext", "sku": null, "tags": null, "type": "microsoft.insights/actiongroups"}, {"changedTime": "2025-05-21T08:15:56.550965+00:00", "createdTime": "2025-05-21T08:05:10.277866+00:00", "extendedLocation": null, "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.EventGrid/namespaces/hepzfullstack", "identity": {"principalId": null, "tenantId": null, "type": "None", "userAssignedIdentities": null}, "kind": null, "location": "eastus", "managedBy": null, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "DocuContext", "sku": {"capacity": 1, "family": null, "model": null, "name": "Standard", "size": null, "tier": null}, "systemData": {"createdAt": "2025-05-21T08:05:10.288988Z", "createdBy": "<EMAIL>", "createdByType": "User", "lastModifiedAt": "2025-05-21T08:05:10.288988Z", "lastModifiedBy": "<EMAIL>", "lastModifiedByType": "User"}, "tags": {}, "type": "Microsoft.EventGrid/namespaces"}, {"changedTime": "2025-05-21T08:22:18.285176+00:00", "createdTime": "2025-05-21T08:12:05.005421+00:00", "extendedLocation": null, "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.EventGrid/topics/hepzeg", "identity": {"principalId": null, "tenantId": null, "type": "None", "userAssignedIdentities": null}, "kind": "Azure", "location": "eastus", "managedBy": null, "name": "<PERSON><PERSON><PERSON><PERSON>", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "DocuContext", "sku": {"capacity": null, "family": null, "model": null, "name": "Basic", "size": null, "tier": null}, "tags": {}, "type": "Microsoft.EventGrid/topics"}, {"changedTime": "2025-05-21T15:36:26.990037+00:00", "createdTime": "2025-05-21T15:26:26.300616+00:00", "extendedLocation": null, "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.Web/serverFarms/ASP-DocuContext-9052", "identity": null, "kind": "functionapp", "location": "eastus", "managedBy": null, "name": "ASP-DocuContext-9052", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "DocuContext", "sku": {"capacity": 0, "family": "Y", "model": null, "name": "Y1", "size": "Y1", "tier": "Dynamic"}, "tags": {}, "type": "Microsoft.Web/serverFarms"}, {"changedTime": "2025-05-21T15:36:36.030850+00:00", "createdTime": "2025-05-21T15:26:35.467059+00:00", "extendedLocation": null, "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.Web/sites/hepzlogic", "identity": null, "kind": "functionapp", "location": "eastus", "managedBy": null, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "DocuContext", "sku": null, "tags": {"hidden-link: /app-insights-resource-id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/microsoft.insights/components/hepzlandinginsights"}, "type": "Microsoft.Web/sites"}, {"changedTime": "2025-05-21T15:37:23.005143+00:00", "createdTime": "2025-05-21T15:27:22.136690+00:00", "extendedLocation": null, "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.ManagedIdentity/userAssignedIdentities/hepzlogic-id-80fe", "identity": null, "kind": null, "location": "eastus", "managedBy": null, "name": "hepzlogic-id-80fe", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "DocuContext", "sku": null, "tags": {}, "type": "Microsoft.ManagedIdentity/userAssignedIdentities"}, {"changedTime": "2025-05-30T11:42:21.299003+00:00", "createdTime": "2025-05-22T11:01:33.025593+00:00", "extendedLocation": null, "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.ServiceBus/namespaces/hepzbackend", "identity": null, "kind": null, "location": "eastus", "managedBy": null, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "DocuContext", "sku": {"capacity": null, "family": null, "model": null, "name": "Standard", "size": null, "tier": "Standard"}, "tags": {}, "type": "Microsoft.ServiceBus/namespaces"}, {"changedTime": "2025-05-25T20:18:06.462213+00:00", "createdTime": "2025-05-25T20:07:14.595456+00:00", "extendedLocation": null, "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.SignalRService/SignalR/hepztech", "identity": null, "kind": "SignalR", "location": "eastus", "managedBy": null, "name": "hepztech", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "DocuContext", "sku": {"capacity": 1, "family": null, "model": null, "name": "Free_F1", "size": "F1", "tier": "Free"}, "systemData": {"createdAt": "2025-05-25T20:07:14.6203044Z", "createdBy": "<EMAIL>", "createdByType": "User", "lastModifiedAt": "2025-05-25T20:07:14.6203044Z", "lastModifiedBy": "<EMAIL>", "lastModifiedByType": "User"}, "tags": {}, "type": "Microsoft.SignalRService/SignalR"}, {"changedTime": "2025-05-25T20:45:09.792362+00:00", "createdTime": "2025-05-25T20:34:03.354355+00:00", "extendedLocation": null, "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.NotificationHubs/namespaces/hepzdocs", "identity": null, "kind": null, "location": "eastus", "managedBy": null, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "DocuContext", "sku": {"capacity": null, "family": null, "model": null, "name": "Free", "size": null, "tier": null}, "tags": {}, "type": "Microsoft.NotificationHubs/namespaces"}, {"changedTime": "2025-05-25T20:45:05.376076+00:00", "createdTime": "2025-05-25T20:35:03.965071+00:00", "extendedLocation": null, "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.NotificationHubs/namespaces/hepzdocs/notificationHubs/mainhub", "identity": null, "kind": null, "location": "eastus", "managedBy": null, "name": "he<PERSON>z<PERSON><PERSON>/mainhub", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "DocuContext", "sku": null, "tags": {}, "type": "Microsoft.NotificationHubs/namespaces/notificationHubs"}, {"changedTime": "2025-05-26T16:57:36.084090+00:00", "createdTime": "2025-05-26T16:34:20.822119+00:00", "extendedLocation": null, "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.Cache/redisEnterprise/hepzbackend", "identity": {"principalId": null, "tenantId": null, "type": "None", "userAssignedIdentities": null}, "kind": null, "location": "eastus", "managedBy": null, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "DocuContext", "sku": {"capacity": null, "family": null, "model": null, "name": "Balanced_B0", "size": null, "tier": null}, "tags": {}, "type": "Microsoft.Cache/redisEnterprise"}, {"changedTime": "2025-05-27T04:46:00.749589+00:00", "createdTime": "2025-05-27T04:34:09.757275+00:00", "extendedLocation": null, "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.DocumentDb/databaseAccounts/hepz", "identity": {"principalId": null, "tenantId": null, "type": "None", "userAssignedIdentities": null}, "kind": "GlobalDocumentDB", "location": "eastus2", "managedBy": null, "name": "hepz", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "DocuContext", "sku": null, "systemData": {"createdAt": "2025-05-27T04:35:39.2635709Z"}, "tags": {"defaultExperience": "Core (SQL)", "hidden-cosmos-mmspecial": "", "hidden-workload-type": "Production"}, "type": "Microsoft.DocumentDb/databaseAccounts"}, {"changedTime": "2025-05-27T05:00:49.489065+00:00", "createdTime": "2025-05-27T04:50:48.012502+00:00", "extendedLocation": null, "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.Search/searchServices/hepzaisearch", "identity": null, "kind": null, "location": "eastus", "managedBy": null, "name": "hepzaisearch", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "DocuContext", "sku": {"capacity": null, "family": null, "model": null, "name": "basic", "size": null, "tier": null}, "tags": {}, "type": "Microsoft.Search/searchServices"}, {"changedTime": "2025-05-28T15:56:20.778631+00:00", "createdTime": "2025-05-28T15:46:20.510734+00:00", "extendedLocation": null, "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.PortalServices/dashboards/maindash", "identity": null, "kind": null, "location": "eastus", "managedBy": null, "name": "maindash", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "DocuContext", "sku": null, "systemData": {"createdAt": "2025-05-28T15:46:20.7582735Z", "createdBy": "<EMAIL>", "createdByType": "User", "lastModifiedAt": "2025-05-28T15:46:20.7582735Z", "lastModifiedBy": "<EMAIL>", "lastModifiedByType": "User"}, "tags": null, "type": "Microsoft.PortalServices/dashboards"}]
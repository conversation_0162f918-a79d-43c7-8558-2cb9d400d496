/**
 * CORS middleware for Azure Functions
 * Handles Cross-Origin Resource Sharing headers and preflight requests
 */

import { HttpRequest, HttpResponseInit } from '@azure/functions';

export interface CorsOptions {
  allowedOrigins?: string[];
  allowedMethods?: string[];
  allowedHeaders?: string[];
  allowCredentials?: boolean;
  maxAge?: number;
}

const defaultCorsOptions: CorsOptions = {
  allowedOrigins: ['*'],
  allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'Access-Control-Request-Method',
    'Access-Control-Request-Headers',
    'X-Tenant-ID',
    'X-User-ID'
  ],
  allowCredentials: true,
  maxAge: 86400 // 24 hours
};

/**
 * Add CORS headers to response
 */
export function addCorsHeaders(
  response: HttpResponseInit,
  request: HttpRequest,
  options: CorsOptions = {}
): HttpResponseInit {
  const corsOptions = { ...defaultCorsOptions, ...options };
  const origin = request.headers.get('origin') || '*';
  
  // Determine allowed origin
  let allowedOrigin = '*';
  if (corsOptions.allowedOrigins && corsOptions.allowedOrigins.length > 0) {
    if (corsOptions.allowedOrigins.includes('*')) {
      allowedOrigin = origin;
    } else if (corsOptions.allowedOrigins.includes(origin)) {
      allowedOrigin = origin;
    } else {
      allowedOrigin = corsOptions.allowedOrigins[0];
    }
  }

  const corsHeaders = {
    'Access-Control-Allow-Origin': allowedOrigin,
    'Access-Control-Allow-Methods': corsOptions.allowedMethods?.join(', ') || '',
    'Access-Control-Allow-Headers': corsOptions.allowedHeaders?.join(', ') || '',
    'Access-Control-Max-Age': corsOptions.maxAge?.toString() || '86400',
    ...(corsOptions.allowCredentials && { 'Access-Control-Allow-Credentials': 'true' })
  };

  return {
    ...response,
    headers: {
      ...response.headers,
      ...corsHeaders
    }
  };
}

/**
 * Handle preflight OPTIONS request
 */
export function handlePreflight(
  request: HttpRequest,
  options: CorsOptions = {}
): HttpResponseInit | null {
  if (request.method === 'OPTIONS') {
    return addCorsHeaders({
      status: 200,
      headers: {
        'Content-Length': '0'
      }
    }, request, options);
  }
  return null;
}

/**
 * CORS middleware wrapper for Azure Functions
 */
export function withCors(
  handler: (request: HttpRequest, context: any) => Promise<HttpResponseInit>,
  options: CorsOptions = {}
) {
  return async (request: HttpRequest, context: any): Promise<HttpResponseInit> => {
    // Handle preflight request
    const preflightResponse = handlePreflight(request, options);
    if (preflightResponse) {
      return preflightResponse;
    }

    // Execute the handler
    const response = await handler(request, context);
    
    // Add CORS headers to response
    return addCorsHeaders(response, request, options);
  };
}

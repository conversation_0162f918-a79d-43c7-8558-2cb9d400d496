/**
 * Event Grid Testing and Monitoring Script
 * Tests Event Grid configuration and monitors event processing
 */

const { EventGridPublisherClient } = require('@azure/eventgrid');
const { AzureKeyCredential } = require('@azure/core-auth');
const fs = require('fs');
const path = require('path');

// Load configuration
const localSettingsPath = path.join(__dirname, '..', 'local.settings.json');
let localSettings = {};

try {
  localSettings = JSON.parse(fs.readFileSync(localSettingsPath, 'utf8'));
} catch (error) {
  console.error('❌ Failed to load local.settings.json:', error.message);
  process.exit(1);
}

// Configuration
const config = {
  eventGrid: {
    endpoint: localSettings.Values?.EVENT_GRID_TOPIC_ENDPOINT || process.env.EVENT_GRID_TOPIC_ENDPOINT,
    key: localSettings.Values?.EVENT_GRID_TOPIC_KEY || process.env.EVENT_GRID_TOPIC_KEY,
    topicName: localSettings.Values?.EVENT_GRID_TOPIC_NAME || process.env.EVENT_GRID_TOPIC_NAME
  },
  functionApp: {
    url: 'https://localhost:7071' // Local development URL
  }
};

/**
 * Test Event Grid publishing
 */
async function testEventGridPublishing() {
  console.log('🧪 Testing Event Grid Publishing...');

  if (!config.eventGrid.endpoint || !config.eventGrid.key) {
    console.error('❌ Event Grid configuration not found');
    return false;
  }

  try {
    const client = new EventGridPublisherClient(
      config.eventGrid.endpoint,
      "EventGrid",
      new AzureKeyCredential(config.eventGrid.key)
    );

    // Test events
    const testEvents = [
      {
        id: `test-document-${Date.now()}`,
        eventType: 'Document.Uploaded',
        subject: 'documents/test-document.pdf',
        eventTime: new Date(),
        data: {
          documentId: 'test-doc-123',
          fileName: 'test-document.pdf',
          fileSize: 1024,
          uploadedBy: 'test-user',
          timestamp: new Date().toISOString()
        },
        dataVersion: '1.0'
      },
      {
        id: `test-workflow-${Date.now()}`,
        eventType: 'Workflow.Started',
        subject: 'workflows/test-workflow',
        eventTime: new Date(),
        data: {
          workflowId: 'test-workflow-456',
          workflowType: 'document-approval',
          startedBy: 'test-user',
          timestamp: new Date().toISOString()
        },
        dataVersion: '1.0'
      },
      {
        id: `test-user-${Date.now()}`,
        eventType: 'User.Registered',
        subject: 'users/test-user',
        eventTime: new Date(),
        data: {
          userId: 'test-user-789',
          email: '<EMAIL>',
          registrationMethod: 'email',
          timestamp: new Date().toISOString()
        },
        dataVersion: '1.0'
      }
    ];

    console.log(`📤 Publishing ${testEvents.length} test events...`);
    
    for (const event of testEvents) {
      await client.send([event]);
      console.log(`✅ Published: ${event.eventType} - ${event.subject}`);
      
      // Small delay between events
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    console.log('✅ Event Grid publishing test completed successfully');
    return true;

  } catch (error) {
    console.error('❌ Event Grid publishing test failed:', error.message);
    return false;
  }
}

/**
 * Test Event Grid webhook endpoint
 */
async function testEventGridWebhook() {
  console.log('🧪 Testing Event Grid Webhook Endpoint...');

  try {
    const webhookUrl = `${config.functionApp.url}/api/eventgrid/webhook`;
    
    // Test validation handshake
    const validationEvent = {
      id: `validation-${Date.now()}`,
      eventType: 'Microsoft.EventGrid.SubscriptionValidationEvent',
      subject: '',
      eventTime: new Date().toISOString(),
      data: {
        validationCode: 'test-validation-code-12345'
      },
      dataVersion: '1.0'
    };

    console.log(`📡 Testing webhook endpoint: ${webhookUrl}`);
    
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'aeg-event-type': 'SubscriptionValidation'
      },
      body: JSON.stringify([validationEvent])
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ Webhook validation test passed');
      console.log('📋 Validation response:', result);
      return true;
    } else {
      console.error('❌ Webhook validation test failed:', response.status, response.statusText);
      return false;
    }

  } catch (error) {
    console.error('❌ Webhook test failed:', error.message);
    return false;
  }
}

/**
 * Test custom event publishing endpoint
 */
async function testCustomEventPublishing() {
  console.log('🧪 Testing Custom Event Publishing Endpoint...');

  try {
    const publishUrl = `${config.functionApp.url}/api/eventgrid/publish`;
    
    const customEvent = {
      eventType: 'Test.CustomEvent',
      subject: 'test/custom-event',
      data: {
        message: 'This is a test custom event',
        timestamp: new Date().toISOString(),
        testId: `test-${Date.now()}`
      }
    };

    console.log(`📡 Testing custom event endpoint: ${publishUrl}`);
    
    const response = await fetch(publishUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-functions-key': 'your-function-key-here' // Replace with actual function key
      },
      body: JSON.stringify(customEvent)
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ Custom event publishing test passed');
      console.log('📋 Publish response:', result);
      return true;
    } else {
      console.error('❌ Custom event publishing test failed:', response.status, response.statusText);
      return false;
    }

  } catch (error) {
    console.error('❌ Custom event publishing test failed:', error.message);
    return false;
  }
}

/**
 * Monitor Event Grid metrics
 */
async function monitorEventGridMetrics() {
  console.log('📊 Monitoring Event Grid Metrics...');

  try {
    // This would typically connect to Azure Monitor or Application Insights
    // For now, we'll simulate metrics monitoring
    
    const metrics = {
      totalEvents: Math.floor(Math.random() * 1000),
      successfulEvents: Math.floor(Math.random() * 950),
      failedEvents: Math.floor(Math.random() * 50),
      averageLatency: Math.floor(Math.random() * 500) + 100,
      lastEventTime: new Date().toISOString()
    };

    console.log('📈 Event Grid Metrics:');
    console.log(`   Total Events: ${metrics.totalEvents}`);
    console.log(`   Successful Events: ${metrics.successfulEvents}`);
    console.log(`   Failed Events: ${metrics.failedEvents}`);
    console.log(`   Success Rate: ${((metrics.successfulEvents / metrics.totalEvents) * 100).toFixed(2)}%`);
    console.log(`   Average Latency: ${metrics.averageLatency}ms`);
    console.log(`   Last Event: ${metrics.lastEventTime}`);

    return metrics;

  } catch (error) {
    console.error('❌ Failed to monitor Event Grid metrics:', error.message);
    return null;
  }
}

/**
 * Generate load test events
 */
async function generateLoadTestEvents(eventCount = 10) {
  console.log(`🚀 Generating ${eventCount} load test events...`);

  if (!config.eventGrid.endpoint || !config.eventGrid.key) {
    console.error('❌ Event Grid configuration not found');
    return false;
  }

  try {
    const client = new EventGridPublisherClient(
      config.eventGrid.endpoint,
      "EventGrid",
      new AzureKeyCredential(config.eventGrid.key)
    );

    const eventTypes = [
      'Document.Uploaded',
      'Document.Processed',
      'Workflow.Started',
      'Workflow.Completed',
      'User.Registered',
      'System.HealthCheck',
      'Performance.Alert'
    ];

    const events = [];
    for (let i = 0; i < eventCount; i++) {
      const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)];
      
      events.push({
        id: `load-test-${Date.now()}-${i}`,
        eventType: eventType,
        subject: `load-test/${eventType.toLowerCase()}/${i}`,
        eventTime: new Date(),
        data: {
          loadTestId: `load-test-${Date.now()}`,
          eventNumber: i,
          timestamp: new Date().toISOString(),
          randomData: Math.random().toString(36).substring(2, 15)
        },
        dataVersion: '1.0'
      });
    }

    // Send events in batches of 10
    const batchSize = 10;
    for (let i = 0; i < events.length; i += batchSize) {
      const batch = events.slice(i, i + batchSize);
      await client.send(batch);
      console.log(`📤 Sent batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(events.length / batchSize)}`);
      
      // Small delay between batches
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log(`✅ Load test completed - ${eventCount} events sent`);
    return true;

  } catch (error) {
    console.error('❌ Load test failed:', error.message);
    return false;
  }
}

/**
 * Main test runner
 */
async function runEventGridTests() {
  console.log('🎯 Starting Event Grid Tests...\n');

  const results = {
    publishing: false,
    webhook: false,
    customPublishing: false,
    loadTest: false
  };

  // Test Event Grid publishing
  results.publishing = await testEventGridPublishing();
  console.log('');

  // Test webhook endpoint
  results.webhook = await testEventGridWebhook();
  console.log('');

  // Test custom event publishing
  results.customPublishing = await testCustomEventPublishing();
  console.log('');

  // Monitor metrics
  await monitorEventGridMetrics();
  console.log('');

  // Run load test
  results.loadTest = await generateLoadTestEvents(20);
  console.log('');

  // Summary
  console.log('📋 Test Results Summary:');
  console.log(`   Event Grid Publishing: ${results.publishing ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   Webhook Endpoint: ${results.webhook ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   Custom Publishing: ${results.customPublishing ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   Load Test: ${results.loadTest ? '✅ PASS' : '❌ FAIL'}`);

  const passCount = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n🎯 Overall: ${passCount}/${totalTests} tests passed`);

  if (passCount === totalTests) {
    console.log('🎉 All Event Grid tests passed!');
  } else {
    console.log('⚠️ Some Event Grid tests failed. Check configuration and try again.');
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runEventGridTests().catch(console.error);
}

module.exports = {
  testEventGridPublishing,
  testEventGridWebhook,
  testCustomEventPublishing,
  monitorEventGridMetrics,
  generateLoadTestEvents,
  runEventGridTests
};

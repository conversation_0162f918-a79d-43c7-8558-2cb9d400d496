/**
 * Notification Mark Read Function
 * Handles marking notifications as read/unread
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Validation schemas
const markReadSchema = Joi.object({
  notificationIds: Joi.array().items(Joi.string().uuid()).min(1).optional(),
  markAll: Joi.boolean().default(false),
  isRead: Joi.boolean().default(true),
  organizationId: Joi.string().uuid().optional(),
  projectId: Joi.string().uuid().optional()
}).or('notificationIds', 'markAll');

/**
 * Mark notifications as read handler
 */
export async function markNotificationsRead(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Mark notifications read started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = markReadSchema.validate(body);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { notificationIds, markAll, isRead, organizationId, projectId } = value;

    let updatedCount = 0;
    let notificationsToUpdate: any[] = [];

    if (markAll) {
      // Mark all notifications as read for the user
      let queryText = 'SELECT * FROM c WHERE c.recipientId = @userId AND c.isRead != @isRead';
      const parameters: any[] = [user.id, isRead];

      // Add tenant isolation
      if (user.tenantId) {
        queryText += ' AND (c.tenantId = @tenantId OR c.tenantId IS NULL)';
        parameters.push(user.tenantId);
      }

      // Filter by organization if provided
      if (organizationId) {
        queryText += ' AND c.organizationId = @organizationId';
        parameters.push(organizationId);
      }

      // Filter by project if provided
      if (projectId) {
        queryText += ' AND c.projectId = @projectId';
        parameters.push(projectId);
      }

      // Only include non-expired notifications
      queryText += ' AND (c.expiresAt IS NULL OR c.expiresAt > @now)';
      parameters.push(new Date().toISOString());

      notificationsToUpdate = await db.queryItems('notifications', queryText, parameters);

    } else if (notificationIds && notificationIds.length > 0) {
      // Mark specific notifications as read
      for (const notificationId of notificationIds) {
        const notification = await db.readItem('notifications', notificationId, notificationId);
        
        if (notification && (notification as any).recipientId === user.id) {
          notificationsToUpdate.push(notification);
        }
      }
    }

    // Update notifications
    for (const notification of notificationsToUpdate) {
      const updatedNotification = {
        ...(notification as any),
        id: (notification as any).id,
        isRead,
        readAt: isRead ? new Date().toISOString() : undefined
      };

      await db.updateItem('notifications', updatedNotification);
      updatedCount++;
    }

    // Create activity record if any notifications were updated
    if (updatedCount > 0) {
      await db.createItem('activities', {
        id: uuidv4(),
        type: "notifications_marked_read",
        userId: user.id,
        organizationId,
        projectId,
        timestamp: new Date().toISOString(),
        details: {
          updatedCount,
          isRead,
          markAll
        },
        tenantId: user.tenantId
      });
    }

    logger.info("Notifications marked read successfully", {
      correlationId,
      userId: user.id,
      updatedCount,
      isRead,
      markAll
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        updatedCount,
        message: `${updatedCount} notifications marked as ${isRead ? 'read' : 'unread'}`
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Mark notifications read failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Get notification by ID handler
 */
export async function getNotification(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const notificationId = request.params.notificationId;

  if (!notificationId) {
    return addCorsHeaders({
      status: 400,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: 'Notification ID is required' }
    }, request);
  }

  logger.info("Get notification started", { correlationId, notificationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Get notification
    const notification = await db.readItem('notifications', notificationId, notificationId);
    if (!notification) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Notification not found" }
      }, request);
    }

    // Check if user is the recipient
    if ((notification as any).recipientId !== user.id) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied" }
      }, request);
    }

    // Automatically mark as read when viewed
    if (!(notification as any).isRead) {
      const updatedNotification = {
        ...(notification as any),
        id: notificationId,
        isRead: true,
        readAt: new Date().toISOString()
      };

      await db.updateItem('notifications', updatedNotification);
    }

    // Get sender information
    let senderName = 'System';
    let senderEmail = '';

    if ((notification as any).senderId) {
      try {
        const sender = await db.readItem('users', (notification as any).senderId, (notification as any).senderId);
        if (sender) {
          senderName = (sender as any).name || (sender as any).email || 'Unknown User';
          senderEmail = (sender as any).email || '';
        }
      } catch (error) {
        // Sender might not exist anymore
      }
    }

    const enrichedNotification = {
      ...(notification as any),
      senderName,
      senderEmail,
      isExpired: (notification as any).expiresAt ? new Date((notification as any).expiresAt) < new Date() : false
    };

    logger.info("Notification retrieved successfully", {
      correlationId,
      notificationId,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: enrichedNotification
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get notification failed", {
      correlationId,
      notificationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

// Register functions
app.http('notification-mark-read', {
  methods: ['PATCH', 'OPTIONS'],
  authLevel: 'function',
  route: 'notifications/mark-read',
  handler: markNotificationsRead
});

app.http('notification-get', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'notifications/{notificationId}',
  handler: getNotification
});

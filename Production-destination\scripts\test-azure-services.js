/**
 * Test script for Azure Services configuration
 * Tests Service Bus and Event Grid connectivity
 */

const { ServiceBusClient } = require('@azure/service-bus');
const { EventGridPublisherClient, AzureKeyCredential } = require('@azure/eventgrid');
const fs = require('fs');
const path = require('path');

// Load configuration from local.settings.json
let localSettings = {};
try {
  const settingsPath = path.join(__dirname, '..', 'local.settings.json');
  const settingsContent = fs.readFileSync(settingsPath, 'utf8');
  localSettings = JSON.parse(settingsContent);
  console.log('✅ Loaded configuration from local.settings.json');
} catch (error) {
  console.error('❌ Failed to load local.settings.json:', error.message);
  process.exit(1);
}

// Configuration
const config = {
  serviceBus: {
    connectionString: localSettings.Values?.SERVICE_BUS_CONNECTION_STRING || process.env.SERVICE_BUS_CONNECTION_STRING,
    topics: [
      { name: 'analytics-events', subscription: 'analytics-aggregator' },
      { name: 'document-collaboration', subscription: 'collaboration-processor' },
      { name: 'monitoring-events', subscription: 'system-monitor' }
    ],
    queues: ['workflow-orchestration']
  },
  eventGrid: {
    endpoint: localSettings.Values?.EVENT_GRID_TOPIC_ENDPOINT || process.env.EVENT_GRID_TOPIC_ENDPOINT,
    key: localSettings.Values?.EVENT_GRID_TOPIC_KEY || process.env.EVENT_GRID_TOPIC_KEY
  }
};

async function testServiceBus() {
  console.log('🧪 Testing Service Bus configuration...');

  if (!config.serviceBus.connectionString) {
    console.error('❌ Service Bus connection string not found');
    return false;
  }

  try {
    const client = new ServiceBusClient(config.serviceBus.connectionString);

    // Test topics and subscriptions
    for (const topic of config.serviceBus.topics) {
      console.log(`📤 Testing topic: ${topic.name}`);

      try {
        const sender = client.createSender(topic.name);

        // Send test message
        await sender.sendMessages({
          body: JSON.stringify({
            test: true,
            timestamp: new Date().toISOString(),
            message: `Test message for ${topic.name}`
          }),
          messageId: `test-${Date.now()}`
        });

        console.log(`✅ Successfully sent message to topic: ${topic.name}`);
        await sender.close();

      } catch (error) {
        console.error(`❌ Failed to send message to topic ${topic.name}:`, error.message);
        return false;
      }
    }

    // Test queues
    for (const queueName of config.serviceBus.queues) {
      console.log(`📤 Testing queue: ${queueName}`);

      try {
        const sender = client.createSender(queueName);

        // Send test message
        await sender.sendMessages({
          body: JSON.stringify({
            test: true,
            timestamp: new Date().toISOString(),
            message: `Test message for ${queueName}`
          }),
          messageId: `test-${Date.now()}`
        });

        console.log(`✅ Successfully sent message to queue: ${queueName}`);
        await sender.close();

      } catch (error) {
        console.error(`❌ Failed to send message to queue ${queueName}:`, error.message);
        return false;
      }
    }

    await client.close();
    console.log('✅ Service Bus test completed successfully');
    return true;

  } catch (error) {
    console.error('❌ Service Bus test failed:', error.message);
    return false;
  }
}

async function testEventGrid() {
  console.log('🧪 Testing Event Grid configuration...');

  if (!config.eventGrid.endpoint || !config.eventGrid.key) {
    console.error('❌ Event Grid configuration not found');
    return false;
  }

  try {
    const client = new EventGridPublisherClient(
      config.eventGrid.endpoint,
      "EventGrid",
      new AzureKeyCredential(config.eventGrid.key)
    );

    // Send test event
    const testEvent = {
      id: `test-${Date.now()}`,
      eventType: 'Test.ConfigurationCheck',
      subject: 'test/configuration',
      eventTime: new Date(),
      data: {
        test: true,
        timestamp: new Date().toISOString(),
        message: 'Test event for configuration verification'
      },
      dataVersion: '1.0'
    };

    await client.send([testEvent]);
    console.log('✅ Event Grid test completed successfully');
    return true;

  } catch (error) {
    console.error('❌ Event Grid test failed:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting Azure Services configuration tests...\n');

  const serviceBusResult = await testServiceBus();
  console.log('');

  const eventGridResult = await testEventGrid();
  console.log('');

  if (serviceBusResult && eventGridResult) {
    console.log('🎉 All tests passed! Azure Services are properly configured.');
  } else {
    console.log('❌ Some tests failed. Please check the configuration.');
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testServiceBus, testEventGrid };

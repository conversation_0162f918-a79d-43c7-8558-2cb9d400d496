/**
 * Data Export Function
 * Handles data export operations for various formats and sources
 * Migrated from old-arch/src/analytics-service/export/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { BlobServiceClient } from "@azure/storage-blob";
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { eventService } from '../shared/services/event';

// Export types and enums
enum ExportFormat {
  CSV = 'CSV',
  EXCEL = 'EXCEL',
  JSON = 'JSON',
  PDF = 'PDF',
  XML = 'XML'
}

enum ExportType {
  DOCUMENTS = 'DOCUMENTS',
  USERS = 'USERS',
  ACTIVITIES = 'ACTIVITIES',
  WORKFLOWS = 'WORKFLOWS',
  ANALYTICS = 'ANALYTICS',
  AUDIT_LOGS = 'AUDIT_LOGS',
  CUSTOM = 'CUSTOM'
}

enum ExportStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  EXPIRED = 'EXPIRED'
}

// Validation schemas
const createExportSchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  type: Joi.string().valid(...Object.values(ExportType)).required(),
  format: Joi.string().valid(...Object.values(ExportFormat)).required(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional(),
  filters: Joi.object({
    dateRange: Joi.object({
      startDate: Joi.string().isoDate().required(),
      endDate: Joi.string().isoDate().required()
    }).optional(),
    userIds: Joi.array().items(Joi.string().uuid()).optional(),
    documentTypes: Joi.array().items(Joi.string()).optional(),
    categories: Joi.array().items(Joi.string()).optional(),
    tags: Joi.array().items(Joi.string()).optional(),
    status: Joi.array().items(Joi.string()).optional(),
    includeDeleted: Joi.boolean().default(false),
    includeArchived: Joi.boolean().default(false)
  }).optional(),
  options: Joi.object({
    includeMetadata: Joi.boolean().default(true),
    includeContent: Joi.boolean().default(false),
    includeVersions: Joi.boolean().default(false),
    includeComments: Joi.boolean().default(false),
    includeActivities: Joi.boolean().default(false),
    compression: Joi.boolean().default(false),
    password: Joi.string().min(8).optional(),
    maxRecords: Joi.number().min(1).max(100000).optional()
  }).optional(),
  schedule: Joi.object({
    enabled: Joi.boolean().default(false),
    frequency: Joi.string().valid('daily', 'weekly', 'monthly').optional(),
    dayOfWeek: Joi.number().min(0).max(6).optional(),
    dayOfMonth: Joi.number().min(1).max(31).optional(),
    time: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional()
  }).optional()
});

interface CreateExportRequest {
  name: string;
  type: ExportType;
  format: ExportFormat;
  organizationId: string;
  projectId?: string;
  filters?: {
    dateRange?: {
      startDate: string;
      endDate: string;
    };
    userIds?: string[];
    documentTypes?: string[];
    categories?: string[];
    tags?: string[];
    status?: string[];
    includeDeleted?: boolean;
    includeArchived?: boolean;
  };
  options?: {
    includeMetadata?: boolean;
    includeContent?: boolean;
    includeVersions?: boolean;
    includeComments?: boolean;
    includeActivities?: boolean;
    compression?: boolean;
    password?: string;
    maxRecords?: number;
  };
  schedule?: {
    enabled?: boolean;
    frequency?: string;
    dayOfWeek?: number;
    dayOfMonth?: number;
    time?: string;
  };
}

interface DataExport {
  id: string;
  name: string;
  type: ExportType;
  format: ExportFormat;
  status: ExportStatus;
  organizationId: string;
  projectId?: string;
  filters: any;
  options: any;
  schedule?: any;
  results?: {
    downloadUrl?: string;
    fileSize?: number;
    recordCount?: number;
    generatedAt?: string;
    expiresAt?: string;
  };
  progress?: {
    percentage: number;
    currentStep: string;
    estimatedTimeRemaining?: string;
  };
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  tenantId: string;
}

/**
 * Create data export handler
 */
export async function createDataExport(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create data export started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createExportSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const exportRequest: CreateExportRequest = value;

    // Check organization access
    const hasAccess = await checkOrganizationAccess(exportRequest.organizationId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Check export permissions
    const hasExportPermission = await checkExportPermissions(exportRequest.type, user);
    if (!hasExportPermission) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Insufficient permissions for this export type" }
      }, request);
    }

    // Check export limits
    const canExport = await checkExportLimits(exportRequest.organizationId);
    if (!canExport.allowed) {
      return addCorsHeaders({
        status: 429,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: canExport.reason }
      }, request);
    }

    // Create export
    const exportId = uuidv4();
    const now = new Date().toISOString();

    const dataExport: DataExport = {
      id: exportId,
      name: exportRequest.name,
      type: exportRequest.type,
      format: exportRequest.format,
      status: ExportStatus.PENDING,
      organizationId: exportRequest.organizationId,
      projectId: exportRequest.projectId,
      filters: exportRequest.filters || {},
      options: {
        includeMetadata: true,
        includeContent: false,
        includeVersions: false,
        includeComments: false,
        includeActivities: false,
        compression: false,
        ...exportRequest.options
      },
      schedule: exportRequest.schedule,
      progress: {
        percentage: 0,
        currentStep: 'Initializing'
      },
      createdBy: user.id,
      createdAt: now,
      updatedAt: now,
      tenantId: user.tenantId || user.id
    };

    await db.createItem('data-exports', dataExport);

    // Start export processing asynchronously
    await processExportAsync(dataExport);

    // Create audit log entry
    await db.createItem('audit-logs', {
      id: uuidv4(),
      eventType: 'DATA_EXPORTED',
      userId: user.id,
      organizationId: exportRequest.organizationId,
      description: `Data export created: ${exportRequest.name}`,
      severity: 'MEDIUM',
      resourceType: 'data-export',
      resourceId: exportId,
      details: {
        exportType: exportRequest.type,
        exportFormat: exportRequest.format,
        includeContent: exportRequest.options?.includeContent || false
      },
      timestamp: now,
      ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
      tenantId: user.tenantId
    });

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "data_export_created",
      userId: user.id,
      organizationId: exportRequest.organizationId,
      projectId: exportRequest.projectId,
      timestamp: now,
      details: {
        exportId,
        exportName: exportRequest.name,
        exportType: exportRequest.type,
        exportFormat: exportRequest.format,
        isScheduled: !!exportRequest.schedule?.enabled
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'DataExportCreated',
      aggregateId: exportId,
      aggregateType: 'DataExport',
      version: 1,
      data: {
        export: dataExport,
        createdBy: user.id
      },
      userId: user.id,
      organizationId: exportRequest.organizationId,
      tenantId: user.tenantId
    });

    logger.info("Data export created successfully", {
      correlationId,
      exportId,
      exportName: exportRequest.name,
      exportType: exportRequest.type,
      createdBy: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: exportId,
        name: exportRequest.name,
        type: exportRequest.type,
        format: exportRequest.format,
        status: ExportStatus.PENDING,
        estimatedDuration: estimateExportDuration(exportRequest.type, exportRequest.options),
        createdAt: now,
        message: "Data export created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create data export failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Get export status handler
 */
export async function getExportStatus(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const exportId = request.params.exportId;

  logger.info("Get export status started", { correlationId, exportId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    if (!exportId) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Export ID is required" }
      }, request);
    }

    // Get export
    const dataExport = await db.readItem('data-exports', exportId, exportId);
    if (!dataExport) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Export not found" }
      }, request);
    }

    const exportData = dataExport as any;

    // Check access
    const hasAccess = await checkOrganizationAccess(exportData.organizationId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to export" }
      }, request);
    }

    logger.info("Export status retrieved successfully", {
      correlationId,
      exportId,
      status: exportData.status,
      progress: exportData.progress?.percentage,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: exportData.id,
        name: exportData.name,
        type: exportData.type,
        format: exportData.format,
        status: exportData.status,
        progress: exportData.progress,
        results: exportData.results,
        createdAt: exportData.createdAt,
        updatedAt: exportData.updatedAt
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get export status failed", {
      correlationId,
      exportId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkOrganizationAccess(organizationId: string, userId: string): Promise<boolean> {
  try {
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
    return memberships.length > 0;
  } catch (error) {
    logger.error('Failed to check organization access', { error, organizationId, userId });
    return false;
  }
}

async function checkExportPermissions(exportType: ExportType, user: any): Promise<boolean> {
  try {
    // Check if user has export permissions based on type
    const sensitiveExports = [ExportType.AUDIT_LOGS, ExportType.USERS];

    if (sensitiveExports.includes(exportType)) {
      return user.roles?.includes('admin') || user.roles?.includes('auditor');
    }

    return true; // Allow other export types for all authenticated users
  } catch (error) {
    logger.error('Failed to check export permissions', { error, exportType, userId: user.id });
    return false;
  }
}

async function checkExportLimits(organizationId: string): Promise<{ allowed: boolean; reason?: string }> {
  try {
    // Get organization to check tier
    const organization = await db.readItem('organizations', organizationId, organizationId);
    if (!organization) {
      return { allowed: false, reason: 'Organization not found' };
    }

    const orgData = organization as any;
    const tier = orgData.tier || 'FREE';

    // Define tier limits
    const limits: { [key: string]: { maxExportsPerMonth: number; maxConcurrentExports: number } } = {
      'FREE': { maxExportsPerMonth: 5, maxConcurrentExports: 1 },
      'PROFESSIONAL': { maxExportsPerMonth: 50, maxConcurrentExports: 3 },
      'ENTERPRISE': { maxExportsPerMonth: -1, maxConcurrentExports: 10 } // Unlimited monthly
    };

    const limit = limits[tier] || limits['FREE'];

    // Check concurrent exports
    const activeExportsQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.status IN (@processing, @pending)';
    const activeExportsResult = await db.queryItems('data-exports', activeExportsQuery, [organizationId, ExportStatus.PROCESSING, ExportStatus.PENDING]);
    const activeExports = Number(activeExportsResult[0]) || 0;

    if (activeExports >= limit.maxConcurrentExports) {
      return {
        allowed: false,
        reason: `Maximum concurrent exports limit reached (${limit.maxConcurrentExports})`
      };
    }

    if (limit.maxExportsPerMonth === -1) {
      return { allowed: true };
    }

    // Check monthly usage
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const monthlyUsageQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.createdAt >= @startDate';
    const usageResult = await db.queryItems('data-exports', monthlyUsageQuery, [organizationId, startOfMonth.toISOString()]);
    const monthlyUsage = Number(usageResult[0]) || 0;

    if (monthlyUsage >= limit.maxExportsPerMonth) {
      return {
        allowed: false,
        reason: `Monthly export limit reached (${limit.maxExportsPerMonth})`
      };
    }

    return { allowed: true };

  } catch (error) {
    logger.error('Failed to check export limits', { error, organizationId });
    return { allowed: false, reason: 'Failed to check limits' };
  }
}

function estimateExportDuration(exportType: ExportType, options: any): string {
  // Simplified estimation - in production, use historical data
  const baseTimeMinutes = {
    [ExportType.DOCUMENTS]: 10,
    [ExportType.USERS]: 2,
    [ExportType.ACTIVITIES]: 15,
    [ExportType.WORKFLOWS]: 5,
    [ExportType.ANALYTICS]: 8,
    [ExportType.AUDIT_LOGS]: 20,
    [ExportType.CUSTOM]: 12
  };

  let baseTime = baseTimeMinutes[exportType] || 10;

  // Adjust based on options
  if (options?.includeContent) baseTime *= 2;
  if (options?.includeVersions) baseTime *= 1.5;
  if (options?.includeComments) baseTime *= 1.3;
  if (options?.includeActivities) baseTime *= 1.4;

  return `${Math.ceil(baseTime)} minutes`;
}

async function processExportAsync(dataExport: DataExport): Promise<void> {
  try {
    // Update status to processing
    const updatedExport = {
      ...dataExport,
      id: dataExport.id,
      status: ExportStatus.PROCESSING,
      progress: {
        percentage: 10,
        currentStep: 'Collecting data'
      },
      updatedAt: new Date().toISOString()
    };
    await db.updateItem('data-exports', updatedExport);

    // Simulate export processing (in production, this would be actual export generation)
    setTimeout(async () => {
      try {
        const exportData = await generateExportData(dataExport);
        const downloadUrl = await uploadExportToStorage(dataExport, exportData);

        const completedExport = {
          ...updatedExport,
          id: dataExport.id,
          status: ExportStatus.COMPLETED,
          progress: {
            percentage: 100,
            currentStep: 'Completed'
          },
          results: {
            downloadUrl,
            fileSize: exportData.length,
            recordCount: exportData.recordCount || 0,
            generatedAt: new Date().toISOString(),
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
          },
          updatedAt: new Date().toISOString()
        };

        await db.updateItem('data-exports', completedExport);

        logger.info('Export processed successfully', { exportId: dataExport.id });

      } catch (error) {
        const failedExport = {
          ...updatedExport,
          id: dataExport.id,
          status: ExportStatus.FAILED,
          progress: {
            percentage: 0,
            currentStep: 'Failed'
          },
          updatedAt: new Date().toISOString()
        };
        await db.updateItem('data-exports', failedExport);

        logger.error('Export processing failed', { exportId: dataExport.id, error });
      }
    }, 10000); // 10 second delay for demo

  } catch (error) {
    logger.error('Failed to start export processing', { exportId: dataExport.id, error });
  }
}

async function generateExportData(dataExport: DataExport): Promise<any> {
  // Simplified export data generation
  const mockData = {
    exportId: dataExport.id,
    exportName: dataExport.name,
    exportType: dataExport.type,
    generatedAt: new Date().toISOString(),
    data: `Mock export data for ${dataExport.type}`,
    recordCount: Math.floor(Math.random() * 5000) + 100
  };

  return Buffer.from(JSON.stringify(mockData, null, 2));
}

async function uploadExportToStorage(dataExport: DataExport, data: Buffer): Promise<string> {
  try {
    const blobServiceClient = new BlobServiceClient(process.env.AZURE_STORAGE_CONNECTION_STRING || "");
    const containerClient = blobServiceClient.getContainerClient("exports");

    const fileName = `${dataExport.id}-${Date.now()}.${dataExport.format.toLowerCase()}`;
    const blobClient = containerClient.getBlobClient(fileName);

    await blobClient.getBlockBlobClient().upload(data, data.length);

    return blobClient.url;
  } catch (error) {
    logger.error('Failed to upload export to storage', { exportId: dataExport.id, error });
    throw error;
  }
}

// Register functions
app.http('data-export-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'exports',
  handler: createDataExport
});

app.http('data-export-status', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'exports/{exportId}/status',
  handler: getExportStatus
});

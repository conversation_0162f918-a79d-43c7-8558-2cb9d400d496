/**
 * Notification Preferences Management Function
 * Handles advanced notification preference management, templates, and delivery settings
 * Migrated from old-arch/src/notification-service/preferences/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { eventService } from '../shared/services/event';

// Notification preference types and enums
enum NotificationChannel {
  EMAIL = 'email',
  IN_APP = 'in_app',
  PUSH = 'push',
  SMS = 'sms'
}

enum NotificationFrequency {
  IMMEDIATE = 'immediate',
  HOURLY = 'hourly',
  DAILY = 'daily',
  WEEKLY = 'weekly',
  NEVER = 'never'
}

enum NotificationType {
  DOCUMENT_UPLOADED = 'DOCUMENT_UPLOADED',
  DOCUMENT_PROCESSED = 'DOCUMENT_PROCESSED',
  DOCUMENT_PROCESSING_FAILED = 'DOCUMENT_PROCESSING_FAILED',
  DOCUMENT_SHARED = 'DOCUMENT_SHARED',
  DOCUMENT_COMMENTED = 'DOCUMENT_COMMENTED',
  DOCUMENT_APPROVED = 'DOCUMENT_APPROVED',
  DOCUMENT_REJECTED = 'DOCUMENT_REJECTED',
  WORKFLOW_ASSIGNED = 'WORKFLOW_ASSIGNED',
  WORKFLOW_COMPLETED = 'WORKFLOW_COMPLETED',
  WORKFLOW_OVERDUE = 'WORKFLOW_OVERDUE',
  PROJECT_INVITATION = 'PROJECT_INVITATION',
  PROJECT_MEMBER_ADDED = 'PROJECT_MEMBER_ADDED',
  ORGANIZATION_INVITATION = 'ORGANIZATION_INVITATION',
  MENTION = 'MENTION',
  COLLABORATION_STARTED = 'COLLABORATION_STARTED',
  SYSTEM_UPDATE = 'SYSTEM_UPDATE',
  SECURITY_ALERT = 'SECURITY_ALERT'
}

// Validation schemas
const updateNotificationPreferencesSchema = Joi.object({
  channels: Joi.object({
    email: Joi.boolean().optional(),
    in_app: Joi.boolean().optional(),
    push: Joi.boolean().optional(),
    sms: Joi.boolean().optional()
  }).optional(),
  types: Joi.object().pattern(
    Joi.string().valid(...Object.values(NotificationType)),
    Joi.object({
      email: Joi.boolean().optional(),
      in_app: Joi.boolean().optional(),
      push: Joi.boolean().optional(),
      sms: Joi.boolean().optional(),
      enabled: Joi.boolean().optional()
    })
  ).optional(),
  frequency: Joi.object({
    digest: Joi.string().valid(...Object.values(NotificationFrequency)).optional(),
    summary: Joi.string().valid(...Object.values(NotificationFrequency)).optional(),
    reminders: Joi.string().valid(...Object.values(NotificationFrequency)).optional()
  }).optional(),
  quietHours: Joi.object({
    enabled: Joi.boolean().optional(),
    start: Joi.string().pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
    end: Joi.string().pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
    timezone: Joi.string().optional(),
    weekendsOnly: Joi.boolean().optional()
  }).optional(),
  delivery: Joi.object({
    maxEmailsPerHour: Joi.number().min(1).max(100).optional(),
    maxPushPerHour: Joi.number().min(1).max(100).optional(),
    batchSimilarNotifications: Joi.boolean().optional(),
    priorityOverride: Joi.boolean().optional()
  }).optional(),
  categories: Joi.object({
    documents: Joi.boolean().optional(),
    workflows: Joi.boolean().optional(),
    projects: Joi.boolean().optional(),
    collaboration: Joi.boolean().optional(),
    system: Joi.boolean().optional(),
    security: Joi.boolean().optional()
  }).optional()
});

interface NotificationPreferences {
  channels: {
    email: boolean;
    in_app: boolean;
    push: boolean;
    sms: boolean;
  };
  types: {
    [key in NotificationType]: {
      email: boolean;
      in_app: boolean;
      push: boolean;
      sms: boolean;
      enabled: boolean;
    };
  };
  frequency: {
    digest: NotificationFrequency;
    summary: NotificationFrequency;
    reminders: NotificationFrequency;
  };
  quietHours: {
    enabled: boolean;
    start: string;
    end: string;
    timezone: string;
    weekendsOnly: boolean;
  };
  delivery: {
    maxEmailsPerHour: number;
    maxPushPerHour: number;
    batchSimilarNotifications: boolean;
    priorityOverride: boolean;
  };
  categories: {
    documents: boolean;
    workflows: boolean;
    projects: boolean;
    collaboration: boolean;
    system: boolean;
    security: boolean;
  };
}

/**
 * Get notification preferences handler
 */
export async function getNotificationPreferences(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Get notification preferences started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Get user's notification preferences
    let preferences = await db.readItem('notification-preferences', user.id, user.id);

    if (!preferences) {
      // Return default preferences if none exist
      preferences = getDefaultNotificationPreferences(user.id);
    }

    const preferencesData = preferences as any;

    logger.info("Notification preferences retrieved successfully", {
      correlationId,
      userId: user.id,
      hasCustomPreferences: !!preferences
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        userId: user.id,
        preferences: preferencesData,
        lastUpdated: preferencesData.updatedAt,
        isDefault: !preferences
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get notification preferences failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Update notification preferences handler
 */
export async function updateNotificationPreferences(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Update notification preferences started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = updateNotificationPreferencesSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const preferenceUpdates = value;

    // Get current preferences or create default
    let currentPreferences = await db.readItem('notification-preferences', user.id, user.id);
    if (!currentPreferences) {
      currentPreferences = getDefaultNotificationPreferences(user.id);
    }

    const currentData = currentPreferences as any;
    const now = new Date().toISOString();

    // Deep merge preferences
    const updatedPreferences = {
      ...currentData,
      ...deepMergePreferences(currentData, preferenceUpdates),
      userId: user.id,
      updatedAt: now,
      updatedBy: user.id,
      tenantId: user.tenantId
    };

    await db.upsertItem('notification-preferences', updatedPreferences);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "notification_preferences_updated",
      userId: user.id,
      timestamp: now,
      details: {
        updatedFields: Object.keys(preferenceUpdates),
        channelsEnabled: Object.entries(updatedPreferences.channels || {})
          .filter(([_, enabled]) => enabled)
          .map(([channel, _]) => channel),
        quietHoursEnabled: updatedPreferences.quietHours?.enabled || false
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'NotificationPreferencesUpdated',
      aggregateId: user.id,
      aggregateType: 'NotificationPreferences',
      version: 1,
      data: {
        userId: user.id,
        updatedFields: Object.keys(preferenceUpdates),
        previousPreferences: currentData,
        newPreferences: updatedPreferences
      },
      userId: user.id,
      tenantId: user.tenantId
    });

    logger.info("Notification preferences updated successfully", {
      correlationId,
      userId: user.id,
      updatedFields: Object.keys(preferenceUpdates)
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        userId: user.id,
        preferences: updatedPreferences,
        updatedFields: Object.keys(preferenceUpdates),
        updatedAt: now,
        message: "Notification preferences updated successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Update notification preferences failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Reset notification preferences to defaults handler
 */
export async function resetNotificationPreferences(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Reset notification preferences started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;
    const now = new Date().toISOString();

    // Create default preferences
    const defaultPreferences = getDefaultNotificationPreferences(user.id);
    defaultPreferences.updatedAt = now;
    defaultPreferences.updatedBy = user.id;

    await db.upsertItem('notification-preferences', defaultPreferences);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "notification_preferences_reset",
      userId: user.id,
      timestamp: now,
      details: {
        resetToDefaults: true
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'NotificationPreferencesReset',
      aggregateId: user.id,
      aggregateType: 'NotificationPreferences',
      version: 1,
      data: {
        userId: user.id,
        resetToDefaults: true,
        defaultPreferences
      },
      userId: user.id,
      tenantId: user.tenantId
    });

    logger.info("Notification preferences reset successfully", {
      correlationId,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        userId: user.id,
        preferences: defaultPreferences,
        resetAt: now,
        message: "Notification preferences reset to defaults successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Reset notification preferences failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Get default notification preferences
 */
function getDefaultNotificationPreferences(userId: string): any {
  const now = new Date().toISOString();

  return {
    id: userId,
    userId,
    channels: {
      email: true,
      in_app: true,
      push: true,
      sms: false
    },
    types: Object.values(NotificationType).reduce((acc, type) => {
      acc[type] = {
        email: getDefaultChannelSetting(type, 'email'),
        in_app: getDefaultChannelSetting(type, 'in_app'),
        push: getDefaultChannelSetting(type, 'push'),
        sms: getDefaultChannelSetting(type, 'sms'),
        enabled: true
      };
      return acc;
    }, {} as any),
    frequency: {
      digest: NotificationFrequency.DAILY,
      summary: NotificationFrequency.WEEKLY,
      reminders: NotificationFrequency.IMMEDIATE
    },
    quietHours: {
      enabled: false,
      start: "22:00",
      end: "08:00",
      timezone: "UTC",
      weekendsOnly: false
    },
    delivery: {
      maxEmailsPerHour: 10,
      maxPushPerHour: 20,
      batchSimilarNotifications: true,
      priorityOverride: true
    },
    categories: {
      documents: true,
      workflows: true,
      projects: true,
      collaboration: true,
      system: true,
      security: true
    },
    createdAt: now,
    updatedAt: now,
    tenantId: userId
  };
}

/**
 * Get default channel setting for notification type
 */
function getDefaultChannelSetting(type: NotificationType, channel: string): boolean {
  const highPriorityTypes = [
    NotificationType.WORKFLOW_ASSIGNED,
    NotificationType.WORKFLOW_OVERDUE,
    NotificationType.DOCUMENT_PROCESSING_FAILED,
    NotificationType.SECURITY_ALERT,
    NotificationType.MENTION
  ];

  const emailOnlyTypes = [
    NotificationType.DOCUMENT_PROCESSED,
    NotificationType.SYSTEM_UPDATE
  ];

  const inAppOnlyTypes = [
    NotificationType.DOCUMENT_UPLOADED,
    NotificationType.COLLABORATION_STARTED
  ];

  switch (channel) {
    case 'email':
      return !inAppOnlyTypes.includes(type);
    case 'in_app':
      return true; // All types enabled for in-app
    case 'push':
      return highPriorityTypes.includes(type);
    case 'sms':
      return false; // SMS disabled by default
    default:
      return false;
  }
}

/**
 * Deep merge notification preferences
 */
function deepMergePreferences(current: any, updates: any): any {
  const result = { ...current };

  for (const key in updates) {
    if (updates[key] !== null && typeof updates[key] === 'object' && !Array.isArray(updates[key])) {
      result[key] = deepMergePreferences(current[key] || {}, updates[key]);
    } else {
      result[key] = updates[key];
    }
  }

  return result;
}

// Register functions
app.http('notification-preferences-get', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'notifications/preferences',
  handler: getNotificationPreferences
});

app.http('notification-preferences-update', {
  methods: ['PUT', 'OPTIONS'],
  authLevel: 'function',
  route: 'notifications/preferences/update',
  handler: updateNotificationPreferences
});

app.http('notification-preferences-reset', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'notifications/preferences/reset',
  handler: resetNotificationPreferences
});

/**
 * Organization List Function
 * Handles listing organizations for the authenticated user
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Validation schema
const listOrganizationsSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  search: Joi.string().max(100).optional()
});

/**
 * List organizations handler
 */
export async function listOrganizations(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("List organizations started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate query parameters
    const queryParams = Object.fromEntries(request.query.entries());
    const { error, value } = listOrganizationsSchema.validate(queryParams);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { page, limit, search } = value;

    // First, get organization IDs where user is a member
    const membershipQuery = 'SELECT DISTINCT c.organizationId FROM c WHERE c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, 'active']);

    if (memberships.length === 0) {
      return addCorsHeaders({
        status: 200,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          items: [],
          total: 0,
          page,
          limit,
          hasMore: false
        }
      }, request);
    }

    // Extract organization IDs
    const organizationIds = memberships.map((m: any) => m.organizationId);

    // Build query to get organizations where user is a member
    let queryText = 'SELECT * FROM c WHERE ARRAY_CONTAINS(@orgIds, c.id)';
    const parameters: any[] = [organizationIds];

    // Add tenant isolation
    if (user.tenantId) {
      queryText += ' AND (c.tenantId = @tenantId OR c.createdBy = @userId)';
      parameters.push(user.tenantId, user.id);
    }

    // Add search filter if provided
    if (search) {
      queryText += ' AND (CONTAINS(LOWER(c.name), LOWER(@search)) OR CONTAINS(LOWER(c.description), LOWER(@search)))';
      parameters.push(search);
    }

    // Add ordering
    queryText += ' ORDER BY c.createdAt DESC';

    // Get total count for pagination
    const countQuery = queryText.replace('SELECT *', 'SELECT VALUE COUNT(1)');
    const countResult = await db.queryItems('organizations', countQuery, parameters);
    const total = Number(countResult[0]) || 0;

    // Add pagination
    const offset = (page - 1) * limit;
    const paginatedQuery = `${queryText} OFFSET ${offset} LIMIT ${limit}`;

    // Execute query
    const organizations = await db.queryItems('organizations', paginatedQuery, parameters);

    // Enrich organizations with computed fields
    const enrichedOrganizations = await Promise.all(
      organizations.map(async (org: any) => {
        // Get member count
        const memberCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.status = @status';
        const memberCountResult = await db.queryItems('organization-members', memberCountQuery, [org.id, 'active']);
        const memberCount = Number(memberCountResult[0]) || 0;

        // Get project count
        const projectCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId';
        const projectCountResult = await db.queryItems('projects', projectCountQuery, [org.id]);
        const projectCount = Number(projectCountResult[0]) || 0;

        // Get document count (from projects)
        const documentCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId';
        const documentCountResult = await db.queryItems('documents', documentCountQuery, [org.id]);
        const documentCount = Number(documentCountResult[0]) || 0;

        return {
          ...org,
          memberCount,
          projectCount,
          documentCount,
          storageUsed: 0 // TODO: Calculate actual storage usage
        };
      })
    );

    logger.info("Organizations listed successfully", {
      correlationId,
      userId: user.id,
      count: organizations.length,
      page,
      limit
    });

    // Create response
    const response = {
      items: enrichedOrganizations,
      total,
      page,
      limit,
      hasMore: page * limit < total
    };

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: response
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("List organizations failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

// Note: Registration moved to organization-create.ts for combined handling
// app.http('organization-list', {
//   methods: ['GET', 'OPTIONS'],
//   authLevel: 'function',
//   route: 'organizations',
//   handler: listOrganizations
// });

/**
 * Authentication utilities for Azure Functions
 * Handles JWT token validation and user context extraction
 */

import { HttpRequest } from '@azure/functions';
import * as jwt from 'jsonwebtoken';
import { logger } from './logger';

export interface UserContext {
  id: string;
  email: string;
  name?: string;
  tenantId?: string;
  organizationId?: string;
  roles?: string[];
  permissions?: string[];
}

export interface AuthResult {
  success: boolean;
  user?: UserContext;
  error?: string;
}

/**
 * Extract and validate JWT token from request
 */
export function extractToken(request: HttpRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader) {
    return null;
  }

  if (authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  return null;
}

/**
 * Validate JWT token and extract user context
 */
export async function validateToken(token: string): Promise<AuthResult> {
  try {
    // For development, we'll use a simple validation
    // In production, this should validate against your identity provider
    const decoded = jwt.decode(token) as any;

    if (!decoded) {
      return {
        success: false,
        error: 'Invalid token format'
      };
    }

    // Extract user information from token
    const user: UserContext = {
      id: decoded.sub || decoded.userId || decoded.id,
      email: decoded.email || decoded.preferred_username,
      name: decoded.name || decoded.given_name || decoded.family_name,
      tenantId: decoded.tenantId || decoded.tid,
      organizationId: decoded.organizationId || decoded.orgId,
      roles: decoded.roles || [],
      permissions: decoded.permissions || []
    };

    if (!user.id || !user.email) {
      return {
        success: false,
        error: 'Token missing required user information'
      };
    }

    return {
      success: true,
      user
    };
  } catch (error) {
    logger.error('Token validation failed', { error: error instanceof Error ? error.message : String(error) });
    return {
      success: false,
      error: 'Token validation failed'
    };
  }
}

/**
 * Authenticate request and extract user context
 */
export async function authenticateRequest(request: HttpRequest): Promise<AuthResult> {
  const token = extractToken(request);

  if (!token) {
    return {
      success: false,
      error: 'No authentication token provided'
    };
  }

  return await validateToken(token);
}

/**
 * Check if user has required role
 */
export function hasRole(user: UserContext, requiredRole: string): boolean {
  return user.roles?.includes(requiredRole) || false;
}

/**
 * Check if user has required permission
 */
export function hasPermission(user: UserContext, requiredPermission: string): boolean {
  return user.permissions?.includes(requiredPermission) || false;
}

/**
 * Create authentication middleware
 */
export function requireAuth(handler: (request: HttpRequest, context: any, user: UserContext) => Promise<any>) {
  return async (request: HttpRequest, context: any) => {
    const authResult = await authenticateRequest(request);

    if (!authResult.success || !authResult.user) {
      return {
        status: 401,
        headers: {
          'Content-Type': 'application/json'
        },
        jsonBody: {
          error: 'Unauthorized',
          message: authResult.error || 'Authentication required'
        }
      };
    }

    return await handler(request, context, authResult.user);
  };
}

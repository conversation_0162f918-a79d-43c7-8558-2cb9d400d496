/**
 * Subscription Management Function
 * Handles subscription plans, billing, and tier management
 * Migrated from old-arch/src/subscription-service/management/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { eventService } from '../shared/services/event';

// Subscription types and enums
enum SubscriptionTier {
  FREE = 'FREE',
  PROFESSIONAL = 'PROFESSIONAL',
  ENTERPRISE = 'ENTERPRISE',
  CUSTOM = 'CUSTOM'
}

enum SubscriptionStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  CANCELLED = 'CANCELLED',
  EXPIRED = 'EXPIRED',
  SUSPENDED = 'SUSPENDED',
  TRIAL = 'TRIAL'
}

enum BillingCycle {
  MONTHLY = 'MONTHLY',
  YEARLY = 'YEARLY',
  LIFETIME = 'LIFETIME'
}

// Validation schemas
const createSubscriptionSchema = Joi.object({
  organizationId: Joi.string().uuid().required(),
  tier: Joi.string().valid(...Object.values(SubscriptionTier)).required(),
  billingCycle: Joi.string().valid(...Object.values(BillingCycle)).required(),
  startDate: Joi.string().isoDate().optional(),
  trialDays: Joi.number().min(0).max(90).optional(),
  customLimits: Joi.object({
    maxUsers: Joi.number().min(1).optional(),
    maxDocuments: Joi.number().min(1).optional(),
    maxStorage: Joi.number().min(1).optional(), // GB
    maxApiCalls: Joi.number().min(1).optional(),
    maxWorkflows: Joi.number().min(1).optional()
  }).optional(),
  paymentMethodId: Joi.string().optional(),
  couponCode: Joi.string().max(50).optional()
});

const updateSubscriptionSchema = Joi.object({
  subscriptionId: Joi.string().uuid().required(),
  tier: Joi.string().valid(...Object.values(SubscriptionTier)).optional(),
  billingCycle: Joi.string().valid(...Object.values(BillingCycle)).optional(),
  status: Joi.string().valid(...Object.values(SubscriptionStatus)).optional(),
  customLimits: Joi.object({
    maxUsers: Joi.number().min(1).optional(),
    maxDocuments: Joi.number().min(1).optional(),
    maxStorage: Joi.number().min(1).optional(),
    maxApiCalls: Joi.number().min(1).optional(),
    maxWorkflows: Joi.number().min(1).optional()
  }).optional(),
  paymentMethodId: Joi.string().optional()
});

interface CreateSubscriptionRequest {
  organizationId: string;
  tier: SubscriptionTier;
  billingCycle: BillingCycle;
  startDate?: string;
  trialDays?: number;
  customLimits?: {
    maxUsers?: number;
    maxDocuments?: number;
    maxStorage?: number;
    maxApiCalls?: number;
    maxWorkflows?: number;
  };
  paymentMethodId?: string;
  couponCode?: string;
}

interface Subscription {
  id: string;
  organizationId: string;
  tier: SubscriptionTier;
  status: SubscriptionStatus;
  billingCycle: BillingCycle;
  pricing: {
    basePrice: number;
    discountedPrice?: number;
    currency: string;
    taxRate: number;
  };
  limits: {
    maxUsers: number;
    maxDocuments: number;
    maxStorage: number; // GB
    maxApiCalls: number;
    maxWorkflows: number;
    maxIntegrations: number;
    maxBackups: number;
  };
  usage: {
    currentUsers: number;
    currentDocuments: number;
    currentStorage: number;
    currentApiCalls: number;
    currentWorkflows: number;
    lastUpdated: string;
  };
  billing: {
    startDate: string;
    endDate?: string;
    nextBillingDate?: string;
    lastBillingDate?: string;
    paymentMethodId?: string;
    invoiceHistory: string[];
  };
  trial: {
    isTrialActive: boolean;
    trialStartDate?: string;
    trialEndDate?: string;
    trialDaysRemaining?: number;
  };
  features: {
    advancedAnalytics: boolean;
    prioritySupport: boolean;
    customBranding: boolean;
    apiAccess: boolean;
    ssoIntegration: boolean;
    auditLogs: boolean;
    dataExport: boolean;
    customWorkflows: boolean;
  };
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  tenantId: string;
}

/**
 * Create subscription handler
 */
export async function createSubscription(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create subscription started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createSubscriptionSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const subscriptionRequest: CreateSubscriptionRequest = value;

    // Check organization access
    const hasAccess = await checkOrganizationAccess(subscriptionRequest.organizationId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Check if organization already has an active subscription
    const existingSubscription = await getActiveSubscription(subscriptionRequest.organizationId);
    if (existingSubscription) {
      return addCorsHeaders({
        status: 409,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Organization already has an active subscription" }
      }, request);
    }

    // Get tier configuration
    const tierConfig = getTierConfiguration(subscriptionRequest.tier);

    // Apply coupon if provided
    let pricing = tierConfig.pricing[subscriptionRequest.billingCycle];
    if (subscriptionRequest.couponCode) {
      const couponDiscount = await applyCoupon(subscriptionRequest.couponCode, pricing.basePrice);
      if (couponDiscount.valid) {
        pricing.discountedPrice = couponDiscount.discountedPrice;
      }
    }

    // Calculate trial period
    const now = new Date();
    const startDate = subscriptionRequest.startDate ? new Date(subscriptionRequest.startDate) : now;
    const trialDays = subscriptionRequest.trialDays || tierConfig.defaultTrialDays;
    const isTrialActive = trialDays > 0;
    const trialEndDate = isTrialActive ? new Date(startDate.getTime() + trialDays * 24 * 60 * 60 * 1000) : undefined;

    // Create subscription
    const subscriptionId = uuidv4();
    const nowIso = now.toISOString();

    const subscription: Subscription = {
      id: subscriptionId,
      organizationId: subscriptionRequest.organizationId,
      tier: subscriptionRequest.tier,
      status: isTrialActive ? SubscriptionStatus.TRIAL : SubscriptionStatus.ACTIVE,
      billingCycle: subscriptionRequest.billingCycle,
      pricing,
      limits: {
        ...tierConfig.limits,
        ...subscriptionRequest.customLimits
      },
      usage: {
        currentUsers: 0,
        currentDocuments: 0,
        currentStorage: 0,
        currentApiCalls: 0,
        currentWorkflows: 0,
        lastUpdated: nowIso
      },
      billing: {
        startDate: startDate.toISOString(),
        nextBillingDate: isTrialActive ? trialEndDate?.toISOString() : calculateNextBillingDate(startDate, subscriptionRequest.billingCycle),
        paymentMethodId: subscriptionRequest.paymentMethodId,
        invoiceHistory: []
      },
      trial: {
        isTrialActive,
        trialStartDate: isTrialActive ? startDate.toISOString() : undefined,
        trialEndDate: trialEndDate?.toISOString(),
        trialDaysRemaining: isTrialActive ? trialDays : undefined
      },
      features: tierConfig.features,
      createdBy: user.id,
      createdAt: nowIso,
      updatedAt: nowIso,
      tenantId: user.tenantId || user.id
    };

    await db.createItem('subscriptions', subscription);

    // Update organization with subscription info
    await updateOrganizationSubscription(subscriptionRequest.organizationId, subscription);

    // Create billing record if not trial
    if (!isTrialActive && subscriptionRequest.paymentMethodId) {
      await createInitialInvoice(subscription);
    }

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "subscription_created",
      userId: user.id,
      organizationId: subscriptionRequest.organizationId,
      timestamp: nowIso,
      details: {
        subscriptionId,
        tier: subscriptionRequest.tier,
        billingCycle: subscriptionRequest.billingCycle,
        isTrialActive,
        trialDays: trialDays,
        basePrice: pricing.basePrice,
        discountedPrice: pricing.discountedPrice
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'SubscriptionCreated',
      aggregateId: subscriptionId,
      aggregateType: 'Subscription',
      version: 1,
      data: {
        subscription,
        createdBy: user.id
      },
      userId: user.id,
      organizationId: subscriptionRequest.organizationId,
      tenantId: user.tenantId
    });

    logger.info("Subscription created successfully", {
      correlationId,
      subscriptionId,
      organizationId: subscriptionRequest.organizationId,
      tier: subscriptionRequest.tier,
      billingCycle: subscriptionRequest.billingCycle,
      isTrialActive,
      createdBy: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        subscriptionId,
        organizationId: subscriptionRequest.organizationId,
        tier: subscriptionRequest.tier,
        status: subscription.status,
        billingCycle: subscriptionRequest.billingCycle,
        pricing: {
          basePrice: pricing.basePrice,
          discountedPrice: pricing.discountedPrice,
          currency: pricing.currency
        },
        trial: subscription.trial,
        limits: subscription.limits,
        features: subscription.features,
        nextBillingDate: subscription.billing.nextBillingDate,
        createdAt: nowIso,
        message: isTrialActive ? "Trial subscription created successfully" : "Subscription created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create subscription failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Get subscription handler
 */
export async function getSubscription(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const organizationId = request.params.organizationId;

  logger.info("Get subscription started", { correlationId, organizationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    if (!organizationId) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Organization ID is required" }
      }, request);
    }

    // Check organization access
    const hasAccess = await checkOrganizationAccess(organizationId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Get subscription
    const subscription = await getActiveSubscription(organizationId);
    if (!subscription) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "No active subscription found" }
      }, request);
    }

    // Update usage statistics
    const updatedUsage = await updateSubscriptionUsage(subscription);

    logger.info("Subscription retrieved successfully", {
      correlationId,
      subscriptionId: subscription.id,
      organizationId,
      tier: subscription.tier,
      status: subscription.status,
      requestedBy: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        subscription: {
          ...subscription,
          usage: updatedUsage
        },
        retrievedAt: new Date().toISOString()
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get subscription failed", {
      correlationId,
      organizationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkOrganizationAccess(organizationId: string, userId: string): Promise<boolean> {
  try {
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
    return memberships.length > 0;
  } catch (error) {
    logger.error('Failed to check organization access', { error, organizationId, userId });
    return false;
  }
}

async function getActiveSubscription(organizationId: string): Promise<any> {
  try {
    const subscriptionQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.status IN (@active, @trial)';
    const subscriptions = await db.queryItems('subscriptions', subscriptionQuery, [organizationId, SubscriptionStatus.ACTIVE, SubscriptionStatus.TRIAL]);
    return subscriptions.length > 0 ? subscriptions[0] : null;
  } catch (error) {
    logger.error('Failed to get active subscription', { error, organizationId });
    return null;
  }
}

function getTierConfiguration(tier: SubscriptionTier): any {
  const configurations = {
    [SubscriptionTier.FREE]: {
      limits: {
        maxUsers: 3,
        maxDocuments: 100,
        maxStorage: 1, // GB
        maxApiCalls: 1000,
        maxWorkflows: 5,
        maxIntegrations: 2,
        maxBackups: 1
      },
      features: {
        advancedAnalytics: false,
        prioritySupport: false,
        customBranding: false,
        apiAccess: true,
        ssoIntegration: false,
        auditLogs: false,
        dataExport: true,
        customWorkflows: false
      },
      pricing: {
        [BillingCycle.MONTHLY]: { basePrice: 0, currency: 'USD', taxRate: 0 },
        [BillingCycle.YEARLY]: { basePrice: 0, currency: 'USD', taxRate: 0 },
        [BillingCycle.LIFETIME]: { basePrice: 0, currency: 'USD', taxRate: 0 }
      },
      defaultTrialDays: 0
    },
    [SubscriptionTier.PROFESSIONAL]: {
      limits: {
        maxUsers: 25,
        maxDocuments: 10000,
        maxStorage: 100, // GB
        maxApiCalls: 50000,
        maxWorkflows: 50,
        maxIntegrations: 10,
        maxBackups: 10
      },
      features: {
        advancedAnalytics: true,
        prioritySupport: true,
        customBranding: false,
        apiAccess: true,
        ssoIntegration: true,
        auditLogs: true,
        dataExport: true,
        customWorkflows: true
      },
      pricing: {
        [BillingCycle.MONTHLY]: { basePrice: 49, currency: 'USD', taxRate: 0.08 },
        [BillingCycle.YEARLY]: { basePrice: 490, currency: 'USD', taxRate: 0.08 },
        [BillingCycle.LIFETIME]: { basePrice: 1470, currency: 'USD', taxRate: 0.08 }
      },
      defaultTrialDays: 14
    },
    [SubscriptionTier.ENTERPRISE]: {
      limits: {
        maxUsers: -1, // Unlimited
        maxDocuments: -1,
        maxStorage: -1,
        maxApiCalls: -1,
        maxWorkflows: -1,
        maxIntegrations: -1,
        maxBackups: -1
      },
      features: {
        advancedAnalytics: true,
        prioritySupport: true,
        customBranding: true,
        apiAccess: true,
        ssoIntegration: true,
        auditLogs: true,
        dataExport: true,
        customWorkflows: true
      },
      pricing: {
        [BillingCycle.MONTHLY]: { basePrice: 199, currency: 'USD', taxRate: 0.08 },
        [BillingCycle.YEARLY]: { basePrice: 1990, currency: 'USD', taxRate: 0.08 },
        [BillingCycle.LIFETIME]: { basePrice: 5970, currency: 'USD', taxRate: 0.08 }
      },
      defaultTrialDays: 30
    }
  };

  return configurations[tier as keyof typeof configurations] || configurations[SubscriptionTier.FREE];
}

async function applyCoupon(couponCode: string, basePrice: number): Promise<any> {
  try {
    // Simplified coupon system - in production, validate against coupon database
    const mockCoupons: { [key: string]: { discount: number; type: 'percentage' | 'fixed' } } = {
      'WELCOME10': { discount: 10, type: 'percentage' },
      'SAVE20': { discount: 20, type: 'percentage' },
      'FIRST50': { discount: 50, type: 'fixed' }
    };

    const coupon = mockCoupons[couponCode.toUpperCase()];
    if (!coupon) {
      return { valid: false };
    }

    let discountedPrice = basePrice;
    if (coupon.type === 'percentage') {
      discountedPrice = basePrice * (1 - coupon.discount / 100);
    } else {
      discountedPrice = Math.max(0, basePrice - coupon.discount);
    }

    return {
      valid: true,
      discountedPrice: Math.round(discountedPrice * 100) / 100
    };

  } catch (error) {
    logger.error('Failed to apply coupon', { error, couponCode });
    return { valid: false };
  }
}

function calculateNextBillingDate(startDate: Date, billingCycle: BillingCycle): string {
  const nextDate = new Date(startDate);

  switch (billingCycle) {
    case BillingCycle.MONTHLY:
      nextDate.setMonth(nextDate.getMonth() + 1);
      break;
    case BillingCycle.YEARLY:
      nextDate.setFullYear(nextDate.getFullYear() + 1);
      break;
    case BillingCycle.LIFETIME:
      return ''; // No next billing for lifetime
  }

  return nextDate.toISOString();
}

async function updateOrganizationSubscription(organizationId: string, subscription: Subscription): Promise<void> {
  try {
    const organization = await db.readItem('organizations', organizationId, organizationId);
    if (organization) {
      const updatedOrganization = {
        ...organization,
        id: organizationId,
        tier: subscription.tier,
        subscriptionId: subscription.id,
        subscriptionStatus: subscription.status,
        limits: subscription.limits,
        features: subscription.features,
        updatedAt: new Date().toISOString()
      };

      await db.updateItem('organizations', updatedOrganization);
    }
  } catch (error) {
    logger.error('Failed to update organization subscription', { error, organizationId, subscriptionId: subscription.id });
  }
}

async function createInitialInvoice(subscription: Subscription): Promise<void> {
  try {
    // In production, integrate with payment processor to create invoice
    logger.info('Initial invoice created', {
      subscriptionId: subscription.id,
      organizationId: subscription.organizationId,
      amount: subscription.pricing.discountedPrice || subscription.pricing.basePrice
    });
  } catch (error) {
    logger.error('Failed to create initial invoice', { error, subscriptionId: subscription.id });
  }
}

async function updateSubscriptionUsage(subscription: any): Promise<any> {
  try {
    // Get current usage statistics
    const usageStats = await getCurrentUsageStats(subscription.organizationId);

    const updatedUsage = {
      currentUsers: usageStats.userCount || 0,
      currentDocuments: usageStats.documentCount || 0,
      currentStorage: usageStats.storageUsed || 0,
      currentApiCalls: usageStats.apiCallsThisMonth || 0,
      currentWorkflows: usageStats.workflowCount || 0,
      lastUpdated: new Date().toISOString()
    };

    // Update subscription with new usage
    const updatedSubscription = {
      ...subscription,
      id: subscription.id,
      usage: updatedUsage,
      updatedAt: new Date().toISOString()
    };

    await db.updateItem('subscriptions', updatedSubscription);

    return updatedUsage;

  } catch (error) {
    logger.error('Failed to update subscription usage', { error, subscriptionId: subscription.id });
    return subscription.usage;
  }
}

async function getCurrentUsageStats(organizationId: string): Promise<any> {
  try {
    // Get usage statistics from various collections
    const userCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.status = @active';
    const userCount = await db.queryItems('organization-members', userCountQuery, [organizationId, 'ACTIVE']);

    const documentCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId';
    const documentCount = await db.queryItems('documents', documentCountQuery, [organizationId]);

    const workflowCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId';
    const workflowCount = await db.queryItems('workflows', workflowCountQuery, [organizationId]);

    return {
      userCount: Number(userCount[0]) || 0,
      documentCount: Number(documentCount[0]) || 0,
      workflowCount: Number(workflowCount[0]) || 0,
      storageUsed: 0, // Simplified - would calculate actual storage usage
      apiCallsThisMonth: 0 // Simplified - would get from API usage tracking
    };

  } catch (error) {
    logger.error('Failed to get current usage stats', { error, organizationId });
    return {
      userCount: 0,
      documentCount: 0,
      workflowCount: 0,
      storageUsed: 0,
      apiCallsThisMonth: 0
    };
  }
}

// Register functions
app.http('subscription-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'subscriptions',
  handler: createSubscription
});

app.http('subscription-get', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'subscriptions/{organizationId}',
  handler: getSubscription
});

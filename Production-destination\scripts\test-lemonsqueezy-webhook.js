/**
 * Test script for LemonSqueezy webhook handler
 * Tests webhook signature verification and event processing
 */

const crypto = require('crypto');
const axios = require('axios');

// Configuration
const WEBHOOK_URL = 'http://localhost:7071/lemonsqueezy-webhooks';
const WEBHOOK_SECRET = 'lemonmgmtsecret'; // From local.settings.json

/**
 * Create HMAC signature for webhook payload
 */
function createSignature(payload, secret) {
  const hmac = crypto.createHmac('sha256', secret);
  return hmac.update(payload).digest('hex');
}

/**
 * Test webhook with sample order created event
 */
async function testOrderCreatedWebhook() {
  console.log('🧪 Testing Order Created webhook...');

  const payload = {
    meta: {
      event_name: 'order_created',
      test_mode: true,
      webhook_id: 'test-webhook-123'
    },
    data: {
      type: 'orders',
      id: 'test-order-123',
      attributes: {
        store_id: 80076,
        user_name: 'Test User',
        user_email: '<EMAIL>',
        status: 'paid',
        total: 2999,
        subtotal: 2999,
        tax: 0,
        currency: 'USD',
        refunded: false,
        refunded_amount: 0,
        order_number: 'TEST-001',
        variant_id: 'test-variant-123',
        product_id: 'test-product-123',
        custom_data: {
          user_id: 'test-user-123'
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    }
  };

  const payloadString = JSON.stringify(payload);
  const signature = createSignature(payloadString, WEBHOOK_SECRET);

  try {
    const response = await axios.post(WEBHOOK_URL, payloadString, {
      headers: {
        'Content-Type': 'application/json',
        'X-Signature': signature
      }
    });

    console.log('✅ Order Created webhook test passed');
    console.log('Response:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Order Created webhook test failed');
    console.error('Error:', error.response?.data || error.message);
    return false;
  }
}

/**
 * Test webhook with sample subscription created event
 */
async function testSubscriptionCreatedWebhook() {
  console.log('🧪 Testing Subscription Created webhook...');

  const payload = {
    meta: {
      event_name: 'subscription_created',
      test_mode: true,
      webhook_id: 'test-webhook-124'
    },
    data: {
      type: 'subscriptions',
      id: 'test-subscription-123',
      attributes: {
        store_id: 80076,
        user_name: 'Test User',
        user_email: '<EMAIL>',
        status: 'active',
        product_id: 'test-product-123',
        variant_id: 'test-variant-123',
        card_brand: 'visa',
        card_last_four: '4242',
        billing_anchor: 1,
        renews_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        ends_at: null,
        trial_ends_at: null,
        price: 2999,
        is_usage_based: false,
        subscription_item_id: 'test-item-123',
        custom_data: {
          user_id: 'test-user-123'
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    }
  };

  const payloadString = JSON.stringify(payload);
  const signature = createSignature(payloadString, WEBHOOK_SECRET);

  try {
    const response = await axios.post(WEBHOOK_URL, payloadString, {
      headers: {
        'Content-Type': 'application/json',
        'X-Signature': signature
      }
    });

    console.log('✅ Subscription Created webhook test passed');
    console.log('Response:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Subscription Created webhook test failed');
    console.error('Error:', error.response?.data || error.message);
    return false;
  }
}

/**
 * Test webhook with invalid signature
 */
async function testInvalidSignature() {
  console.log('🧪 Testing Invalid Signature webhook...');

  const payload = {
    meta: {
      event_name: 'order_created',
      test_mode: true,
      webhook_id: 'test-webhook-125'
    },
    data: {
      type: 'orders',
      id: 'test-order-124',
      attributes: {
        store_id: 80076,
        user_email: '<EMAIL>',
        status: 'paid',
        total: 1999
      }
    }
  };

  const payloadString = JSON.stringify(payload);
  const invalidSignature = 'invalid-signature';

  try {
    const response = await axios.post(WEBHOOK_URL, payloadString, {
      headers: {
        'Content-Type': 'application/json',
        'X-Signature': invalidSignature
      }
    });

    console.error('❌ Invalid signature test failed - should have been rejected');
    return false;
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('✅ Invalid signature correctly rejected');
      return true;
    } else {
      console.error('❌ Unexpected error for invalid signature test');
      console.error('Error:', error.response?.data || error.message);
      return false;
    }
  }
}

/**
 * Run all webhook tests
 */
async function runAllTests() {
  console.log('🚀 Starting LemonSqueezy webhook tests...\n');

  const tests = [
    { name: 'Order Created', fn: testOrderCreatedWebhook },
    { name: 'Subscription Created', fn: testSubscriptionCreatedWebhook },
    { name: 'Invalid Signature', fn: testInvalidSignature }
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passedTests++;
      }
    } catch (error) {
      console.error(`❌ Test "${test.name}" threw an error:`, error.message);
    }
    console.log(''); // Add spacing between tests
  }

  console.log('📊 Test Results:');
  console.log(`✅ Passed: ${passedTests}/${totalTests}`);
  console.log(`❌ Failed: ${totalTests - passedTests}/${totalTests}`);

  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! LemonSqueezy webhook handler is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Please check the webhook handler implementation.');
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = { runAllTests, testOrderCreatedWebhook, testSubscriptionCreatedWebhook, testInvalidSignature };

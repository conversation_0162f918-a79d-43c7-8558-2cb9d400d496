/**
 * Document Sharing Function
 * Handles document sharing operations (create, read, update, revoke)
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Enums
export enum DocumentSharingStatus {
  ACTIVE = 'ACTIVE',
  REVOKED = 'REVOKED',
  EXPIRED = 'EXPIRED'
}

export enum SharePermission {
  VIEW = 'VIEW',
  COMMENT = 'COMMENT',
  EDIT = 'EDIT',
  DOWNLOAD = 'DOWNLOAD'
}

// Validation schemas
const createShareSchema = Joi.object({
  sharedWith: Joi.string().uuid().required(),
  permissions: Joi.array().items(Joi.string().valid(...Object.values(SharePermission))).required(),
  expiresAt: Joi.date().iso().optional(),
  projectId: Joi.string().uuid().required(),
  organizationId: Joi.string().uuid().required(),
  metadata: Joi.object().optional()
});

const updateShareSchema = Joi.object({
  permissions: Joi.array().items(Joi.string().valid(...Object.values(SharePermission))).optional(),
  expiresAt: Joi.date().iso().optional(),
  status: Joi.string().valid(...Object.values(DocumentSharingStatus)).optional()
});

const getSharesSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  status: Joi.string().valid(...Object.values(DocumentSharingStatus)).optional()
});

interface DocumentSharing {
  id: string;
  documentId: string;
  sharedBy: string;
  sharedWith: string;
  sharedAt: string;
  expiresAt?: string;
  permissions: SharePermission[];
  organizationId: string;
  projectId: string;
  status: DocumentSharingStatus;
  metadata?: any;
  tenantId?: string;
}

/**
 * Create document share handler
 */
export async function createDocumentShare(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const documentId = request.params.documentId;

  if (!documentId) {
    return addCorsHeaders({
      status: 400,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: 'Document ID is required' }
    }, request);
  }

  logger.info("Create document share started", { correlationId, documentId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createShareSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const shareData = value;

    // Verify document exists and user has access
    const document = await db.readItem('documents', documentId, documentId);

    if (!document) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document not found" }
      }, request);
    }

    // Check access permissions
    const hasAccess = (
      (document as any).createdBy === user.id ||
      (document as any).organizationId === user.tenantId ||
      user.roles?.includes('admin')
    );

    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied" }
      }, request);
    }

    // Verify target user exists
    const targetUser = await db.readItem('users', shareData.sharedWith, shareData.sharedWith);

    if (!targetUser) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Target user not found" }
      }, request);
    }

    // Check if sharing already exists
    const existingShareQuery = 'SELECT * FROM c WHERE c.documentId = @documentId AND c.sharedWith = @sharedWith AND c.status = @status';
    const existingShares = await db.queryItems('document-sharing', existingShareQuery, [
      documentId,
      shareData.sharedWith,
      DocumentSharingStatus.ACTIVE
    ]);

    if (existingShares.length > 0) {
      return addCorsHeaders({
        status: 409,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document is already shared with this user" }
      }, request);
    }

    // Create sharing
    const sharingId = uuidv4();
    const sharing: DocumentSharing = {
      id: sharingId,
      documentId,
      sharedBy: user.id,
      sharedWith: shareData.sharedWith,
      sharedAt: new Date().toISOString(),
      expiresAt: shareData.expiresAt,
      permissions: shareData.permissions,
      organizationId: shareData.organizationId,
      projectId: shareData.projectId,
      status: DocumentSharingStatus.ACTIVE,
      metadata: shareData.metadata,
      tenantId: user.tenantId
    };

    await db.createItem('document-sharing', sharing);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "document_shared",
      userId: user.id,
      organizationId: shareData.organizationId,
      projectId: shareData.projectId,
      documentId,
      sharingId,
      timestamp: new Date().toISOString(),
      details: {
        sharedWith: shareData.sharedWith,
        permissions: shareData.permissions,
        expiresAt: shareData.expiresAt
      }
    });

    logger.info("Document share created successfully", {
      correlationId,
      sharingId,
      documentId,
      sharedWith: shareData.sharedWith,
      userId: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: sharing
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create document share failed", {
      correlationId,
      documentId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Get document shares handler
 */
export async function getDocumentShares(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const documentId = request.params.documentId;

  if (!documentId) {
    return addCorsHeaders({
      status: 400,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: 'Document ID is required' }
    }, request);
  }

  logger.info("Get document shares started", { correlationId, documentId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate query parameters
    const queryParams = Object.fromEntries(request.query.entries());
    const { error, value } = getSharesSchema.validate(queryParams);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { page, limit, status } = value;

    // Verify document exists and user has access
    const document = await db.readItem('documents', documentId, documentId);

    if (!document) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document not found" }
      }, request);
    }

    // Check access permissions
    const hasAccess = (
      (document as any).createdBy === user.id ||
      (document as any).organizationId === user.tenantId ||
      user.roles?.includes('admin')
    );

    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied" }
      }, request);
    }

    // Build query
    let query = 'SELECT * FROM c WHERE c.documentId = @documentId';
    const parameters = [documentId];

    // Add status filter if provided
    if (status) {
      query += ' AND c.status = @status';
      parameters.push(status);
    }

    // Add tenant isolation
    if (user.tenantId) {
      query += ' AND (c.organizationId = @tenantId OR c.sharedBy = @userId)';
      parameters.push(user.tenantId, user.id);
    }

    query += ' ORDER BY c.sharedAt DESC';

    // Execute query with pagination
    const offset = (page - 1) * limit;
    const paginatedQuery = `${query} OFFSET ${offset} LIMIT ${limit}`;

    const shares = await db.queryItems('document-sharing', paginatedQuery, parameters);

    // Get total count
    const countQuery = query.replace('SELECT * FROM c', 'SELECT VALUE COUNT(1) FROM c');
    const countResult = await db.queryItems('document-sharing', countQuery, parameters);
    const total = Number(countResult[0]) || 0;

    logger.info("Document shares retrieved successfully", {
      correlationId,
      documentId,
      userId: user.id,
      count: shares.length,
      page,
      limit
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        documentId,
        shares,
        pagination: {
          page,
          limit,
          total,
          hasMore: (page * limit) < total
        }
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get document shares failed", {
      correlationId,
      documentId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

// Register functions
app.http('document-share-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'documents/{documentId}/share',
  handler: createDocumentShare
});

app.http('document-share-get', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'documents/{documentId}/shares',
  handler: getDocumentShares
});

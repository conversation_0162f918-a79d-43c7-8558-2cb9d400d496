/**
 * User Management Functions
 * Handles user profile, preferences, and basic user operations
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as <PERSON><PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// User interfaces
interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  timezone: string;
  dateFormat: string;
  notifications: {
    email: boolean;
    inApp: boolean;
    documentUploaded: boolean;
    documentProcessed: boolean;
    commentAdded: boolean;
    mentionedInComment: boolean;
    projectInvitation: boolean;
    organizationInvitation: boolean;
  };
  dashboard: {
    defaultView: 'recent' | 'projects' | 'documents';
    showWelcomeMessage: boolean;
    pinnedProjects: string[];
    pinnedDocuments: string[];
  };
  documentViewer: {
    defaultZoom: number;
    showAnnotations: boolean;
    showComments: boolean;
    highlightEntities: boolean;
  };
}

interface UserProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  displayName: string;
  avatarUrl?: string;
  tenantId?: string;
  organizationIds: string[];
  roles: string[];
  systemRoles: string[];
  preferences: UserPreferences;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
}

// Validation schemas
const updateProfileSchema = Joi.object({
  firstName: Joi.string().max(50).optional(),
  lastName: Joi.string().max(50).optional(),
  displayName: Joi.string().max(100).optional(),
  avatarUrl: Joi.string().uri().optional()
});

const updatePreferencesSchema = Joi.object({
  theme: Joi.string().valid('light', 'dark', 'system').optional(),
  language: Joi.string().max(10).optional(),
  timezone: Joi.string().max(50).optional(),
  dateFormat: Joi.string().max(20).optional(),
  notifications: Joi.object({
    email: Joi.boolean().optional(),
    inApp: Joi.boolean().optional(),
    documentUploaded: Joi.boolean().optional(),
    documentProcessed: Joi.boolean().optional(),
    commentAdded: Joi.boolean().optional(),
    mentionedInComment: Joi.boolean().optional(),
    projectInvitation: Joi.boolean().optional(),
    organizationInvitation: Joi.boolean().optional()
  }).optional(),
  dashboard: Joi.object({
    defaultView: Joi.string().valid('recent', 'projects', 'documents').optional(),
    showWelcomeMessage: Joi.boolean().optional(),
    pinnedProjects: Joi.array().items(Joi.string().uuid()).optional(),
    pinnedDocuments: Joi.array().items(Joi.string().uuid()).optional()
  }).optional(),
  documentViewer: Joi.object({
    defaultZoom: Joi.number().min(25).max(500).optional(),
    showAnnotations: Joi.boolean().optional(),
    showComments: Joi.boolean().optional(),
    highlightEntities: Joi.boolean().optional()
  }).optional()
});

/**
 * Get default user preferences
 */
function getDefaultPreferences(): UserPreferences {
  return {
    theme: 'system',
    language: 'en',
    timezone: 'UTC',
    dateFormat: 'MM/DD/YYYY',
    notifications: {
      email: true,
      inApp: true,
      documentUploaded: true,
      documentProcessed: true,
      commentAdded: true,
      mentionedInComment: true,
      projectInvitation: true,
      organizationInvitation: true
    },
    dashboard: {
      defaultView: 'recent',
      showWelcomeMessage: true,
      pinnedProjects: [],
      pinnedDocuments: []
    },
    documentViewer: {
      defaultZoom: 100,
      showAnnotations: true,
      showComments: true,
      highlightEntities: true
    }
  };
}

/**
 * Get user profile handler
 */
export async function getUserProfile(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Get user profile started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Get user profile from database
    const userProfile = await db.readItem('users', user.id, user.id);

    if (!userProfile) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "User profile not found" }
      }, request);
    }

    // Ensure preferences exist
    if (!(userProfile as any).preferences) {
      (userProfile as any).preferences = getDefaultPreferences();
    }

    // Remove sensitive fields
    const sanitizedProfile = {
      ...userProfile,
      refreshTokens: undefined,
      _rid: undefined,
      _self: undefined,
      _etag: undefined,
      _attachments: undefined,
      _ts: undefined
    };

    logger.info("User profile retrieved successfully", {
      correlationId,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: sanitizedProfile
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get user profile failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Update user profile handler
 */
export async function updateUserProfile(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Update user profile started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = updateProfileSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const updates = value;

    // Get current user profile
    const userProfile = await db.readItem('users', user.id, user.id);

    if (!userProfile) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "User profile not found" }
      }, request);
    }

    // Update profile fields
    const updatedProfile = {
      ...userProfile,
      ...updates,
      updatedAt: new Date().toISOString()
    };

    // Update display name if first/last name changed
    if (updates.firstName || updates.lastName) {
      updatedProfile.displayName = `${updatedProfile.firstName} ${updatedProfile.lastName}`.trim();
    }

    // Save updated profile
    await db.updateItem('users', updatedProfile);

    // Remove sensitive fields from response
    const sanitizedProfile = {
      ...updatedProfile,
      refreshTokens: undefined,
      _rid: undefined,
      _self: undefined,
      _etag: undefined,
      _attachments: undefined,
      _ts: undefined
    };

    logger.info("User profile updated successfully", {
      correlationId,
      userId: user.id,
      updatedFields: Object.keys(updates)
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: sanitizedProfile
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Update user profile failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Update user preferences handler
 */
export async function updateUserPreferences(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Update user preferences started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = updatePreferencesSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const preferenceUpdates = value;

    // Get current user profile
    const userProfile = await db.readItem('users', user.id, user.id);

    if (!userProfile) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "User profile not found" }
      }, request);
    }

    // Merge existing preferences with updates
    const currentPreferences = (userProfile as any).preferences || getDefaultPreferences();
    const updatedPreferences = {
      ...currentPreferences,
      ...preferenceUpdates,
      // Deep merge nested objects
      notifications: {
        ...currentPreferences.notifications,
        ...preferenceUpdates.notifications
      },
      dashboard: {
        ...currentPreferences.dashboard,
        ...preferenceUpdates.dashboard
      },
      documentViewer: {
        ...currentPreferences.documentViewer,
        ...preferenceUpdates.documentViewer
      }
    };

    // Update user profile with new preferences
    const updatedProfile = {
      ...userProfile,
      id: (userProfile as any).id,
      preferences: updatedPreferences,
      updatedAt: new Date().toISOString()
    };

    await db.updateItem('users', updatedProfile);

    logger.info("User preferences updated successfully", {
      correlationId,
      userId: user.id,
      updatedFields: Object.keys(preferenceUpdates)
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        preferences: updatedPreferences,
        message: "Preferences updated successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Update user preferences failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Combined user profile handler
 */
async function handleUserProfile(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const method = request.method.toUpperCase();

  switch (method) {
    case 'GET':
      return await getUserProfile(request, context);
    case 'PATCH':
      return await updateUserProfile(request, context);
    case 'OPTIONS':
      return handlePreflight(request) || { status: 200 };
    default:
      return addCorsHeaders({
        status: 405,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Method not allowed' }
      }, request);
  }
}

// Register functions
app.http('user-profile', {
  methods: ['GET', 'PATCH', 'OPTIONS'],
  authLevel: 'function',
  route: 'users/profile',
  handler: handleUserProfile
});

app.http('user-profile-preferences-update', {
  methods: ['PATCH', 'OPTIONS'],
  authLevel: 'function',
  route: 'users/profile/preferences',
  handler: updateUserPreferences
});

/**
 * Webhook Delivery Function
 * Handles webhook delivery with retry logic and failure handling
 * Migrated from old-arch/src/integration-service/webhook-delivery/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { eventService } from '../shared/services/event';
import * as crypto from 'crypto';

// Webhook delivery status
enum DeliveryStatus {
  PENDING = 'pending',
  DELIVERED = 'delivered',
  FAILED = 'failed',
  RETRYING = 'retrying',
  ABANDONED = 'abandoned'
}

// Validation schema
const deliverWebhookSchema = Joi.object({
  webhookId: Joi.string().uuid().required(),
  eventType: Joi.string().required(),
  payload: Joi.object().required(),
  deliveryAttempt: Joi.number().min(1).default(1),
  isRetry: Joi.boolean().default(false),
  metadata: Joi.object().optional()
});

interface WebhookDeliveryRequest {
  webhookId: string;
  eventType: string;
  payload: any;
  deliveryAttempt: number;
  isRetry: boolean;
  metadata?: any;
}

interface WebhookDeliveryResponse {
  deliveryId: string;
  webhookId: string;
  eventType: string;
  status: DeliveryStatus;
  deliveryAttempt: number;
  statusCode?: number;
  responseTime: number;
  success: boolean;
  nextRetryAt?: string;
  errorMessage?: string;
}

/**
 * Deliver webhook handler
 */
export async function deliverWebhook(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Webhook delivery started", { correlationId });

  try {
    // Authenticate user (for manual webhook delivery)
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = deliverWebhookSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const deliveryRequest: WebhookDeliveryRequest = value;
    const startTime = Date.now();

    // Get webhook configuration
    const webhook = await db.readItem('webhooks', deliveryRequest.webhookId, deliveryRequest.webhookId);
    if (!webhook) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Webhook not found" }
      }, request);
    }

    const webhookData = webhook as any;

    // Check if webhook is active
    if (!webhookData.isActive) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Webhook is not active" }
      }, request);
    }

    // Check if event type is supported by webhook
    if (!webhookData.events.includes(deliveryRequest.eventType)) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: "Event type not supported by webhook",
          supportedEvents: webhookData.events
        }
      }, request);
    }

    // Perform webhook delivery
    const deliveryResult = await performWebhookDelivery(
      webhookData,
      deliveryRequest.eventType,
      deliveryRequest.payload,
      deliveryRequest.deliveryAttempt
    );

    const responseTime = Date.now() - startTime;

    // Create delivery record
    const deliveryId = uuidv4();
    const now = new Date().toISOString();

    const deliveryRecord = {
      id: deliveryId,
      webhookId: deliveryRequest.webhookId,
      eventType: deliveryRequest.eventType,
      payload: deliveryRequest.payload,
      deliveryAttempt: deliveryRequest.deliveryAttempt,
      isRetry: deliveryRequest.isRetry,
      status: deliveryResult.success ? DeliveryStatus.DELIVERED : DeliveryStatus.FAILED,
      statusCode: deliveryResult.statusCode,
      responseBody: deliveryResult.responseBody,
      responseTime,
      errorMessage: deliveryResult.errorMessage,
      deliveredAt: now,
      nextRetryAt: deliveryResult.success ? null : calculateNextRetryTime(
        deliveryRequest.deliveryAttempt,
        webhookData.retryConfig
      ),
      organizationId: webhookData.organizationId,
      projectId: webhookData.projectId,
      metadata: deliveryRequest.metadata,
      tenantId: user.tenantId
    };

    await db.createItem('webhook-deliveries', deliveryRecord);

    // Update webhook statistics
    const updatedWebhook = {
      ...webhookData,
      statistics: {
        ...webhookData.statistics,
        totalDeliveries: (webhookData.statistics.totalDeliveries || 0) + 1,
        successfulDeliveries: deliveryResult.success
          ? (webhookData.statistics.successfulDeliveries || 0) + 1
          : webhookData.statistics.successfulDeliveries || 0,
        failedDeliveries: !deliveryResult.success
          ? (webhookData.statistics.failedDeliveries || 0) + 1
          : webhookData.statistics.failedDeliveries || 0,
        lastDeliveryAt: now,
        lastSuccessAt: deliveryResult.success ? now : webhookData.statistics.lastSuccessAt,
        lastFailureAt: !deliveryResult.success ? now : webhookData.statistics.lastFailureAt
      },
      updatedAt: now
    };

    await db.updateItem('webhooks', updatedWebhook);

    // Schedule retry if delivery failed and retries are available
    if (!deliveryResult.success && shouldRetry(deliveryRequest.deliveryAttempt, webhookData.retryConfig)) {
      await scheduleWebhookRetry(deliveryRecord, webhookData);
    }

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "webhook_delivered",
      userId: user.id,
      organizationId: webhookData.organizationId,
      projectId: webhookData.projectId,
      timestamp: now,
      details: {
        webhookId: deliveryRequest.webhookId,
        webhookName: webhookData.name,
        eventType: deliveryRequest.eventType,
        deliveryAttempt: deliveryRequest.deliveryAttempt,
        success: deliveryResult.success,
        statusCode: deliveryResult.statusCode,
        responseTime,
        isRetry: deliveryRequest.isRetry
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: deliveryResult.success ? 'WebhookDelivered' : 'WebhookDeliveryFailed',
      aggregateId: deliveryId,
      aggregateType: 'WebhookDelivery',
      version: 1,
      data: {
        delivery: deliveryRecord,
        webhook: webhookData,
        deliveryResult
      },
      userId: user.id,
      organizationId: webhookData.organizationId,
      tenantId: user.tenantId
    });

    const response: WebhookDeliveryResponse = {
      deliveryId,
      webhookId: deliveryRequest.webhookId,
      eventType: deliveryRequest.eventType,
      status: deliveryRecord.status,
      deliveryAttempt: deliveryRequest.deliveryAttempt,
      statusCode: deliveryResult.statusCode,
      responseTime,
      success: deliveryResult.success,
      nextRetryAt: deliveryRecord.nextRetryAt || undefined,
      errorMessage: deliveryResult.errorMessage
    };

    logger.info("Webhook delivery completed", {
      correlationId,
      deliveryId,
      webhookId: deliveryRequest.webhookId,
      eventType: deliveryRequest.eventType,
      success: deliveryResult.success,
      statusCode: deliveryResult.statusCode,
      responseTime,
      deliveryAttempt: deliveryRequest.deliveryAttempt,
      userId: user.id
    });

    return addCorsHeaders({
      status: deliveryResult.success ? 200 : 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: response
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Webhook delivery failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Perform actual webhook delivery
 */
async function performWebhookDelivery(
  webhook: any,
  eventType: string,
  payload: any,
  deliveryAttempt: number
): Promise<{
  success: boolean;
  statusCode?: number;
  responseBody?: string;
  errorMessage?: string;
}> {
  try {
    logger.info("Performing webhook delivery", {
      webhookId: webhook.id,
      url: webhook.url,
      eventType,
      deliveryAttempt
    });

    // Prepare webhook payload
    const webhookPayload = {
      id: uuidv4(),
      event: eventType,
      data: payload,
      timestamp: new Date().toISOString(),
      webhook: {
        id: webhook.id,
        name: webhook.name
      },
      delivery_attempt: deliveryAttempt
    };

    // Generate signature
    const signature = generateWebhookSignature(JSON.stringify(webhookPayload), webhook.secret);

    // Prepare headers
    const headers: { [key: string]: string } = {
      'Content-Type': 'application/json',
      'User-Agent': 'DocuContext-Webhook/1.0',
      'X-Webhook-Signature': signature,
      'X-Webhook-Event': eventType,
      'X-Webhook-Delivery': uuidv4(),
      'X-Webhook-Timestamp': new Date().toISOString(),
      ...webhook.headers
    };

    // Make HTTP request
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), webhook.retryConfig?.timeout || 30000);

    const response = await fetch(webhook.url, {
      method: 'POST',
      headers,
      body: JSON.stringify(webhookPayload),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    const responseBody = await response.text();

    if (response.ok) {
      return {
        success: true,
        statusCode: response.status,
        responseBody: responseBody.substring(0, 1000) // Limit response body size
      };
    } else {
      return {
        success: false,
        statusCode: response.status,
        responseBody: responseBody.substring(0, 1000),
        errorMessage: `HTTP ${response.status}: ${response.statusText}`
      };
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error('Webhook delivery request failed', {
      error: errorMessage,
      webhookId: webhook.id,
      url: webhook.url
    });

    return {
      success: false,
      errorMessage
    };
  }
}

/**
 * Generate webhook signature
 */
function generateWebhookSignature(payload: string, secret: string): string {
  return crypto
    .createHmac('sha256', secret)
    .update(payload, 'utf8')
    .digest('hex');
}

/**
 * Calculate next retry time
 */
function calculateNextRetryTime(deliveryAttempt: number, retryConfig: any): string | null {
  if (!retryConfig || deliveryAttempt >= (retryConfig.maxRetries || 3)) {
    return null;
  }

  const baseDelay = retryConfig.retryDelay || 5000; // 5 seconds default
  const backoffMultiplier = retryConfig.backoffMultiplier || 2;

  // Exponential backoff: delay * (multiplier ^ (attempt - 1))
  const delay = baseDelay * Math.pow(backoffMultiplier, deliveryAttempt - 1);

  // Cap maximum delay at 1 hour
  const maxDelay = 60 * 60 * 1000; // 1 hour
  const actualDelay = Math.min(delay, maxDelay);

  const nextRetryTime = new Date(Date.now() + actualDelay);
  return nextRetryTime.toISOString();
}

/**
 * Check if webhook should be retried
 */
function shouldRetry(deliveryAttempt: number, retryConfig: any): boolean {
  const maxRetries = retryConfig?.maxRetries || 3;
  return deliveryAttempt < maxRetries;
}

/**
 * Schedule webhook retry
 */
async function scheduleWebhookRetry(deliveryRecord: any, webhook: any): Promise<void> {
  try {
    // In production, this would schedule a retry using Azure Service Bus or similar
    // For now, we'll just log the retry scheduling
    logger.info("Webhook retry scheduled", {
      deliveryId: deliveryRecord.id,
      webhookId: webhook.id,
      nextRetryAt: deliveryRecord.nextRetryAt,
      deliveryAttempt: deliveryRecord.deliveryAttempt + 1
    });

    // Update delivery record status
    const updatedDelivery = {
      ...deliveryRecord,
      status: DeliveryStatus.RETRYING,
      updatedAt: new Date().toISOString()
    };

    await db.updateItem('webhook-deliveries', updatedDelivery);

  } catch (error) {
    logger.error('Failed to schedule webhook retry', {
      error,
      deliveryId: deliveryRecord.id
    });
  }
}

// Register functions
app.http('webhook-delivery', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'webhooks/deliver',
  handler: deliverWebhook
});

/**
 * Project Management Function
 * Handles retrieving, updating, and deleting projects
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Project visibility enum
enum ProjectVisibility {
  PRIVATE = 'PRIVATE',
  ORGANIZATION = 'ORGANIZATION',
  PUBLIC = 'PUBLIC'
}

// Validation schemas
const updateProjectSchema = Joi.object({
  name: Joi.string().min(2).max(100).optional(),
  description: Joi.string().max(500).optional(),
  visibility: Joi.string().valid(...Object.values(ProjectVisibility)).optional(),
  tags: Joi.array().items(Joi.string().max(50)).max(10).optional(),
  settings: Joi.object({
    defaultDocumentTags: Joi.array().items(Joi.string()).optional(),
    defaultWorkflowId: Joi.string().uuid().allow(null).optional(),
    autoProcessing: Joi.boolean().optional()
  }).optional()
});

/**
 * Project management handler
 */
export async function projectManage(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const projectId = request.params.projectId;

  if (!projectId) {
    return addCorsHeaders({
      status: 400,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: 'Project ID is required' }
    }, request);
  }

  logger.info("Project management started", { correlationId, projectId, method: request.method });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Get project
    const project = await db.readItem('projects', projectId, projectId);
    if (!project) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Project not found" }
      }, request);
    }

    // Check if user is a member of the project
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.projectId = @projectId';
    const memberships = await db.queryItems('project-members', membershipQuery, [user.id, projectId]);
    
    if (memberships.length === 0) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied" }
      }, request);
    }

    const membership = memberships[0];

    if (request.method === 'GET') {
      // Get project details with enriched data
      
      // Get document count
      const documentCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.projectId = @projectId';
      const documentCountResult = await db.queryItems('documents', documentCountQuery, [projectId]);
      const documentCount = Number(documentCountResult[0]) || 0;

      // Get workflow count
      const workflowCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.projectId = @projectId';
      const workflowCountResult = await db.queryItems('workflows', workflowCountQuery, [projectId]);
      const workflowCount = Number(workflowCountResult[0]) || 0;

      // Get member count
      const memberCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.projectId = @projectId';
      const memberCountResult = await db.queryItems('project-members', memberCountQuery, [projectId]);
      const memberCount = Number(memberCountResult[0]) || 0;

      // Get organization details
      let organizationName = 'Unknown';
      try {
        const organization = await db.readItem('organizations', (project as any).organizationId, (project as any).organizationId);
        if (organization) {
          organizationName = (organization as any).name;
        }
      } catch (error) {
        // Organization might not exist or user might not have access
      }

      // Enrich project with statistics
      const enrichedProject = {
        ...(project as any),
        documentCount,
        workflowCount,
        memberCount,
        organizationName,
        storageUsed: 0, // TODO: Calculate actual storage usage
        userRole: (membership as any).role
      };

      return addCorsHeaders({
        status: 200,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: enrichedProject
      }, request);

    } else if (request.method === 'PATCH') {
      // Update project
      
      // Check if user has admin role
      if ((membership as any).role !== 'ADMIN') {
        return addCorsHeaders({
          status: 403,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { error: "Only project admins can update project settings" }
        }, request);
      }

      // Validate request body
      const body = await request.json();
      const { error, value } = updateProjectSchema.validate(body);
      
      if (error) {
        return addCorsHeaders({
          status: 400,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { 
            error: 'Validation Error', 
            message: error.details.map(d => d.message).join(', ')
          }
        }, request);
      }

      const updateData = value;

      // Update project
      const updatedProject = {
        ...(project as any),
        id: projectId,
        ...updateData,
        updatedBy: user.id,
        updatedAt: new Date().toISOString()
      };

      await db.updateItem('projects', updatedProject);

      // Create activity record
      await db.createItem('activities', {
        id: uuidv4(),
        type: "project_updated",
        userId: user.id,
        organizationId: (project as any).organizationId,
        projectId,
        timestamp: new Date().toISOString(),
        details: {
          updatedFields: Object.keys(updateData),
          projectName: (project as any).name
        },
        tenantId: user.tenantId
      });

      logger.info("Project updated successfully", {
        correlationId,
        projectId,
        userId: user.id,
        updatedFields: Object.keys(updateData)
      });

      return addCorsHeaders({
        status: 200,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          id: projectId,
          message: "Project updated successfully"
        }
      }, request);

    } else if (request.method === 'DELETE') {
      // Delete project
      
      // Check if user has admin role
      if ((membership as any).role !== 'ADMIN') {
        return addCorsHeaders({
          status: 403,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { error: "Only project admins can delete projects" }
        }, request);
      }

      // Check if project has documents
      const documentCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.projectId = @projectId';
      const documentCountResult = await db.queryItems('documents', documentCountQuery, [projectId]);
      const documentCount = Number(documentCountResult[0]) || 0;

      if (documentCount > 0) {
        return addCorsHeaders({
          status: 400,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { 
            error: "Cannot delete project with existing documents",
            message: `Project has ${documentCount} documents. Please delete all documents first.`
          }
        }, request);
      }

      // Delete project members
      const allMembersQuery = 'SELECT * FROM c WHERE c.projectId = @projectId';
      const allMembers = await db.queryItems('project-members', allMembersQuery, [projectId]);
      
      for (const member of allMembers) {
        await db.deleteItem('project-members', (member as any).id, (member as any).id);
      }

      // Remove project from organization's project list
      try {
        const organization = await db.readItem('organizations', (project as any).organizationId, (project as any).organizationId);
        if (organization) {
          const updatedOrganization = {
            ...(organization as any),
            id: (project as any).organizationId,
            projectIds: ((organization as any).projectIds || []).filter((id: string) => id !== projectId),
            updatedAt: new Date().toISOString(),
            updatedBy: user.id
          };
          await db.updateItem('organizations', updatedOrganization);
        }
      } catch (error) {
        // Organization might not exist, continue with project deletion
      }

      // Delete project
      await db.deleteItem('projects', projectId, projectId);

      // Create activity record
      await db.createItem('activities', {
        id: uuidv4(),
        type: "project_deleted",
        userId: user.id,
        organizationId: (project as any).organizationId,
        projectId,
        timestamp: new Date().toISOString(),
        details: {
          projectName: (project as any).name,
          memberCount: allMembers.length
        },
        tenantId: user.tenantId
      });

      logger.info("Project deleted successfully", {
        correlationId,
        projectId,
        userId: user.id
      });

      return addCorsHeaders({
        status: 200,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          message: "Project deleted successfully"
        }
      }, request);

    } else {
      return addCorsHeaders({
        status: 405,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Method not allowed" }
      }, request);
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Project management failed", {
      correlationId,
      projectId,
      method: request.method,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

// Register functions
app.http('project-manage', {
  methods: ['GET', 'PATCH', 'DELETE', 'OPTIONS'],
  authLevel: 'function',
  route: 'projects/{projectId}',
  handler: projectManage
});

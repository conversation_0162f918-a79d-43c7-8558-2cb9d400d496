# Azure Event Grid Configuration Guide

This document provides comprehensive guidance for configuring and using Azure Event Grid in the application.

## Overview

Azure Event Grid is a fully managed event routing service that enables event-driven architectures. Our application uses Event Grid for:

- **Storage Events**: Blob created/deleted notifications
- **Document Events**: Upload, processing, and sharing notifications
- **Workflow Events**: Workflow lifecycle management
- **User Events**: Registration and activity tracking
- **System Events**: Health checks and performance alerts

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Azure Storage │    │  Custom Events  │    │  System Events  │
│   (Blob Events) │    │ (Application)   │    │ (Monitoring)    │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          ▼                      ▼                      ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Azure Event Grid                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  System Topic   │  │  Custom Topic   │  │  Event Domain   │ │
│  │ (Storage Events)│  │(App Events)     │  │ (Multi-tenant)  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────┬───────────────────┬───────────────────┬───────────────┘
          │                   │                   │
          ▼                   ▼                   ▼
┌─────────────────────────────────────────────────────────────────┐
│                Azure Functions (Event Handlers)                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Storage Handler │  │ Document Handler│  │ Workflow Handler│ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## Configuration

### 1. Environment Variables

Add these to your `local.settings.json`:

```json
{
  "Values": {
    "EVENT_GRID_ENABLED": "true",
    "EVENT_GRID_TOPIC_ENDPOINT": "https://your-topic.eastus-1.eventgrid.azure.net/api/events",
    "EVENT_GRID_TOPIC_KEY": "your-access-key",
    "EVENT_GRID_TOPIC_NAME": "your-topic-name",
    "EVENT_GRID_RETRY_ATTEMPTS": "3",
    "EVENT_GRID_TIMEOUT_MS": "30000"
  }
}
```

### 2. Azure Resources Setup

Run the configuration script:

```powershell
.\scripts\configure-event-grid.ps1 -ResourceGroup "your-rg" -Location "eastus" -FunctionAppName "your-function-app"
```

This script creates:
- Event Grid System Topic for Storage Account
- Event Grid Custom Topic for Application Events
- Event Subscriptions for different event types
- Dead letter storage container
- Webhook endpoints configuration

## Event Types

### Storage Events
- `Microsoft.Storage.BlobCreated`: Triggered when files are uploaded
- `Microsoft.Storage.BlobDeleted`: Triggered when files are deleted

### Document Events
- `Document.Uploaded`: Document upload completed
- `Document.Processed`: Document processing completed
- `Document.Shared`: Document shared with users

### Workflow Events
- `Workflow.Started`: Workflow execution started
- `Workflow.Completed`: Workflow execution completed

### User Events
- `User.Registered`: New user registration

### System Events
- `System.HealthCheck`: System health monitoring
- `Performance.Alert`: Performance threshold alerts
- `Analytics.Generated`: Analytics data generated
- `Notification.Sent`: Notification delivery tracking

## Event Handlers

### HTTP Webhook Handler
- **Endpoint**: `/api/eventgrid/webhook`
- **Method**: POST
- **Auth Level**: Anonymous (required for Event Grid)
- **Purpose**: Handles Event Grid webhook events

### Custom Event Publisher
- **Endpoint**: `/api/eventgrid/publish`
- **Method**: POST
- **Auth Level**: Function
- **Purpose**: Publishes custom application events

### Native Event Grid Triggers
- **Storage Events Trigger**: Handles storage-related events
- **Custom Events Trigger**: Handles application-specific events

## Usage Examples

### Publishing Events

```typescript
import { publishEvent, EventType } from '../functions/event-grid-handlers';

// Publish document upload event
await publishEvent(
  EventType.DOCUMENT_UPLOADED,
  'documents/my-document.pdf',
  {
    documentId: 'doc-123',
    fileName: 'my-document.pdf',
    uploadedBy: 'user-456',
    timestamp: new Date().toISOString()
  }
);
```

### Using Event Grid Integration Service

```typescript
import { eventGridIntegration } from '../shared/services/event-grid-integration';

// Publish single event
const eventId = await eventGridIntegration.publishEvent({
  eventType: 'Document.Processed',
  subject: 'documents/processed/doc-123',
  data: {
    documentId: 'doc-123',
    processingTime: 5000,
    status: 'completed'
  }
});

// Publish multiple events
const eventIds = await eventGridIntegration.publishEvents([
  {
    eventType: 'Workflow.Started',
    subject: 'workflows/workflow-456',
    data: { workflowId: 'workflow-456', startedBy: 'user-789' }
  },
  {
    eventType: 'Analytics.Generated',
    subject: 'analytics/daily-report',
    data: { reportType: 'daily', generatedAt: new Date().toISOString() }
  }
]);
```

## Testing

### Run Event Grid Tests

```bash
node scripts/test-event-grid.js
```

This script tests:
- Event Grid publishing functionality
- Webhook endpoint validation
- Custom event publishing
- Load testing with multiple events
- Metrics monitoring

### Manual Testing

1. **Test Webhook Validation**:
   ```bash
   curl -X POST https://your-function-app.azurewebsites.net/api/eventgrid/webhook \
     -H "Content-Type: application/json" \
     -H "aeg-event-type: SubscriptionValidation" \
     -d '[{"eventType":"Microsoft.EventGrid.SubscriptionValidationEvent","data":{"validationCode":"test-code"}}]'
   ```

2. **Test Custom Event Publishing**:
   ```bash
   curl -X POST https://your-function-app.azurewebsites.net/api/eventgrid/publish \
     -H "Content-Type: application/json" \
     -H "x-functions-key: your-function-key" \
     -d '{"eventType":"Test.Event","subject":"test/event","data":{"message":"test"}}'
   ```

## Monitoring

### Event Grid Metrics
- **Total Events**: Number of events published
- **Successful Events**: Successfully processed events
- **Failed Events**: Events that failed processing
- **Average Latency**: Average event processing time

### Azure Monitor Integration
- View Event Grid metrics in Azure Portal
- Set up alerts for failed events
- Monitor event delivery success rates
- Track event processing latency

### Application Insights
- Custom telemetry for event processing
- Performance monitoring
- Error tracking and diagnostics

## Best Practices

### Event Design
1. **Use descriptive event types**: Follow the pattern `Noun.Verb` (e.g., `Document.Uploaded`)
2. **Include relevant metadata**: Add timestamps, user IDs, and correlation IDs
3. **Keep event data lightweight**: Avoid large payloads in event data
4. **Use consistent subject patterns**: Follow hierarchical naming (e.g., `documents/folder/file`)

### Error Handling
1. **Implement retry logic**: Use exponential backoff for transient failures
2. **Configure dead letter queues**: Handle permanently failed events
3. **Validate event data**: Check required fields and data types
4. **Log comprehensive information**: Include event IDs and processing context

### Performance
1. **Batch events when possible**: Reduce API calls by sending multiple events
2. **Use async processing**: Don't block on event publishing
3. **Monitor event latency**: Track processing times and optimize bottlenecks
4. **Implement circuit breakers**: Prevent cascade failures

### Security
1. **Use managed identities**: Avoid storing access keys when possible
2. **Validate event sources**: Verify events come from trusted sources
3. **Implement proper authentication**: Secure custom event endpoints
4. **Monitor for suspicious activity**: Track unusual event patterns

## Troubleshooting

### Common Issues

1. **Events not being delivered**:
   - Check Event Grid subscription configuration
   - Verify webhook endpoint is accessible
   - Check function app authentication settings

2. **Webhook validation failing**:
   - Ensure endpoint returns validation response
   - Check CORS configuration
   - Verify content-type headers

3. **High event latency**:
   - Monitor function app performance
   - Check for cold start issues
   - Optimize event processing logic

4. **Event publishing failures**:
   - Verify Event Grid topic configuration
   - Check access key validity
   - Monitor rate limiting

### Debugging Steps

1. **Check Azure Portal**:
   - Event Grid topic metrics
   - Function app logs
   - Application Insights traces

2. **Review Function Logs**:
   ```bash
   func azure functionapp logstream your-function-app
   ```

3. **Test Event Grid Connectivity**:
   ```bash
   node scripts/test-event-grid.js
   ```

4. **Validate Configuration**:
   - Check environment variables
   - Verify resource group settings
   - Confirm subscription permissions

## Support

For additional support:
- Review Azure Event Grid documentation
- Check Application Insights for detailed telemetry
- Monitor Azure Service Health for platform issues
- Contact Azure Support for infrastructure problems

/**
 * Document Intelligence Function
 * Handles comprehensive document analysis with advanced AI features
 * Migrated from old-arch/src/ai-service/models/document-intelligence.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { eventService } from '../shared/services/event';

// Document intelligence types and enums
enum AnalysisFeature {
  KEY_VALUE_PAIRS = 'keyValuePairs',
  TABLES = 'tables',
  ENTITIES = 'entities',
  STYLES = 'styles',
  LANGUAGES = 'languages',
  BARCODES = 'barcodes',
  FORMULAS = 'formulas',
  FONTS = 'fonts',
  PARAGRAPHS = 'paragraphs',
  SECTIONS = 'sections',
  FIGURES = 'figures',
  SIGNATURES = 'signatures',
  LAYOUT = 'layout',
  METADATA = 'metadata'
}

enum DocumentType {
  INVOICE = 'invoice',
  RECEIPT = 'receipt',
  CONTRACT = 'contract',
  BUSINESS_CARD = 'businessCard',
  ID_DOCUMENT = 'idDocument',
  TAX_DOCUMENT = 'tax',
  HEALTH_INSURANCE = 'healthInsurance',
  FINANCIAL_STATEMENT = 'financialStatement',
  LEGAL_DOCUMENT = 'legalDocument',
  GENERAL = 'general'
}

enum ConfidenceLevel {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  VERY_HIGH = 'VERY_HIGH'
}

// Validation schemas
const comprehensiveAnalysisSchema = Joi.object({
  documentId: Joi.string().uuid().required(),
  documentType: Joi.string().valid(...Object.values(DocumentType)).default(DocumentType.GENERAL),
  features: Joi.array().items(Joi.string().valid(...Object.values(AnalysisFeature))).default([
    AnalysisFeature.KEY_VALUE_PAIRS,
    AnalysisFeature.TABLES,
    AnalysisFeature.ENTITIES,
    AnalysisFeature.LAYOUT
  ]),
  options: Joi.object({
    extractTables: Joi.boolean().default(true),
    extractKeyValuePairs: Joi.boolean().default(true),
    extractEntities: Joi.boolean().default(true),
    extractBarcodes: Joi.boolean().default(false),
    extractFormulas: Joi.boolean().default(false),
    extractSignatures: Joi.boolean().default(false),
    analyzeLayout: Joi.boolean().default(true),
    detectLanguages: Joi.boolean().default(true),
    extractMetadata: Joi.boolean().default(true),
    enableBusinessIntelligence: Joi.boolean().default(false),
    customModels: Joi.array().items(Joi.string()).optional()
  }).optional(),
  businessRules: Joi.object({
    requiredFields: Joi.array().items(Joi.string()).optional(),
    validationRules: Joi.array().items(Joi.object()).optional(),
    complianceChecks: Joi.array().items(Joi.string()).optional()
  }).optional(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional()
});

interface ComprehensiveAnalysisRequest {
  documentId: string;
  documentType?: DocumentType;
  features?: AnalysisFeature[];
  options?: {
    extractTables?: boolean;
    extractKeyValuePairs?: boolean;
    extractEntities?: boolean;
    extractBarcodes?: boolean;
    extractFormulas?: boolean;
    extractSignatures?: boolean;
    analyzeLayout?: boolean;
    detectLanguages?: boolean;
    extractMetadata?: boolean;
    enableBusinessIntelligence?: boolean;
    customModels?: string[];
  };
  businessRules?: {
    requiredFields?: string[];
    validationRules?: any[];
    complianceChecks?: string[];
  };
  organizationId: string;
  projectId?: string;
}

interface DocumentIntelligenceResult {
  id: string;
  documentId: string;
  documentType: DocumentType;
  analysisFeatures: AnalysisFeature[];
  results: {
    keyValuePairs: Array<{
      key: string;
      value: string;
      confidence: number;
      boundingBox?: any;
    }>;
    tables: Array<{
      id: string;
      rows: number;
      columns: number;
      cells: Array<{
        rowIndex: number;
        columnIndex: number;
        text: string;
        confidence: number;
        isHeader?: boolean;
      }>;
      confidence: number;
    }>;
    entities: Array<{
      type: string;
      text: string;
      confidence: number;
      category: string;
      subcategory?: string;
      boundingBox?: any;
    }>;
    languages: Array<{
      language: string;
      confidence: number;
      regions: any[];
    }>;
    barcodes: Array<{
      type: string;
      value: string;
      confidence: number;
      boundingBox?: any;
    }>;
    formulas: Array<{
      formula: string;
      result?: string;
      confidence: number;
      boundingBox?: any;
    }>;
    signatures: Array<{
      type: 'handwritten' | 'digital';
      confidence: number;
      boundingBox?: any;
      verified?: boolean;
    }>;
    layout: {
      pages: Array<{
        pageNumber: number;
        width: number;
        height: number;
        angle: number;
        unit: string;
        lines: any[];
        words: any[];
        paragraphs: any[];
        sections: any[];
      }>;
    };
    metadata: {
      pageCount: number;
      documentSize: number;
      creationDate?: string;
      modificationDate?: string;
      author?: string;
      title?: string;
      subject?: string;
      keywords?: string[];
    };
  };
  businessIntelligence?: {
    insights: Array<{
      type: string;
      message: string;
      confidence: number;
      impact: string;
      actionable: boolean;
    }>;
    dataQuality: {
      completeness: number;
      accuracy: number;
      consistency: number;
      validity: number;
    };
    complianceStatus: {
      overall: string;
      checks: Array<{
        rule: string;
        status: 'PASS' | 'FAIL' | 'WARNING';
        message: string;
      }>;
    };
    recommendations: Array<{
      type: string;
      priority: string;
      description: string;
      estimatedImpact: string;
    }>;
  };
  performance: {
    processingTime: number;
    modelVersion: string;
    confidenceScore: number;
    qualityScore: number;
  };
  createdAt: string;
  organizationId: string;
  tenantId: string;
}

/**
 * Comprehensive document analysis handler
 */
export async function performComprehensiveAnalysis(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;
  const startTime = Date.now();
  logger.info("Comprehensive document analysis started", { correlationId });

  try {
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    const body = await request.json();
    const { error, value } = comprehensiveAnalysisSchema.validate(body);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const analysisRequest: ComprehensiveAnalysisRequest = value;

    // Check document access
    const document = await db.readItem('documents', analysisRequest.documentId, analysisRequest.documentId);
    if (!document) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document not found" }
      }, request);
    }

    const documentData = document as any;

    // Check organization access
    const hasAccess = await checkOrganizationAccess(analysisRequest.organizationId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Perform comprehensive analysis
    const analysisResult = await executeComprehensiveAnalysis(analysisRequest, documentData, user.id);

    // Store analysis result
    await storeAnalysisResult(analysisResult);

    const duration = Date.now() - startTime;

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "document_intelligence_analysis",
      userId: user.id,
      organizationId: analysisRequest.organizationId,
      projectId: analysisRequest.projectId,
      documentId: analysisRequest.documentId,
      timestamp: new Date().toISOString(),
      details: {
        analysisId: analysisResult.id,
        documentType: analysisRequest.documentType,
        featuresAnalyzed: analysisResult.analysisFeatures,
        confidenceScore: analysisResult.performance.confidenceScore,
        processingTime: duration
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'DocumentIntelligenceCompleted',
      aggregateId: analysisRequest.documentId,
      aggregateType: 'Document',
      version: 1,
      data: {
        analysisResult: {
          ...analysisResult,
          results: {
            ...analysisResult.results,
            keyValuePairs: analysisResult.results.keyValuePairs.slice(0, 10) // Limit data in event
          }
        },
        analyzedBy: user.id
      },
      userId: user.id,
      organizationId: analysisRequest.organizationId,
      tenantId: user.tenantId
    });

    logger.info("Comprehensive document analysis completed", {
      correlationId,
      analysisId: analysisResult.id,
      documentId: analysisRequest.documentId,
      documentType: analysisRequest.documentType,
      featuresAnalyzed: analysisResult.analysisFeatures.length,
      confidenceScore: analysisResult.performance.confidenceScore,
      duration,
      analyzedBy: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        analysisResult,
        processingTime: duration,
        message: "Comprehensive document analysis completed successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Comprehensive document analysis failed", { correlationId, error: errorMessage });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkOrganizationAccess(organizationId: string, userId: string): Promise<boolean> {
  try {
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
    return memberships.length > 0;
  } catch (error) {
    logger.error('Failed to check organization access', { error, organizationId, userId });
    return false;
  }
}

async function executeComprehensiveAnalysis(request: ComprehensiveAnalysisRequest, document: any, userId: string): Promise<DocumentIntelligenceResult> {
  const analysisId = uuidv4();
  const startTime = Date.now();

  try {
    // Simulate comprehensive document analysis
    const analysisResult: DocumentIntelligenceResult = {
      id: analysisId,
      documentId: request.documentId,
      documentType: request.documentType || DocumentType.GENERAL,
      analysisFeatures: request.features || [
        AnalysisFeature.KEY_VALUE_PAIRS,
        AnalysisFeature.TABLES,
        AnalysisFeature.ENTITIES,
        AnalysisFeature.LAYOUT
      ],
      results: {
        keyValuePairs: await extractKeyValuePairs(document, request.options),
        tables: await extractTables(document, request.options),
        entities: await extractEntities(document, request.options),
        languages: await detectLanguages(document, request.options),
        barcodes: await extractBarcodes(document, request.options),
        formulas: await extractFormulas(document, request.options),
        signatures: await extractSignatures(document, request.options),
        layout: await analyzeLayout(document, request.options),
        metadata: await extractMetadata(document, request.options)
      },
      businessIntelligence: request.options?.enableBusinessIntelligence ? 
        await generateBusinessIntelligence(document, request) : undefined,
      performance: {
        processingTime: Date.now() - startTime,
        modelVersion: '2024.1.0',
        confidenceScore: Math.random() * 0.3 + 0.7, // 0.7 to 1.0
        qualityScore: Math.random() * 0.2 + 0.8 // 0.8 to 1.0
      },
      createdAt: new Date().toISOString(),
      organizationId: request.organizationId,
      tenantId: request.organizationId
    };

    return analysisResult;

  } catch (error) {
    logger.error('Failed to execute comprehensive analysis', { error, analysisId, documentId: request.documentId });
    throw error;
  }
}

async function extractKeyValuePairs(document: any, options?: any): Promise<any[]> {
  // Mock key-value pair extraction
  const mockPairs = [
    { key: 'Invoice Number', value: 'INV-2024-001', confidence: 0.95 },
    { key: 'Date', value: '2024-01-15', confidence: 0.92 },
    { key: 'Total Amount', value: '$1,250.00', confidence: 0.98 },
    { key: 'Customer Name', value: 'Acme Corporation', confidence: 0.89 },
    { key: 'Due Date', value: '2024-02-15', confidence: 0.91 }
  ];

  return mockPairs.map(pair => ({
    ...pair,
    boundingBox: {
      x: Math.random() * 500,
      y: Math.random() * 700,
      width: Math.random() * 200 + 100,
      height: 20
    }
  }));
}

async function extractTables(document: any, options?: any): Promise<any[]> {
  // Mock table extraction
  return [
    {
      id: uuidv4(),
      rows: 5,
      columns: 4,
      cells: [
        { rowIndex: 0, columnIndex: 0, text: 'Item', confidence: 0.95, isHeader: true },
        { rowIndex: 0, columnIndex: 1, text: 'Quantity', confidence: 0.93, isHeader: true },
        { rowIndex: 0, columnIndex: 2, text: 'Price', confidence: 0.94, isHeader: true },
        { rowIndex: 0, columnIndex: 3, text: 'Total', confidence: 0.96, isHeader: true },
        { rowIndex: 1, columnIndex: 0, text: 'Widget A', confidence: 0.91 },
        { rowIndex: 1, columnIndex: 1, text: '10', confidence: 0.98 },
        { rowIndex: 1, columnIndex: 2, text: '$25.00', confidence: 0.94 },
        { rowIndex: 1, columnIndex: 3, text: '$250.00', confidence: 0.97 }
      ],
      confidence: 0.94
    }
  ];
}

async function extractEntities(document: any, options?: any): Promise<any[]> {
  // Mock entity extraction
  return [
    { type: 'PERSON', text: 'John Smith', confidence: 0.92, category: 'Person', boundingBox: {} },
    { type: 'ORGANIZATION', text: 'Acme Corporation', confidence: 0.89, category: 'Organization', boundingBox: {} },
    { type: 'DATE', text: '2024-01-15', confidence: 0.95, category: 'DateTime', boundingBox: {} },
    { type: 'MONEY', text: '$1,250.00', confidence: 0.97, category: 'Quantity', subcategory: 'Currency', boundingBox: {} },
    { type: 'EMAIL', text: '<EMAIL>', confidence: 0.93, category: 'PersonType', subcategory: 'Email', boundingBox: {} }
  ];
}

async function detectLanguages(document: any, options?: any): Promise<any[]> {
  return [
    { language: 'en', confidence: 0.98, regions: [{ pageNumber: 1, boundingBox: {} }] },
    { language: 'es', confidence: 0.15, regions: [{ pageNumber: 1, boundingBox: {} }] }
  ];
}

async function extractBarcodes(document: any, options?: any): Promise<any[]> {
  if (!options?.extractBarcodes) return [];
  
  return [
    { type: 'QR', value: 'https://example.com/invoice/123', confidence: 0.96, boundingBox: {} },
    { type: 'Code128', value: '1234567890', confidence: 0.94, boundingBox: {} }
  ];
}

async function extractFormulas(document: any, options?: any): Promise<any[]> {
  if (!options?.extractFormulas) return [];
  
  return [
    { formula: 'SUM(B2:B10)', result: '1250.00', confidence: 0.91, boundingBox: {} },
    { formula: 'B2*C2', result: '250.00', confidence: 0.89, boundingBox: {} }
  ];
}

async function extractSignatures(document: any, options?: any): Promise<any[]> {
  if (!options?.extractSignatures) return [];
  
  return [
    { type: 'handwritten', confidence: 0.87, boundingBox: {}, verified: false },
    { type: 'digital', confidence: 0.95, boundingBox: {}, verified: true }
  ];
}

async function analyzeLayout(document: any, options?: any): Promise<any> {
  return {
    pages: [
      {
        pageNumber: 1,
        width: 612,
        height: 792,
        angle: 0,
        unit: 'pixel',
        lines: [],
        words: [],
        paragraphs: [],
        sections: []
      }
    ]
  };
}

async function extractMetadata(document: any, options?: any): Promise<any> {
  return {
    pageCount: 1,
    documentSize: document.size || 0,
    creationDate: document.createdAt,
    modificationDate: document.updatedAt,
    author: document.createdBy,
    title: document.name,
    subject: document.description,
    keywords: document.tags || []
  };
}

async function generateBusinessIntelligence(document: any, request: ComprehensiveAnalysisRequest): Promise<any> {
  return {
    insights: [
      {
        type: 'data_quality',
        message: 'Document contains high-quality structured data suitable for automation',
        confidence: 0.92,
        impact: 'high',
        actionable: true
      },
      {
        type: 'compliance',
        message: 'Document meets standard invoice formatting requirements',
        confidence: 0.89,
        impact: 'medium',
        actionable: false
      }
    ],
    dataQuality: {
      completeness: 95,
      accuracy: 92,
      consistency: 88,
      validity: 94
    },
    complianceStatus: {
      overall: 'COMPLIANT',
      checks: [
        { rule: 'Required fields present', status: 'PASS', message: 'All mandatory fields detected' },
        { rule: 'Date format validation', status: 'PASS', message: 'Dates in correct format' },
        { rule: 'Amount validation', status: 'WARNING', message: 'Currency symbol inconsistent' }
      ]
    },
    recommendations: [
      {
        type: 'automation',
        priority: 'high',
        description: 'Consider automating data extraction for similar documents',
        estimatedImpact: '40% time savings'
      },
      {
        type: 'quality',
        priority: 'medium',
        description: 'Standardize currency formatting for better accuracy',
        estimatedImpact: '15% accuracy improvement'
      }
    ]
  };
}

async function storeAnalysisResult(result: DocumentIntelligenceResult): Promise<void> {
  try {
    await db.createItem('document-intelligence-results', result);
    
    logger.info('Document intelligence result stored', {
      analysisId: result.id,
      documentId: result.documentId,
      featuresAnalyzed: result.analysisFeatures.length,
      confidenceScore: result.performance.confidenceScore
    });

  } catch (error) {
    logger.error('Failed to store analysis result', { error, analysisId: result.id });
  }
}

// Register functions
app.http('document-intelligence-comprehensive', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'documents/{documentId}/intelligence/comprehensive',
  handler: performComprehensiveAnalysis
});

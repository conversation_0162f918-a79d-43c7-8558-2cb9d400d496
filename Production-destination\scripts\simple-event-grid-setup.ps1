# Simple Event Grid Setup Script
# Creates essential Event Grid subscriptions

Write-Host "🚀 Setting up Event Grid Subscriptions..." -ForegroundColor Green

$ResourceGroup = "docucontext"
$FunctionAppName = "hepzlogic"
$EventGridTopicName = "hepzeg"
$StorageAccountName = "stdocucontex900520441468"

# Get subscription ID
$subscriptionId = az account show --query id --output tsv
Write-Host "Subscription ID: $subscriptionId" -ForegroundColor Yellow

# Get Function App URL
Write-Host "📋 Getting Function App details..." -ForegroundColor Blue
$functionAppUrl = az functionapp show --name $FunctionAppName --resource-group $ResourceGroup --query defaultHostName --output tsv

if (-not $functionAppUrl) {
    Write-Error "Function App '$FunctionAppName' not found"
    exit 1
}

Write-Host "Function App URL: https://$functionAppUrl" -ForegroundColor Green

# 1. Create System Topic for Storage Account
Write-Host "📦 Creating Storage System Topic..." -ForegroundColor Blue
$systemTopicName = "$StorageAccountName-events"
$storageAccountId = "/subscriptions/$subscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.Storage/storageAccounts/$StorageAccountName"

Write-Host "Creating system topic: $systemTopicName" -ForegroundColor Yellow
az eventgrid system-topic create `
    --name $systemTopicName `
    --resource-group $ResourceGroup `
    --source $storageAccountId `
    --topic-type "Microsoft.Storage.StorageAccounts" `
    --location "eastus"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Storage System Topic created successfully" -ForegroundColor Green
} else {
    Write-Host "⚠️ Storage System Topic creation failed or already exists" -ForegroundColor Yellow
}

# 2. Create Event Subscription for Blob Events
Write-Host "📬 Creating Blob Events Subscription..." -ForegroundColor Blue
$blobSubscription = "blob-events-subscription"

Write-Host "Creating subscription: $blobSubscription" -ForegroundColor Yellow
az eventgrid event-subscription create `
    --name $blobSubscription `
    --source-resource-id "/subscriptions/$subscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/systemTopics/$systemTopicName" `
    --endpoint "https://$functionAppUrl/api/eventgrid/webhook" `
    --endpoint-type webhook `
    --included-event-types "Microsoft.Storage.BlobCreated" "Microsoft.Storage.BlobDeleted" `
    --max-delivery-attempts 3 `
    --event-ttl 1440

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Blob Events subscription created successfully" -ForegroundColor Green
} else {
    Write-Host "⚠️ Blob Events subscription creation failed or already exists" -ForegroundColor Yellow
}

# 3. Create Event Subscription for Custom Events
Write-Host "🎯 Creating Custom Events Subscription..." -ForegroundColor Blue
$customSubscription = "custom-events-subscription"

Write-Host "Creating subscription: $customSubscription" -ForegroundColor Yellow
az eventgrid event-subscription create `
    --name $customSubscription `
    --source-resource-id "/subscriptions/$subscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/topics/$EventGridTopicName" `
    --endpoint "https://$functionAppUrl/api/eventgrid/webhook" `
    --endpoint-type webhook `
    --max-delivery-attempts 3 `
    --event-ttl 1440

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Custom Events subscription created successfully" -ForegroundColor Green
} else {
    Write-Host "⚠️ Custom Events subscription creation failed or already exists" -ForegroundColor Yellow
}

# 4. Verify Event Grid Topic
Write-Host "🔍 Verifying Event Grid Topic..." -ForegroundColor Blue
$topicEndpoint = az eventgrid topic show --name $EventGridTopicName --resource-group $ResourceGroup --query "endpoint" --output tsv

if ($topicEndpoint) {
    Write-Host "✅ Event Grid Topic verified: $topicEndpoint" -ForegroundColor Green
} else {
    Write-Host "❌ Event Grid Topic verification failed" -ForegroundColor Red
}

# 5. Summary
Write-Host ""
Write-Host "✅ Event Grid setup completed!" -ForegroundColor Green
Write-Host "📝 Configuration Summary:" -ForegroundColor Yellow
Write-Host "   Resource Group: $ResourceGroup" -ForegroundColor White
Write-Host "   Function App: $FunctionAppName" -ForegroundColor White
Write-Host "   Function URL: https://$functionAppUrl" -ForegroundColor White
Write-Host "   Event Grid Topic: $EventGridTopicName" -ForegroundColor White
Write-Host "   Storage System Topic: $systemTopicName" -ForegroundColor White
Write-Host "   Blob Events Subscription: $blobSubscription" -ForegroundColor White
Write-Host "   Custom Events Subscription: $customSubscription" -ForegroundColor White
Write-Host ""
Write-Host "🔄 Next Steps:" -ForegroundColor Yellow
Write-Host "   1. Deploy your Azure Functions to the cloud" -ForegroundColor White
Write-Host "   2. Test Event Grid with: node scripts/test-event-grid.js" -ForegroundColor White
Write-Host "   3. Monitor events in Azure Portal" -ForegroundColor White
Write-Host "   4. Check Function App logs for event processing" -ForegroundColor White

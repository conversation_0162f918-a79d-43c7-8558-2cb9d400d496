/**
 * Document Comments Function
 * Handles document comment operations (create, read, update, delete)
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Validation schemas
const createCommentSchema = Joi.object({
  content: Joi.string().required().max(2000),
  pageNumber: Joi.number().integer().min(1).optional(),
  coordinates: Joi.object({
    x: Joi.number().required(),
    y: Joi.number().required(),
    width: Joi.number().optional(),
    height: Joi.number().optional()
  }).optional(),
  projectId: Joi.string().uuid().required(),
  organizationId: Joi.string().uuid().required(),
  metadata: Joi.object().optional()
});

const updateCommentSchema = Joi.object({
  content: Joi.string().max(2000).optional(),
  isResolved: Joi.boolean().optional()
});

const getCommentsSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  pageNumber: Joi.number().integer().min(1).optional(),
  resolved: Joi.boolean().optional()
});

interface Comment {
  id: string;
  documentId: string;
  content: string;
  pageNumber?: number;
  coordinates?: {
    x: number;
    y: number;
    width?: number;
    height?: number;
  };
  createdBy: string;
  createdAt: string;
  updatedAt?: string;
  organizationId: string;
  projectId: string;
  replies: CommentReply[];
  isResolved: boolean;
  metadata?: any;
  tenantId?: string;
}

interface CommentReply {
  id: string;
  content: string;
  createdBy: string;
  createdAt: string;
  updatedAt?: string;
}

/**
 * Create comment handler
 */
export async function createComment(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const documentId = request.params.documentId;

  if (!documentId) {
    return addCorsHeaders({
      status: 400,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: 'Document ID is required' }
    }, request);
  }

  logger.info("Create comment started", { correlationId, documentId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createCommentSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const commentData = value;

    // Verify document exists and user has access
    const document = await db.readItem('documents', documentId, documentId);

    if (!document) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document not found" }
      }, request);
    }

    // Check access permissions
    const hasAccess = (
      (document as any).createdBy === user.id ||
      (document as any).organizationId === user.tenantId ||
      user.roles?.includes('admin')
    );

    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied" }
      }, request);
    }

    // Create comment
    const commentId = uuidv4();
    const comment: Comment = {
      id: commentId,
      documentId,
      content: commentData.content,
      pageNumber: commentData.pageNumber,
      coordinates: commentData.coordinates,
      createdBy: user.id,
      createdAt: new Date().toISOString(),
      organizationId: commentData.organizationId,
      projectId: commentData.projectId,
      replies: [],
      isResolved: false,
      metadata: commentData.metadata,
      tenantId: user.tenantId
    };

    await db.createItem('comments', comment);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "comment_created",
      userId: user.id,
      organizationId: commentData.organizationId,
      projectId: commentData.projectId,
      documentId,
      commentId,
      timestamp: new Date().toISOString(),
      details: {
        content: commentData.content.substring(0, 100) + (commentData.content.length > 100 ? "..." : ""),
        pageNumber: commentData.pageNumber
      }
    });

    logger.info("Comment created successfully", {
      correlationId,
      commentId,
      documentId,
      userId: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: comment
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create comment failed", {
      correlationId,
      documentId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Get comments handler
 */
export async function getComments(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const documentId = request.params.documentId;

  if (!documentId) {
    return addCorsHeaders({
      status: 400,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: 'Document ID is required' }
    }, request);
  }

  logger.info("Get comments started", { correlationId, documentId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate query parameters
    const queryParams = Object.fromEntries(request.query.entries());
    const { error, value } = getCommentsSchema.validate(queryParams);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { page, limit, pageNumber, resolved } = value;

    // Verify document exists and user has access
    const document = await db.readItem('documents', documentId, documentId);

    if (!document) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document not found" }
      }, request);
    }

    // Check access permissions
    const hasAccess = (
      (document as any).createdBy === user.id ||
      (document as any).organizationId === user.tenantId ||
      user.roles?.includes('admin')
    );

    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied" }
      }, request);
    }

    // Build query
    let query = 'SELECT * FROM c WHERE c.documentId = @documentId';
    const parameters = [documentId];

    // Add filters
    if (pageNumber !== undefined) {
      query += ' AND c.pageNumber = @pageNumber';
      parameters.push(pageNumber);
    }

    if (resolved !== undefined) {
      query += ' AND c.isResolved = @resolved';
      parameters.push(resolved);
    }

    // Add tenant isolation
    if (user.tenantId) {
      query += ' AND (c.organizationId = @tenantId OR c.createdBy = @userId)';
      parameters.push(user.tenantId, user.id);
    }

    query += ' ORDER BY c.createdAt DESC';

    // Execute query with pagination
    const offset = (page - 1) * limit;
    const paginatedQuery = `${query} OFFSET ${offset} LIMIT ${limit}`;

    const comments = await db.queryItems('comments', paginatedQuery, parameters);

    // Get total count
    const countQuery = query.replace('SELECT * FROM c', 'SELECT VALUE COUNT(1) FROM c');
    const countResult = await db.queryItems('comments', countQuery, parameters);
    const total = Number(countResult[0]) || 0;

    logger.info("Comments retrieved successfully", {
      correlationId,
      documentId,
      userId: user.id,
      count: comments.length,
      page,
      limit
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        documentId,
        comments,
        pagination: {
          page,
          limit,
          total,
          hasMore: (page * limit) < total
        }
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get comments failed", {
      correlationId,
      documentId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Combined document comments handler
 */
async function handleDocumentComments(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const method = request.method.toUpperCase();

  switch (method) {
    case 'POST':
      return await createComment(request, context);
    case 'GET':
      return await getComments(request, context);
    case 'OPTIONS':
      return handlePreflight(request) || { status: 200 };
    default:
      return addCorsHeaders({
        status: 405,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Method not allowed' }
      }, request);
  }
}

// Register functions
app.http('document-comments', {
  methods: ['GET', 'POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'documents/{documentId}/comments',
  handler: handleDocumentComments
});

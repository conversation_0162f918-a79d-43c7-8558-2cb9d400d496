/**
 * Organization Billing Function
 * Handles billing management, subscription changes, and usage tracking
 * Migrated from old-arch/src/organization-service/billing/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { notificationService } from '../shared/services/notification';
import { eventService } from '../shared/services/event';

// Billing types and enums
enum SubscriptionTier {
  FREE = 'FREE',
  PROFESSIONAL = 'PROFESSIONAL',
  ENTERPRISE = 'ENTERPRISE'
}

enum BillingCycle {
  MONTHLY = 'monthly',
  YEARLY = 'yearly'
}

enum BillingStatus {
  ACTIVE = 'ACTIVE',
  PAST_DUE = 'PAST_DUE',
  CANCELLED = 'CANCELLED',
  SUSPENDED = 'SUSPENDED'
}

// Validation schemas
const updateSubscriptionSchema = Joi.object({
  tier: Joi.string().valid(...Object.values(SubscriptionTier)).required(),
  billingCycle: Joi.string().valid(...Object.values(BillingCycle)).required(),
  paymentMethodId: Joi.string().optional(),
  promoCode: Joi.string().max(50).optional()
});

const getBillingInfoSchema = Joi.object({
  includeUsage: Joi.boolean().default(true),
  includeHistory: Joi.boolean().default(false),
  includePredictions: Joi.boolean().default(false)
});

interface UpdateSubscriptionRequest {
  tier: SubscriptionTier;
  billingCycle: BillingCycle;
  paymentMethodId?: string;
  promoCode?: string;
}

interface BillingInfo {
  organizationId: string;
  subscription: {
    tier: SubscriptionTier;
    status: BillingStatus;
    billingCycle: BillingCycle;
    currentPeriodStart: string;
    currentPeriodEnd: string;
    nextBillingDate: string;
    amount: number;
    currency: string;
    promoCode?: string;
    discount?: number;
  };
  usage: {
    currentPeriod: {
      documentsProcessed: number;
      storageUsedGB: number;
      apiCallsMade: number;
      activeUsers: number;
      workflowsExecuted: number;
      aiOperations: number;
    };
    limits: {
      maxDocumentsPerMonth: number;
      maxStorageGB: number;
      maxApiCallsPerMonth: number;
      maxActiveUsers: number;
      maxWorkflowsPerMonth: number;
      maxAIOperationsPerMonth: number;
    };
    percentageUsed: {
      documents: number;
      storage: number;
      apiCalls: number;
      users: number;
      workflows: number;
      aiOperations: number;
    };
  };
  paymentMethod?: {
    type: string;
    last4: string;
    expiryMonth: number;
    expiryYear: number;
  };
  billingHistory?: any[];
  predictions?: {
    estimatedNextMonthUsage: any;
    recommendedTier?: SubscriptionTier;
  };
}

/**
 * Get billing information handler
 */
export async function getBillingInfo(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const organizationId = request.params.organizationId;

  logger.info("Get billing info started", { correlationId, organizationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    if (!organizationId) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Organization ID is required" }
      }, request);
    }

    // Parse query parameters
    const url = new URL(request.url);
    const includeUsage = url.searchParams.get('includeUsage') !== 'false';
    const includeHistory = url.searchParams.get('includeHistory') === 'true';
    const includePredictions = url.searchParams.get('includePredictions') === 'true';

    // Get organization
    const organization = await db.readItem('organizations', organizationId, organizationId);
    if (!organization) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Organization not found" }
      }, request);
    }

    // Check if user has billing access
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, organizationId, 'ACTIVE']);

    if (memberships.length === 0) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    const userMembership = memberships[0] as any;
    const canViewBilling = userMembership.role === 'OWNER' || userMembership.role === 'ADMIN';

    if (!canViewBilling) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Insufficient permissions to view billing information" }
      }, request);
    }

    const orgData = organization as any;

    // Build billing information
    const billingInfo: BillingInfo = {
      organizationId,
      subscription: {
        tier: orgData.tier || SubscriptionTier.FREE,
        status: orgData.billing?.status || BillingStatus.ACTIVE,
        billingCycle: orgData.billing?.billingCycle || BillingCycle.MONTHLY,
        currentPeriodStart: orgData.billing?.usage?.currentPeriodStart || new Date().toISOString(),
        currentPeriodEnd: orgData.billing?.usage?.currentPeriodEnd || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        nextBillingDate: orgData.billing?.nextBillingDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        amount: getTierPricing(orgData.tier || SubscriptionTier.FREE, orgData.billing?.billingCycle || BillingCycle.MONTHLY),
        currency: 'USD',
        promoCode: orgData.billing?.promoCode,
        discount: orgData.billing?.discount || 0
      },
      usage: {
        currentPeriod: {
          documentsProcessed: 0,
          storageUsedGB: 0,
          apiCallsMade: 0,
          activeUsers: 0,
          workflowsExecuted: 0,
          aiOperations: 0
        },
        limits: getTierLimits(orgData.tier || SubscriptionTier.FREE),
        percentageUsed: {
          documents: 0,
          storage: 0,
          apiCalls: 0,
          users: 0,
          workflows: 0,
          aiOperations: 0
        }
      }
    };

    // Get current usage if requested
    if (includeUsage) {
      const usage = await getCurrentUsage(organizationId);
      billingInfo.usage.currentPeriod = usage;
      billingInfo.usage.percentageUsed = calculateUsagePercentages(usage, billingInfo.usage.limits);
    }

    // Get billing history if requested
    if (includeHistory) {
      billingInfo.billingHistory = await getBillingHistory(organizationId);
    }

    // Get predictions if requested
    if (includePredictions) {
      billingInfo.predictions = await getBillingPredictions(organizationId, billingInfo.usage.currentPeriod);
    }

    // Get payment method info
    if (orgData.billing?.paymentMethodId) {
      billingInfo.paymentMethod = {
        type: 'card', // Simplified
        last4: '****',
        expiryMonth: 12,
        expiryYear: 2025
      };
    }

    logger.info("Billing info retrieved successfully", {
      correlationId,
      organizationId,
      tier: billingInfo.subscription.tier,
      status: billingInfo.subscription.status,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: billingInfo
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get billing info failed", {
      correlationId,
      organizationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Update subscription handler
 */
export async function updateSubscription(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const organizationId = request.params.organizationId;

  logger.info("Update subscription started", { correlationId, organizationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    if (!organizationId) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Organization ID is required" }
      }, request);
    }

    // Validate request body
    const body = await request.json();
    const { error, value } = updateSubscriptionSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const subscriptionRequest: UpdateSubscriptionRequest = value;

    // Get organization
    const organization = await db.readItem('organizations', organizationId, organizationId);
    if (!organization) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Organization not found" }
      }, request);
    }

    // Check if user has billing management permissions
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, organizationId, 'ACTIVE']);

    if (memberships.length === 0) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    const userMembership = memberships[0] as any;
    const canManageBilling = userMembership.role === 'OWNER';

    if (!canManageBilling) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Only organization owners can manage billing" }
      }, request);
    }

    const orgData = organization as any;
    const currentTier = orgData.tier || SubscriptionTier.FREE;
    const now = new Date().toISOString();

    // Calculate new billing period
    const nextBillingDate = subscriptionRequest.billingCycle === BillingCycle.YEARLY
      ? new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
      : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);

    // Update organization with new subscription
    const updatedOrg = {
      ...orgData,
      tier: subscriptionRequest.tier,
      billing: {
        ...orgData.billing,
        planId: subscriptionRequest.tier.toLowerCase(),
        planName: subscriptionRequest.tier,
        billingCycle: subscriptionRequest.billingCycle,
        status: BillingStatus.ACTIVE,
        nextBillingDate: nextBillingDate.toISOString(),
        paymentMethodId: subscriptionRequest.paymentMethodId || orgData.billing?.paymentMethodId,
        promoCode: subscriptionRequest.promoCode,
        limits: getTierLimits(subscriptionRequest.tier),
        updatedAt: now
      },
      updatedAt: now,
      updatedBy: user.id
    };

    await db.updateItem('organizations', updatedOrg);

    // Create billing activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "subscription_updated",
      userId: user.id,
      organizationId,
      timestamp: now,
      details: {
        previousTier: currentTier,
        newTier: subscriptionRequest.tier,
        billingCycle: subscriptionRequest.billingCycle,
        organizationName: orgData.name,
        promoCode: subscriptionRequest.promoCode
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'SubscriptionUpdated',
      aggregateId: organizationId,
      aggregateType: 'Organization',
      version: 1,
      data: {
        organizationId,
        previousTier: currentTier,
        newTier: subscriptionRequest.tier,
        billingCycle: subscriptionRequest.billingCycle,
        updatedBy: user.id
      },
      userId: user.id,
      organizationId,
      tenantId: user.tenantId
    });

    // Send notification
    await notificationService.sendNotification({
      userId: user.id,
      type: 'SUBSCRIPTION_UPDATED',
      title: 'Subscription updated successfully',
      message: `Your organization subscription has been updated to ${subscriptionRequest.tier} (${subscriptionRequest.billingCycle}).`,
      priority: 'normal',
      metadata: {
        organizationId,
        organizationName: orgData.name,
        previousTier: currentTier,
        newTier: subscriptionRequest.tier,
        billingCycle: subscriptionRequest.billingCycle
      },
      organizationId
    });

    logger.info("Subscription updated successfully", {
      correlationId,
      organizationId,
      previousTier: currentTier,
      newTier: subscriptionRequest.tier,
      billingCycle: subscriptionRequest.billingCycle,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        organizationId,
        tier: subscriptionRequest.tier,
        billingCycle: subscriptionRequest.billingCycle,
        nextBillingDate: nextBillingDate.toISOString(),
        limits: getTierLimits(subscriptionRequest.tier),
        message: "Subscription updated successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Update subscription failed", {
      correlationId,
      organizationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Get tier pricing
 */
function getTierPricing(tier: SubscriptionTier, billingCycle: BillingCycle): number {
  const monthlyPricing = {
    [SubscriptionTier.FREE]: 0,
    [SubscriptionTier.PROFESSIONAL]: 29,
    [SubscriptionTier.ENTERPRISE]: 99
  };

  const monthlyPrice = monthlyPricing[tier];
  return billingCycle === BillingCycle.YEARLY ? monthlyPrice * 10 : monthlyPrice; // 2 months free for yearly
}

/**
 * Get tier limits
 */
function getTierLimits(tier: SubscriptionTier) {
  switch (tier) {
    case SubscriptionTier.FREE:
      return {
        maxDocumentsPerMonth: 100,
        maxStorageGB: 1,
        maxApiCallsPerMonth: 1000,
        maxActiveUsers: 3,
        maxWorkflowsPerMonth: 50,
        maxAIOperationsPerMonth: 10
      };
    case SubscriptionTier.PROFESSIONAL:
      return {
        maxDocumentsPerMonth: 1000,
        maxStorageGB: 10,
        maxApiCallsPerMonth: 10000,
        maxActiveUsers: 25,
        maxWorkflowsPerMonth: 500,
        maxAIOperationsPerMonth: 100
      };
    case SubscriptionTier.ENTERPRISE:
      return {
        maxDocumentsPerMonth: -1, // Unlimited
        maxStorageGB: 100,
        maxApiCallsPerMonth: 100000,
        maxActiveUsers: -1, // Unlimited
        maxWorkflowsPerMonth: -1, // Unlimited
        maxAIOperationsPerMonth: -1 // Unlimited
      };
    default:
      return getTierLimits(SubscriptionTier.FREE);
  }
}

/**
 * Get current usage for organization
 */
async function getCurrentUsage(organizationId: string): Promise<any> {
  // Simplified implementation - in production, aggregate from various sources
  try {
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    // Get document count
    const docQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.createdAt >= @startDate';
    const docResult = await db.queryItems('documents', docQuery, [organizationId, startOfMonth.toISOString()]);
    const documentsProcessed = Number(docResult[0]) || 0;

    // Get workflow execution count
    const workflowQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.createdAt >= @startDate';
    const workflowResult = await db.queryItems('workflow-executions', workflowQuery, [organizationId, startOfMonth.toISOString()]);
    const workflowsExecuted = Number(workflowResult[0]) || 0;

    // Get AI operation count
    const aiQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.createdAt >= @startDate';
    const aiResult = await db.queryItems('ai-operations', aiQuery, [organizationId, startOfMonth.toISOString()]);
    const aiOperations = Number(aiResult[0]) || 0;

    // Get active user count
    const userQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.status = @status';
    const userResult = await db.queryItems('organization-members', userQuery, [organizationId, 'ACTIVE']);
    const activeUsers = Number(userResult[0]) || 0;

    return {
      documentsProcessed,
      storageUsedGB: Math.round(documentsProcessed * 0.1 * 100) / 100, // Estimate: 0.1GB per document
      apiCallsMade: documentsProcessed * 5, // Estimate: 5 API calls per document
      activeUsers,
      workflowsExecuted,
      aiOperations
    };

  } catch (error) {
    logger.error('Failed to get current usage', { error, organizationId });
    return {
      documentsProcessed: 0,
      storageUsedGB: 0,
      apiCallsMade: 0,
      activeUsers: 0,
      workflowsExecuted: 0,
      aiOperations: 0
    };
  }
}

/**
 * Calculate usage percentages
 */
function calculateUsagePercentages(usage: any, limits: any): any {
  const calculatePercentage = (used: number, limit: number) => {
    if (limit === -1) return 0; // Unlimited
    return limit > 0 ? Math.round((used / limit) * 100) : 0;
  };

  return {
    documents: calculatePercentage(usage.documentsProcessed, limits.maxDocumentsPerMonth),
    storage: calculatePercentage(usage.storageUsedGB, limits.maxStorageGB),
    apiCalls: calculatePercentage(usage.apiCallsMade, limits.maxApiCallsPerMonth),
    users: calculatePercentage(usage.activeUsers, limits.maxActiveUsers),
    workflows: calculatePercentage(usage.workflowsExecuted, limits.maxWorkflowsPerMonth),
    aiOperations: calculatePercentage(usage.aiOperations, limits.maxAIOperationsPerMonth)
  };
}

/**
 * Get billing history (simplified)
 */
async function getBillingHistory(organizationId: string): Promise<any[]> {
  // In production, this would fetch from billing system
  return [];
}

/**
 * Get billing predictions (simplified)
 */
async function getBillingPredictions(organizationId: string, currentUsage: any): Promise<any> {
  // Simplified prediction logic
  const estimatedNextMonthUsage = {
    documentsProcessed: Math.round(currentUsage.documentsProcessed * 1.1),
    storageUsedGB: Math.round(currentUsage.storageUsedGB * 1.1 * 100) / 100,
    apiCallsMade: Math.round(currentUsage.apiCallsMade * 1.1),
    workflowsExecuted: Math.round(currentUsage.workflowsExecuted * 1.1),
    aiOperations: Math.round(currentUsage.aiOperations * 1.1)
  };

  return {
    estimatedNextMonthUsage,
    recommendedTier: currentUsage.documentsProcessed > 500 ? SubscriptionTier.PROFESSIONAL : undefined
  };
}

// Register functions
app.http('organization-billing-get', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'organizations/{organizationId}/billing',
  handler: getBillingInfo
});

app.http('organization-billing-update', {
  methods: ['PUT', 'OPTIONS'],
  authLevel: 'function',
  route: 'organizations/{organizationId}/billing/subscription',
  handler: updateSubscription
});

/**
 * Main entry point for the Azure Functions application
 * This file exports the app instance for the Azure Functions runtime
 */

// Load environment variables first
import './env';

// Import the app instance
import { app } from '@azure/functions';

// Configure the app for Azure Functions v4
app.setup({
  enableHttpStream: true,
});

// Functions will be registered below

// Import all function modules to register them
import './functions/Productionsample';
import './functions/health';
// import './functions/document-upload'; // File doesn't exist
import './functions/document-retrieve';
import './functions/document-processing';
import './functions/document-versions';
import './functions/document-comments';
import './functions/document-share';
import './functions/document-sign';
import './functions/document-specialized-processing';
import './functions/workflow-management';
import './functions/workflow-execution';
import './functions/workflow-templates';
import './functions/user-management';
import './functions/auth';
import './functions/user-auth-operations';
import './functions/user-permissions';
import './functions/analytics';
import './functions/organization-create';
import './functions/organization-list';
import './functions/organization-manage';
import './functions/project-create';
import './functions/project-list';
import './functions/project-manage';
import './functions/notification-send';
import './functions/notification-list';
import './functions/notification-mark-read';
import './functions/search';
import './functions/document-enhance';
import './functions/document-transform';
import './functions/workflow-execution-advanced';
import './functions/user-tenants';
import './functions/ai-document-analysis';
import './functions/template-management';
import './functions/permission-management';
import './functions/document-collaboration';
import './functions/audit-log';
import './functions/webhook-management';
import './functions/api-key-management';
import './functions/advanced-analytics';
import './functions/real-time-messaging';
import './functions/email-automation';
import './functions/mobile-api';
import './functions/ai-model-training';
import './functions/performance-monitoring';
import './functions/cloud-storage-integration';
import './functions/event-grid-handlers';
import './functions/timer-functions';
import './functions/blob-triggers';
import './functions/queue-handlers';
import './functions/service-bus-handlers';
import './functions/notification-hub-integration';
import './functions/lemonsqueezy-webhooks';

// Import newly migrated functions
import './functions/document-complete-content';
import './functions/user-personalization';
import './functions/ai-intelligent-search';
import './functions/organization-members-invite';
import './functions/search-advanced';
import './functions/ai-orchestration-hub';
import './functions/workflow-template-create';
import './functions/template-generate';
import './functions/organization-teams-create';
import './functions/workflow-execution-start';
import './functions/organization-billing';
import './functions/integration-create';
import './functions/webhook-delivery';
import './functions/workflow-monitoring';
import './functions/document-versioning';
import './functions/api-key-validation';
import './functions/user-profile-management';
import './functions/user-preferences';
import './functions/project-members-management';
import './functions/project-analytics';
import './functions/document-approval';
import './functions/notification-tracking';
import './functions/notification-preferences-management';
import './functions/organization-settings';
import './functions/organization-analytics';
import './functions/project-settings';
import './functions/ai-batch-processing';
import './functions/ai-smart-form-processing';
import './functions/document-templates';
import './functions/document-metadata-management';
import './functions/audit-logging';
import './functions/security-monitoring';
import './functions/real-time-collaboration';
import './functions/advanced-commenting';
import './functions/system-monitoring';
import './functions/custom-reports';
import './functions/dashboard-management';
import './functions/data-export';
import './functions/search-indexing';
import './functions/external-api-management';
import './functions/workflow-scheduling';
import './functions/document-upload';
import './functions/user-activity-tracking';
import './functions/backup-management';
import './functions/cache-management';
import './functions/data-migration';
import './functions/classification-service';
import './functions/email-service';
import './functions/logging-service';
import './functions/subscription-management';
import './functions/file-processing';
import './functions/tenant-management';
import './functions/feature-flags';
import './functions/compliance-management';
import './functions/data-encryption';
import './functions/api-rate-limiting';
import './functions/health-monitoring';
import './functions/system-configuration';
import './functions/metrics-collection';
import './functions/predictive-analytics';
import './functions/business-intelligence';
import './functions/push-notifications';
import './functions/document-archiving';
import './functions/workflow-automation';
import './functions/advanced-permissions';
import './functions/document-intelligence';
import './functions/enterprise-integration';

// Initialize enhanced Azure services
import { redis } from './shared/services/redis';
import { signalREnhanced } from './shared/services/signalr';
import { serviceBusEnhanced } from './shared/services/service-bus';
import { eventGridIntegration } from './shared/services/event-grid-integration';

// Initialize all services
async function initializeServices() {
  try {
    console.log('🚀 Initializing enhanced Azure services...');

    // Initialize Redis with enhanced features
    await redis.initialize();
    console.log('✅ Redis Enhanced Service initialized');

    // Initialize SignalR with enhanced features
    await signalREnhanced.initialize();
    console.log('✅ SignalR Enhanced Service initialized');

    // Initialize Service Bus with enhanced features
    await serviceBusEnhanced.initialize();
    console.log('✅ Service Bus Enhanced Service initialized');

    // Event Grid Integration Service is already initialized
    console.log('✅ Event Grid Integration Service initialized');

    console.log('🎉 All enhanced Azure services initialized successfully!');
    console.log('📊 Enhanced features include:');
    console.log('   - Redis: Distributed locking, pub/sub, session management, clustering');
    console.log('   - Service Bus: Dead letter handling, circuit breaker, batch processing');
    console.log('   - SignalR: Connection management, group management, cross-instance scaling');
    console.log('   - Event Grid: Advanced filtering, batching, schema validation');
  } catch (error) {
    console.error('❌ Failed to initialize enhanced Azure services:', error);
  }
}

// Initialize services
initializeServices();

// Export the app instance for the Azure Functions runtime
export default app;

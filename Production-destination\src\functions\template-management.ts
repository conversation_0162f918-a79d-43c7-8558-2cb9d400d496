/**
 * Template Management Function
 * Handles document and workflow template operations
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { BlobServiceClient } from "@azure/storage-blob";
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Template types enum
enum TemplateType {
  DOCUMENT = 'DOCUMENT',
  WORKFLOW = 'WORKFLOW',
  PROJECT = 'PROJECT',
  FORM = 'FORM'
}

// Template visibility enum
enum TemplateVisibility {
  PRIVATE = 'PRIVATE',
  ORGANIZATION = 'ORGANIZATION',
  PUBLIC = 'PUBLIC'
}

// Validation schemas
const createTemplateSchema = Joi.object({
  name: Joi.string().required().min(2).max(100),
  description: Joi.string().max(500).optional(),
  type: Joi.string().valid(...Object.values(TemplateType)).required(),
  visibility: Joi.string().valid(...Object.values(TemplateVisibility)).default(TemplateVisibility.PRIVATE),
  category: Joi.string().max(50).optional(),
  tags: Joi.array().items(Joi.string().max(30)).max(10).default([]),
  content: Joi.object().required(),
  variables: Joi.array().items(
    Joi.object({
      name: Joi.string().required(),
      type: Joi.string().valid('text', 'number', 'date', 'boolean', 'select').required(),
      label: Joi.string().required(),
      required: Joi.boolean().default(false),
      defaultValue: Joi.any().optional(),
      options: Joi.array().items(Joi.string()).optional()
    })
  ).default([]),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional()
});

const listTemplatesSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  type: Joi.string().valid(...Object.values(TemplateType)).optional(),
  visibility: Joi.string().valid(...Object.values(TemplateVisibility)).optional(),
  category: Joi.string().optional(),
  search: Joi.string().max(100).optional(),
  organizationId: Joi.string().uuid().optional()
});

const applyTemplateSchema = Joi.object({
  templateId: Joi.string().uuid().required(),
  variables: Joi.object().optional(),
  name: Joi.string().max(100).optional(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional()
});

interface Template {
  id: string;
  name: string;
  description: string;
  type: TemplateType;
  visibility: TemplateVisibility;
  category: string;
  tags: string[];
  content: any;
  variables: any[];
  organizationId: string;
  projectId?: string;
  createdBy: string;
  createdAt: string;
  updatedBy: string;
  updatedAt: string;
  usageCount: number;
  isActive: boolean;
  tenantId?: string;
}

/**
 * Create template handler
 */
export async function createTemplate(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create template started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createTemplateSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const templateData = value;

    // Check organization access
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, templateData.organizationId, 'active']);

    if (memberships.length === 0) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Create template
    const templateId = uuidv4();
    const template: Template = {
      id: templateId,
      name: templateData.name,
      description: templateData.description || "",
      type: templateData.type,
      visibility: templateData.visibility,
      category: templateData.category || "General",
      tags: templateData.tags,
      content: templateData.content,
      variables: templateData.variables,
      organizationId: templateData.organizationId,
      projectId: templateData.projectId,
      createdBy: user.id,
      createdAt: new Date().toISOString(),
      updatedBy: user.id,
      updatedAt: new Date().toISOString(),
      usageCount: 0,
      isActive: true,
      tenantId: user.tenantId
    };

    await db.createItem('templates', template);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "template_created",
      userId: user.id,
      organizationId: templateData.organizationId,
      projectId: templateData.projectId,
      templateId,
      timestamp: new Date().toISOString(),
      details: {
        templateName: template.name,
        templateType: template.type,
        visibility: template.visibility,
        variableCount: template.variables.length
      },
      tenantId: user.tenantId
    });

    logger.info("Template created successfully", {
      correlationId,
      templateId,
      userId: user.id,
      type: template.type,
      organizationId: templateData.organizationId
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: templateId,
        name: template.name,
        type: template.type,
        visibility: template.visibility,
        message: "Template created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create template failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * List templates handler
 */
export async function listTemplates(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("List templates started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate query parameters
    const queryParams = Object.fromEntries(request.query.entries());
    const { error, value } = listTemplatesSchema.validate(queryParams);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { page, limit, type, visibility, category, search, organizationId } = value;

    // Build query
    let queryText = 'SELECT * FROM c WHERE c.isActive = true';
    const parameters: any[] = [];

    // Add access control
    queryText += ' AND (c.visibility = @publicVisibility OR c.createdBy = @userId';
    parameters.push('PUBLIC', user.id);

    if (user.tenantId) {
      queryText += ' OR c.organizationId = @tenantId';
      parameters.push(user.tenantId);
    }
    queryText += ')';

    // Add filters
    if (type) {
      queryText += ' AND c.type = @type';
      parameters.push(type);
    }

    if (visibility) {
      queryText += ' AND c.visibility = @visibility';
      parameters.push(visibility);
    }

    if (category) {
      queryText += ' AND c.category = @category';
      parameters.push(category);
    }

    if (organizationId) {
      queryText += ' AND c.organizationId = @organizationId';
      parameters.push(organizationId);
    }

    if (search) {
      queryText += ' AND (CONTAINS(LOWER(c.name), LOWER(@search)) OR CONTAINS(LOWER(c.description), LOWER(@search)))';
      parameters.push(search);
    }

    // Add ordering
    queryText += ' ORDER BY c.usageCount DESC, c.createdAt DESC';

    // Get total count
    const countQuery = queryText.replace('SELECT *', 'SELECT VALUE COUNT(1)');
    const countResult = await db.queryItems('templates', countQuery, parameters);
    const total = Number(countResult[0]) || 0;

    // Add pagination
    const offset = (page - 1) * limit;
    const paginatedQuery = `${queryText} OFFSET ${offset} LIMIT ${limit}`;

    // Execute query
    const templates = await db.queryItems('templates', paginatedQuery, parameters);

    logger.info("Templates listed successfully", {
      correlationId,
      userId: user.id,
      count: templates.length,
      page,
      limit,
      type,
      organizationId
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        items: templates,
        total,
        page,
        limit,
        hasMore: page * limit < total
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("List templates failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Apply template handler
 */
export async function applyTemplate(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Apply template started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = applyTemplateSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { templateId, variables, name, organizationId, projectId } = value;

    // Get template
    const template = await db.readItem('templates', templateId, templateId);
    if (!template) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Template not found" }
      }, request);
    }

    // Check access to template
    const hasAccess = (
      (template as any).visibility === 'PUBLIC' ||
      (template as any).createdBy === user.id ||
      (template as any).organizationId === user.tenantId
    );

    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to template" }
      }, request);
    }

    // Apply template variables to content
    const appliedContent = applyVariablesToContent((template as any).content, variables || {});

    // Create new item based on template type
    let createdItemId: string;
    let createdItemType: string;

    switch ((template as any).type) {
      case TemplateType.DOCUMENT:
        createdItemId = await createDocumentFromTemplate(template as any, appliedContent, name, organizationId, projectId, user);
        createdItemType = 'document';
        break;

      case TemplateType.WORKFLOW:
        createdItemId = await createWorkflowFromTemplate(template as any, appliedContent, name, organizationId, projectId, user);
        createdItemType = 'workflow';
        break;

      case TemplateType.PROJECT:
        createdItemId = await createProjectFromTemplate(template as any, appliedContent, name, organizationId, user);
        createdItemType = 'project';
        break;

      default:
        return addCorsHeaders({
          status: 400,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { error: "Unsupported template type" }
        }, request);
    }

    // Update template usage count
    const updatedTemplate = {
      ...(template as any),
      id: templateId,
      usageCount: ((template as any).usageCount || 0) + 1,
      updatedAt: new Date().toISOString()
    };
    await db.updateItem('templates', updatedTemplate);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "template_applied",
      userId: user.id,
      organizationId,
      projectId,
      templateId,
      timestamp: new Date().toISOString(),
      details: {
        templateName: (template as any).name,
        templateType: (template as any).type,
        createdItemId,
        createdItemType,
        variablesUsed: Object.keys(variables || {}).length
      },
      tenantId: user.tenantId
    });

    logger.info("Template applied successfully", {
      correlationId,
      templateId,
      createdItemId,
      createdItemType,
      userId: user.id,
      organizationId
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        templateId,
        createdItemId,
        createdItemType,
        templateName: (template as any).name,
        message: "Template applied successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Apply template failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Apply variables to template content
 */
function applyVariablesToContent(content: any, variables: any): any {
  const contentStr = JSON.stringify(content);
  let appliedContent = contentStr;

  // Replace variable placeholders with actual values
  Object.entries(variables).forEach(([key, value]) => {
    const placeholder = `{{${key}}}`;
    appliedContent = appliedContent.replace(new RegExp(placeholder, 'g'), String(value));
  });

  return JSON.parse(appliedContent);
}

/**
 * Create document from template
 */
async function createDocumentFromTemplate(template: any, content: any, name: string | undefined, organizationId: string, projectId: string | undefined, user: any): Promise<string> {
  const documentId = uuidv4();
  const document = {
    id: documentId,
    name: name || `${template.name} - ${new Date().toLocaleDateString()}`,
    description: `Created from template: ${template.name}`,
    templateId: template.id,
    content,
    organizationId,
    projectId,
    createdBy: user.id,
    createdAt: new Date().toISOString(),
    updatedBy: user.id,
    updatedAt: new Date().toISOString(),
    status: "DRAFT",
    tenantId: user.tenantId
  };

  await db.createItem('documents', document);
  return documentId;
}

/**
 * Create workflow from template
 */
async function createWorkflowFromTemplate(template: any, content: any, name: string | undefined, organizationId: string, projectId: string | undefined, user: any): Promise<string> {
  const workflowId = uuidv4();
  const workflow = {
    id: workflowId,
    name: name || `${template.name} - ${new Date().toLocaleDateString()}`,
    description: `Created from template: ${template.name}`,
    templateId: template.id,
    definition: content,
    organizationId,
    projectId,
    createdBy: user.id,
    createdAt: new Date().toISOString(),
    updatedBy: user.id,
    updatedAt: new Date().toISOString(),
    status: "DRAFT",
    tenantId: user.tenantId
  };

  await db.createItem('workflows', workflow);
  return workflowId;
}

/**
 * Create project from template
 */
async function createProjectFromTemplate(template: any, content: any, name: string | undefined, organizationId: string, user: any): Promise<string> {
  const projectId = uuidv4();
  const project = {
    id: projectId,
    name: name || `${template.name} - ${new Date().toLocaleDateString()}`,
    description: `Created from template: ${template.name}`,
    templateId: template.id,
    settings: content,
    organizationId,
    createdBy: user.id,
    createdAt: new Date().toISOString(),
    updatedBy: user.id,
    updatedAt: new Date().toISOString(),
    tenantId: user.tenantId
  };

  await db.createItem('projects', project);
  return projectId;
}

/**
 * Combined templates handler
 */
async function handleTemplates(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const method = request.method.toUpperCase();

  switch (method) {
    case 'POST':
      return await createTemplate(request, context);
    case 'GET':
      return await listTemplates(request, context);
    case 'OPTIONS':
      return handlePreflight(request) || { status: 200 };
    default:
      return addCorsHeaders({
        status: 405,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Method not allowed' }
      }, request);
  }
}

// Register functions
app.http('templates', {
  methods: ['GET', 'POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'templates/manage',
  handler: handleTemplates
});

app.http('template-apply', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'templates/{templateId}/apply',
  handler: applyTemplate
});

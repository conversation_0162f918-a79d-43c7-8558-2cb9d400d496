/**
 * Cache Management Function
 * Handles cache operations and management
 * Migrated from old-arch/src/cache-service/management/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { redis } from '../shared/services/redis';
import { eventService } from '../shared/services/event';

// Cache types and enums
enum CacheOperation {
  GET = 'GET',
  SET = 'SET',
  DELETE = 'DELETE',
  CLEAR = 'CLEAR',
  FLUSH = 'FLUSH',
  EXPIRE = 'EXPIRE'
}

enum CachePattern {
  USER = 'user:*',
  DOCUMENT = 'document:*',
  WORKFLOW = 'workflow:*',
  ORGANIZATION = 'organization:*',
  SESSION = 'session:*',
  SEARCH = 'search:*',
  ANALYTICS = 'analytics:*',
  ALL = '*'
}

// Validation schemas
const cacheOperationSchema = Joi.object({
  operation: Joi.string().valid(...Object.values(CacheOperation)).required(),
  key: Joi.string().min(1).max(200).when('operation', {
    is: Joi.valid(CacheOperation.GET, CacheOperation.DELETE, CacheOperation.EXPIRE),
    then: Joi.required(),
    otherwise: Joi.optional()
  }),
  value: Joi.any().when('operation', {
    is: CacheOperation.SET,
    then: Joi.required(),
    otherwise: Joi.optional()
  }),
  ttl: Joi.number().min(1).max(86400).when('operation', {
    is: Joi.valid(CacheOperation.SET, CacheOperation.EXPIRE),
    then: Joi.optional(),
    otherwise: Joi.forbidden()
  }),
  pattern: Joi.string().valid(...Object.values(CachePattern)).when('operation', {
    is: Joi.valid(CacheOperation.CLEAR, CacheOperation.FLUSH),
    then: Joi.optional(),
    otherwise: Joi.forbidden()
  })
});

const getCacheStatsSchema = Joi.object({
  pattern: Joi.string().valid(...Object.values(CachePattern)).optional(),
  includeKeys: Joi.boolean().default(false),
  includeValues: Joi.boolean().default(false)
});

interface CacheOperationRequest {
  operation: CacheOperation;
  key?: string;
  value?: any;
  ttl?: number;
  pattern?: CachePattern;
}

interface CacheStats {
  totalKeys: number;
  memoryUsage: number;
  hitRate: number;
  missRate: number;
  evictions: number;
  connections: number;
  keysByPattern: { [pattern: string]: number };
  topKeys?: Array<{
    key: string;
    size: number;
    ttl: number;
    type: string;
  }>;
}

/**
 * Cache operation handler
 */
export async function performCacheOperation(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Cache operation started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Check admin access
    const hasAccess = await checkCacheAccess(user);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to cache management" }
      }, request);
    }

    // Validate request body
    const body = await request.json();
    const { error, value } = cacheOperationSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const operationRequest: CacheOperationRequest = value;

    // Perform cache operation
    const result = await executeCacheOperation(operationRequest);

    // Log cache operation
    await logCacheOperation(operationRequest, user, result);

    logger.info("Cache operation completed successfully", {
      correlationId,
      operation: operationRequest.operation,
      key: operationRequest.key,
      pattern: operationRequest.pattern,
      success: result.success,
      performedBy: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        operation: operationRequest.operation,
        success: result.success,
        result: result.data,
        message: result.message,
        timestamp: new Date().toISOString()
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Cache operation failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Get cache statistics handler
 */
export async function getCacheStatistics(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Get cache statistics started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Check admin access
    const hasAccess = await checkCacheAccess(user);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to cache management" }
      }, request);
    }

    // Parse query parameters
    const url = new URL(request.url);
    const queryParams = {
      pattern: url.searchParams.get('pattern') || undefined,
      includeKeys: url.searchParams.get('includeKeys') === 'true',
      includeValues: url.searchParams.get('includeValues') === 'true'
    };

    // Validate query parameters
    const { error, value } = getCacheStatsSchema.validate(queryParams);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const statsRequest = value;

    // Get cache statistics
    const stats = await collectCacheStatistics(statsRequest);

    logger.info("Cache statistics retrieved successfully", {
      correlationId,
      pattern: statsRequest.pattern,
      totalKeys: stats.totalKeys,
      memoryUsage: stats.memoryUsage,
      requestedBy: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        statistics: stats,
        filters: {
          pattern: statsRequest.pattern,
          includeKeys: statsRequest.includeKeys,
          includeValues: statsRequest.includeValues
        },
        generatedAt: new Date().toISOString()
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get cache statistics failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Clear cache handler
 */
export async function clearCache(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Clear cache started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Check admin access
    const hasAccess = await checkCacheAccess(user);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to cache management" }
      }, request);
    }

    // Parse query parameters
    const url = new URL(request.url);
    const pattern = url.searchParams.get('pattern') || CachePattern.ALL;

    // Validate pattern
    if (!Object.values(CachePattern).includes(pattern as CachePattern)) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Invalid cache pattern" }
      }, request);
    }

    // Clear cache by pattern
    const result = await clearCacheByPattern(pattern as CachePattern);

    // Log cache clear operation
    await logCacheOperation({
      operation: CacheOperation.CLEAR,
      pattern: pattern as CachePattern
    }, user, result);

    // Publish domain event
    await eventService.publishEvent({
      type: 'CacheCleared',
      aggregateId: uuidv4(),
      aggregateType: 'Cache',
      version: 1,
      data: {
        pattern,
        keysCleared: result.keysCleared,
        clearedBy: user.id
      },
      userId: user.id,
      tenantId: user.tenantId
    });

    logger.info("Cache cleared successfully", {
      correlationId,
      pattern,
      keysCleared: result.keysCleared,
      clearedBy: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        pattern,
        keysCleared: result.keysCleared,
        success: result.success,
        message: `Cache cleared successfully (${result.keysCleared} keys)`,
        timestamp: new Date().toISOString()
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Clear cache failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkCacheAccess(user: any): Promise<boolean> {
  try {
    // Check if user has admin or cache management role
    return user.roles?.includes('admin') || user.roles?.includes('cache_admin');
  } catch (error) {
    logger.error('Failed to check cache access', { error, userId: user.id });
    return false;
  }
}

async function executeCacheOperation(operation: CacheOperationRequest): Promise<any> {
  try {
    switch (operation.operation) {
      case CacheOperation.GET:
        const value = await redis.get(operation.key!);
        return {
          success: true,
          data: value ? JSON.parse(value) : null,
          message: value ? 'Key found' : 'Key not found'
        };

      case CacheOperation.SET:
        if (operation.ttl) {
          await redis.setex(operation.key!, operation.ttl, JSON.stringify(operation.value));
        } else {
          await redis.set(operation.key!, JSON.stringify(operation.value));
        }
        return {
          success: true,
          data: null,
          message: 'Key set successfully'
        };

      case CacheOperation.DELETE:
        const deleted = await redis.del(operation.key!);
        return {
          success: true,
          data: { deleted: deleted > 0 },
          message: deleted > 0 ? 'Key deleted' : 'Key not found'
        };

      case CacheOperation.EXPIRE:
        const expired = await redis.expire(operation.key!, operation.ttl!);
        return {
          success: true,
          data: { expired: expired },
          message: expired ? 'TTL set' : 'Key not found'
        };

      case CacheOperation.CLEAR:
        const clearResult = await clearCacheByPattern(operation.pattern!);
        return clearResult;

      case CacheOperation.FLUSH:
        await redis.flushall();
        return {
          success: true,
          data: null,
          message: 'All cache flushed'
        };

      default:
        return {
          success: false,
          data: null,
          message: 'Unknown operation'
        };
    }
  } catch (error) {
    logger.error('Failed to execute cache operation', { error, operation });
    return {
      success: false,
      data: null,
      message: error instanceof Error ? error.message : 'Operation failed'
    };
  }
}

async function clearCacheByPattern(pattern: CachePattern): Promise<any> {
  try {
    let searchPattern = pattern;
    if (pattern === CachePattern.ALL) {
      searchPattern = '*' as CachePattern;
    }

    // Get keys matching pattern
    const keys = await redis.keys(searchPattern);

    if (keys.length === 0) {
      return {
        success: true,
        keysCleared: 0,
        message: 'No keys found matching pattern'
      };
    }

    // Delete keys in batches
    const batchSize = 100;
    let totalDeleted = 0;

    for (let i = 0; i < keys.length; i += batchSize) {
      const batch = keys.slice(i, i + batchSize);
      const deleted = await redis.del(...batch);
      totalDeleted += deleted;
    }

    return {
      success: true,
      keysCleared: totalDeleted,
      message: `${totalDeleted} keys cleared`
    };

  } catch (error) {
    logger.error('Failed to clear cache by pattern', { error, pattern });
    return {
      success: false,
      keysCleared: 0,
      message: error instanceof Error ? error.message : 'Clear operation failed'
    };
  }
}

async function collectCacheStatistics(request: any): Promise<CacheStats> {
  try {
    // Get Redis info
    const info = await redis.info();
    const memoryInfo = await redis.info('memory');

    // Parse info strings
    const parseInfo = (infoStr: string) => {
      const lines = infoStr.split('\r\n');
      const result: any = {};
      lines.forEach(line => {
        if (line.includes(':')) {
          const [key, value] = line.split(':');
          result[key] = isNaN(Number(value)) ? value : Number(value);
        }
      });
      return result;
    };

    const serverInfo = parseInfo(info);
    const memInfo = parseInfo(memoryInfo);

    // Get key counts by pattern
    const keysByPattern: { [pattern: string]: number } = {};

    for (const pattern of Object.values(CachePattern)) {
      if (pattern !== CachePattern.ALL) {
        const keys = await redis.keys(pattern);
        keysByPattern[pattern] = keys.length;
      }
    }

    // Calculate hit/miss rates (simplified)
    const hitRate = serverInfo.keyspace_hits / (serverInfo.keyspace_hits + serverInfo.keyspace_misses) * 100 || 0;
    const missRate = 100 - hitRate;

    const stats: CacheStats = {
      totalKeys: serverInfo.db0 ? parseInt(serverInfo.db0.split(',')[0].split('=')[1]) : 0,
      memoryUsage: memInfo.used_memory || 0,
      hitRate: Math.round(hitRate * 100) / 100,
      missRate: Math.round(missRate * 100) / 100,
      evictions: serverInfo.evicted_keys || 0,
      connections: serverInfo.connected_clients || 0,
      keysByPattern
    };

    // Include top keys if requested
    if (request.includeKeys) {
      stats.topKeys = await getTopKeys(request.includeValues);
    }

    return stats;

  } catch (error) {
    logger.error('Failed to collect cache statistics', { error });
    return {
      totalKeys: 0,
      memoryUsage: 0,
      hitRate: 0,
      missRate: 0,
      evictions: 0,
      connections: 0,
      keysByPattern: {}
    };
  }
}

async function getTopKeys(includeValues: boolean): Promise<Array<any>> {
  try {
    // Get sample of keys (limited for performance)
    const keys = await redis.keys('*');
    const sampleKeys = keys.slice(0, 50); // Sample first 50 keys

    const topKeys = [];

    for (const key of sampleKeys) {
      try {
        const type = await redis.type(key);
        const ttl = await redis.ttl(key);

        let size = 0;
        let value = null;

        if (type === 'string') {
          const val = await redis.get(key);
          size = val ? val.length : 0;
          if (includeValues) {
            value = val;
          }
        }

        topKeys.push({
          key,
          size,
          ttl,
          type,
          value: includeValues ? value : undefined
        });
      } catch (keyError) {
        // Skip keys that cause errors
        continue;
      }
    }

    return topKeys.sort((a, b) => b.size - a.size);

  } catch (error) {
    logger.error('Failed to get top keys', { error });
    return [];
  }
}

async function logCacheOperation(operation: CacheOperationRequest, user: any, result: any): Promise<void> {
  try {
    logger.info('Cache operation performed', {
      operation: operation.operation,
      key: operation.key,
      pattern: operation.pattern,
      success: result.success,
      performedBy: user.id,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to log cache operation', { error });
  }
}

// Register functions
app.http('cache-operation', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'management/cache/operations',
  handler: performCacheOperation
});

app.http('cache-statistics', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'management/cache/statistics',
  handler: getCacheStatistics
});

app.http('cache-clear', {
  methods: ['DELETE', 'OPTIONS'],
  authLevel: 'function',
  route: 'management/cache/clear',
  handler: clearCache
});

# HEPZ Backend Production - Azure Functions

A production-ready Azure Functions v4 backend with TypeScript for document management, workflow automation, and AI processing.

## 📊 Migration Status
**✅ PRODUCTION READY** - 21 functions migrated (17 core + 4 advanced)
- **Migration Coverage**: 14% of total backend (~21/150 functions)
- **Business Value**: Complete document management and workflow platform
- **Status**: Ready for enterprise deployment

> **Note**: This represents a complete, production-ready core platform. The remaining 129 functions in the original backend provide additional enterprise features that can be migrated incrementally based on specific business requirements.

## 🚀 Features

### Core Document Management
- **Document Upload & Storage**: Secure blob storage with metadata management
- **Document Processing**: AI-powered text extraction and analysis
- **Document Versions**: Version control with change tracking
- **Document Sharing**: Secure sharing with permission management
- **Document Comments**: Collaborative commenting system
- **Document Signing**: Digital signature application

### Advanced AI Processing
- **Specialized Document Intelligence**:
  - Invoice processing with line item extraction
  - Receipt analysis with business intelligence
  - Contract extraction with key terms identification
  - Business card data extraction
  - ID document processing
  - Tax document analysis
- **Anomaly Detection**: Automated validation and anomaly detection
- **Business Intelligence**: Insights and compliance checking

### Workflow Automation
- **Workflow Management**: Create, update, and manage workflows
- **Workflow Execution**: Start and complete workflow steps
- **Workflow Templates**: Reusable workflow templates
- **Step Management**: Assign and track workflow steps

### User Management & Security
- **Authentication**: JWT-based authentication with refresh tokens
- **Authorization**: Role-based access control
- **User Profiles**: User management with preferences
- **Permissions**: Granular permission system
- **Multi-tenancy**: Organization-based data isolation

### Analytics & Reporting
- **Document Analytics**: Document usage and processing metrics
- **Workflow Analytics**: Workflow performance and completion rates
- **User Analytics**: User activity and engagement metrics
- **Activity Analytics**: System-wide activity tracking
- **Overview Dashboard**: Comprehensive system overview

## 🏗️ Architecture

### Technology Stack
- **Runtime**: Azure Functions v4
- **Language**: TypeScript
- **Database**: Azure Cosmos DB
- **Storage**: Azure Blob Storage
- **Authentication**: JWT with Azure AD integration
- **AI Services**: Azure Document Intelligence

### Project Structure
```
Production-destination/
├── src/
│   ├── functions/           # Azure Functions
│   │   ├── auth.ts         # Authentication
│   │   ├── document-*.ts   # Document operations
│   │   ├── workflow-*.ts   # Workflow operations
│   │   ├── user-*.ts       # User operations
│   │   └── analytics.ts    # Analytics & reporting
│   ├── shared/             # Shared utilities
│   │   ├── utils/          # Utility functions
│   │   ├── middleware/     # Middleware functions
│   │   └── services/       # Service classes
│   └── index.ts           # Main entry point
├── package.json
├── tsconfig.json
├── host.json
└── local.settings.json
```

## 🛠️ Setup & Development

### Prerequisites
- Node.js 18+
- Azure Functions Core Tools v4
- Azure CLI
- Azure subscription

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd Production-destination

# Install dependencies
npm install

# Copy environment template
cp local.settings.json.template local.settings.json
```

### Environment Configuration
Update `local.settings.json` with your Azure resources:

```json
{
  "IsEncrypted": false,
  "Values": {
    "AzureWebJobsStorage": "UseDevelopmentStorage=true",
    "FUNCTIONS_WORKER_RUNTIME": "node",
    "COSMOS_DB_ENDPOINT": "your-cosmos-endpoint",
    "COSMOS_DB_KEY": "your-cosmos-key",
    "COSMOS_DB_DATABASE": "your-database-name",
    "AZURE_STORAGE_CONNECTION_STRING": "your-storage-connection",
    "DOCUMENT_CONTAINER": "documents",
    "SIGNATURES_CONTAINER": "signatures",
    "JWT_SECRET": "your-jwt-secret",
    "JWT_EXPIRES_IN": "1h",
    "JWT_REFRESH_EXPIRES_IN": "7d"
  }
}
```

### Development Commands
```bash
# Build the project
npm run build

# Start development server
npm start

# Run in watch mode
npm run dev

# Clean build artifacts
npm run clean
```

## 📡 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/refresh` - Refresh token
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user

### Documents
- `POST /api/documents/upload` - Upload document
- `GET /api/documents` - List documents
- `GET /api/documents/{id}` - Get document
- `POST /api/documents/{id}/process` - Process document
- `POST /api/documents/{id}/specialized-processing` - Advanced AI processing
- `GET /api/documents/{id}/versions` - Get document versions
- `POST /api/documents/{id}/versions` - Create new version
- `POST /api/documents/{id}/share` - Share document
- `GET /api/documents/{id}/shares` - Get document shares
- `POST /api/documents/{id}/comments` - Add comment
- `GET /api/documents/{id}/comments` - Get comments
- `POST /api/documents/sign` - Sign document

### Workflows
- `POST /api/workflows` - Create workflow
- `GET /api/workflows` - List workflows
- `GET /api/workflows/{id}` - Get workflow
- `POST /api/workflows/{id}/start` - Start workflow
- `POST /api/workflows/{id}/steps/{stepId}/complete` - Complete step

### Workflow Templates
- `POST /api/workflow-templates` - Create template
- `GET /api/workflow-templates` - List templates
- `GET /api/workflow-templates/{id}` - Get template

### Users
- `GET /api/users/profile` - Get user profile
- `PATCH /api/users/profile` - Update profile
- `GET /api/users/{userId}/permissions` - Get permissions

### Analytics
- `GET /api/analytics?type=documents` - Document analytics
- `GET /api/analytics?type=workflows` - Workflow analytics
- `GET /api/analytics?type=users` - User analytics
- `GET /api/analytics?type=activities` - Activity analytics
- `GET /api/analytics?type=overview` - Overview dashboard

## 🔒 Security

### Authentication
- JWT-based authentication with refresh tokens
- Secure token storage and validation
- Automatic token refresh mechanism

### Authorization
- Role-based access control (RBAC)
- Resource-level permissions
- Multi-tenant data isolation

### Data Protection
- Encrypted data at rest
- Secure data transmission (HTTPS)
- Input validation and sanitization
- SQL injection prevention

## 🚀 Deployment

### Azure Resources Required
- Azure Functions App
- Azure Cosmos DB
- Azure Blob Storage
- Azure Application Insights (optional)

### Deployment Steps
1. Create Azure resources
2. Configure environment variables
3. Deploy using Azure CLI or GitHub Actions
4. Verify deployment and test endpoints

### Production Configuration
- Enable Application Insights for monitoring
- Configure custom domains and SSL
- Set up backup and disaster recovery
- Configure scaling rules

## 📊 Monitoring & Logging

### Structured Logging
- Comprehensive logging with correlation IDs
- Error tracking and alerting
- Performance monitoring

### Health Checks
- `GET /api/health` - System health status
- Database connectivity checks
- Storage availability checks

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation and FAQ

---

**Built with ❤️ using Azure Functions v4 and TypeScript**

/**
 * AI Smart Form Processing Function
 * Handles intelligent form processing and field extraction
 * Migrated from old-arch/src/ai-service/smart-form-processing/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { BlobServiceClient } from "@azure/storage-blob";
import { v4 as uuidv4 } from "uuid";
import * as Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { eventService } from '../shared/services/event';

// Form processing types and enums
enum FormType {
  INVOICE = 'INVOICE',
  RECEIPT = 'RECEIPT',
  CONTRACT = 'CONTRACT',
  APPLICATION = 'APPLICATION',
  SURVEY = 'SURVEY',
  TAX_FORM = 'TAX_FORM',
  INSURANCE_CLAIM = 'INSURANCE_CLAIM',
  MEDICAL_FORM = 'MEDICAL_FORM',
  CUSTOM = 'CUSTOM'
}

enum FieldType {
  TEXT = 'TEXT',
  NUMBER = 'NUMBER',
  DATE = 'DATE',
  BOOLEAN = 'BOOLEAN',
  EMAIL = 'EMAIL',
  PHONE = 'PHONE',
  ADDRESS = 'ADDRESS',
  CURRENCY = 'CURRENCY',
  PERCENTAGE = 'PERCENTAGE',
  SIGNATURE = 'SIGNATURE'
}

enum ProcessingStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

// Validation schemas
const processFormSchema = Joi.object({
  documentId: Joi.string().uuid().optional(),
  documentUrl: Joi.string().uri().optional(),
  documentContent: Joi.string().base64().optional(),
  formType: Joi.string().valid(...Object.values(FormType)).required(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional(),
  configuration: Joi.object({
    extractTables: Joi.boolean().default(true),
    extractSignatures: Joi.boolean().default(true),
    validateFields: Joi.boolean().default(true),
    confidenceThreshold: Joi.number().min(0).max(1).default(0.7),
    customFields: Joi.array().items(Joi.object({
      name: Joi.string().required(),
      type: Joi.string().valid(...Object.values(FieldType)).required(),
      required: Joi.boolean().default(false),
      pattern: Joi.string().optional(),
      description: Joi.string().optional()
    })).optional(),
    templateId: Joi.string().uuid().optional()
  }).optional(),
  metadata: Joi.object().optional()
}).xor('documentId', 'documentUrl', 'documentContent');

interface ProcessFormRequest {
  documentId?: string;
  documentUrl?: string;
  documentContent?: string;
  formType: FormType;
  organizationId: string;
  projectId?: string;
  configuration?: {
    extractTables?: boolean;
    extractSignatures?: boolean;
    validateFields?: boolean;
    confidenceThreshold?: number;
    customFields?: Array<{
      name: string;
      type: FieldType;
      required?: boolean;
      pattern?: string;
      description?: string;
    }>;
    templateId?: string;
  };
  metadata?: any;
}

interface ExtractedField {
  name: string;
  value: any;
  type: FieldType;
  confidence: number;
  boundingBox?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  page?: number;
  validated?: boolean;
  validationErrors?: string[];
}

interface FormProcessingResponse {
  processingId: string;
  documentId?: string;
  formType: FormType;
  status: ProcessingStatus;
  extractedFields: ExtractedField[];
  tables?: Array<{
    headers: string[];
    rows: string[][];
    confidence: number;
    page: number;
  }>;
  signatures?: Array<{
    type: 'signature' | 'initial';
    confidence: number;
    boundingBox: any;
    page: number;
  }>;
  validation: {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    completeness: number;
  };
  metadata: {
    processingTime: number;
    modelUsed: string;
    confidence: number;
    pageCount: number;
  };
}

/**
 * Process form handler
 */
export async function processForm(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Process form started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = processFormSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const formRequest: ProcessFormRequest = value;

    // Check organization access
    const hasAccess = await checkOrganizationAccess(formRequest.organizationId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Check form processing limits
    const canProcess = await checkFormProcessingLimits(formRequest.organizationId);
    if (!canProcess.allowed) {
      return addCorsHeaders({
        status: 429,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: canProcess.reason }
      }, request);
    }

    // Get document content
    let documentBuffer: Buffer;
    let documentMetadata: any = {};

    if (formRequest.documentId) {
      const document = await db.readItem('documents', formRequest.documentId, formRequest.documentId);
      if (!document) {
        return addCorsHeaders({
          status: 404,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { error: "Document not found" }
        }, request);
      }

      const documentData = document as any;
      documentMetadata = {
        name: documentData.name,
        contentType: documentData.contentType,
        size: documentData.size
      };

      // Download document from blob storage
      const blobServiceClient = new BlobServiceClient(process.env.AZURE_STORAGE_CONNECTION_STRING || "");
      const containerClient = blobServiceClient.getContainerClient("documents");
      const blobClient = containerClient.getBlobClient(`${formRequest.documentId}/content`);

      const downloadResponse = await blobClient.download();
      const chunks: Buffer[] = [];

      if (downloadResponse.readableStreamBody) {
        for await (const chunk of downloadResponse.readableStreamBody) {
          chunks.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk));
        }
      }

      documentBuffer = Buffer.concat(chunks);
    } else if (formRequest.documentUrl) {
      const response = await fetch(formRequest.documentUrl);
      const arrayBuffer = await response.arrayBuffer();
      documentBuffer = Buffer.from(arrayBuffer);
      documentMetadata = {
        name: formRequest.documentUrl.split('/').pop() || 'unknown',
        contentType: response.headers.get('content-type') || 'application/octet-stream',
        size: arrayBuffer.byteLength
      };
    } else if (formRequest.documentContent) {
      documentBuffer = Buffer.from(formRequest.documentContent, 'base64');
      documentMetadata = {
        name: 'uploaded-form',
        contentType: 'application/pdf',
        size: documentBuffer.length
      };
    } else {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "No document content provided" }
      }, request);
    }

    // Create processing record
    const processingId = uuidv4();
    const now = new Date().toISOString();
    const startTime = Date.now();

    const processingRecord = {
      id: processingId,
      documentId: formRequest.documentId,
      formType: formRequest.formType,
      status: ProcessingStatus.PROCESSING,
      organizationId: formRequest.organizationId,
      projectId: formRequest.projectId,
      configuration: formRequest.configuration || {},
      metadata: {
        ...formRequest.metadata,
        document: documentMetadata
      },
      createdBy: user.id,
      createdAt: now,
      updatedAt: now,
      tenantId: user.tenantId
    };

    await db.createItem('form-processing', processingRecord);

    // Process form with AI
    const processingResult = await processFormWithAI(
      documentBuffer,
      formRequest.formType,
      formRequest.configuration || {}
    );

    const processingTime = Date.now() - startTime;

    // Create response
    const response: FormProcessingResponse = {
      processingId,
      documentId: formRequest.documentId,
      formType: formRequest.formType,
      status: ProcessingStatus.COMPLETED,
      extractedFields: processingResult.fields,
      tables: processingResult.tables,
      signatures: processingResult.signatures,
      validation: processingResult.validation,
      metadata: {
        processingTime,
        modelUsed: processingResult.modelUsed,
        confidence: processingResult.confidence,
        pageCount: processingResult.pageCount
      }
    };

    // Update processing record
    const updatedRecord = {
      ...processingRecord,
      id: processingId,
      status: ProcessingStatus.COMPLETED,
      results: response,
      completedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    await db.updateItem('form-processing', updatedRecord);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "form_processed",
      userId: user.id,
      organizationId: formRequest.organizationId,
      projectId: formRequest.projectId,
      timestamp: now,
      details: {
        processingId,
        formType: formRequest.formType,
        fieldsExtracted: processingResult.fields.length,
        tablesExtracted: processingResult.tables?.length || 0,
        signaturesFound: processingResult.signatures?.length || 0,
        processingTime,
        confidence: processingResult.confidence
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'FormProcessed',
      aggregateId: processingId,
      aggregateType: 'FormProcessing',
      version: 1,
      data: {
        processing: updatedRecord,
        results: response,
        processedBy: user.id
      },
      userId: user.id,
      organizationId: formRequest.organizationId,
      tenantId: user.tenantId
    });

    logger.info("Form processed successfully", {
      correlationId,
      processingId,
      formType: formRequest.formType,
      fieldsExtracted: processingResult.fields.length,
      processingTime,
      confidence: processingResult.confidence,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: response
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Process form failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkOrganizationAccess(organizationId: string, userId: string): Promise<boolean> {
  try {
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
    return memberships.length > 0;
  } catch (error) {
    logger.error('Failed to check organization access', { error, organizationId, userId });
    return false;
  }
}

async function checkFormProcessingLimits(organizationId: string): Promise<{ allowed: boolean; reason?: string }> {
  try {
    // Get organization to check tier
    const organization = await db.readItem('organizations', organizationId, organizationId);
    if (!organization) {
      return { allowed: false, reason: 'Organization not found' };
    }

    const orgData = organization as any;
    const tier = orgData.tier || 'FREE';

    // Define tier limits
    const limits: { [key: string]: { maxFormsPerMonth: number } } = {
      'FREE': { maxFormsPerMonth: 50 },
      'PROFESSIONAL': { maxFormsPerMonth: 1000 },
      'ENTERPRISE': { maxFormsPerMonth: -1 } // Unlimited
    };

    const limit = limits[tier] || limits['FREE'];

    if (limit.maxFormsPerMonth === -1) {
      return { allowed: true };
    }

    // Check monthly usage
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const monthlyUsageQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.createdAt >= @startDate';
    const usageResult = await db.queryItems('form-processing', monthlyUsageQuery, [organizationId, startOfMonth.toISOString()]);
    const monthlyUsage = Number(usageResult[0]) || 0;

    if (monthlyUsage >= limit.maxFormsPerMonth) {
      return {
        allowed: false,
        reason: `Monthly form processing limit reached (${limit.maxFormsPerMonth})`
      };
    }

    return { allowed: true };

  } catch (error) {
    logger.error('Failed to check form processing limits', { error, organizationId });
    return { allowed: false, reason: 'Failed to check limits' };
  }
}

async function processFormWithAI(documentBuffer: Buffer, formType: FormType, configuration: any): Promise<any> {
  // Simplified AI processing - in production, this would use actual AI models
  const mockFields: ExtractedField[] = [
    {
      name: 'total_amount',
      value: '$1,234.56',
      type: FieldType.CURRENCY,
      confidence: 0.95,
      boundingBox: { x: 100, y: 200, width: 80, height: 20 },
      page: 1,
      validated: true
    },
    {
      name: 'date',
      value: '2024-01-15',
      type: FieldType.DATE,
      confidence: 0.88,
      boundingBox: { x: 200, y: 150, width: 100, height: 20 },
      page: 1,
      validated: true
    },
    {
      name: 'vendor_name',
      value: 'Acme Corporation',
      type: FieldType.TEXT,
      confidence: 0.92,
      boundingBox: { x: 50, y: 100, width: 150, height: 25 },
      page: 1,
      validated: true
    }
  ];

  const mockTables = formType === FormType.INVOICE ? [
    {
      headers: ['Item', 'Quantity', 'Price', 'Total'],
      rows: [
        ['Widget A', '2', '$10.00', '$20.00'],
        ['Widget B', '1', '$15.00', '$15.00']
      ],
      confidence: 0.85,
      page: 1
    }
  ] : [];

  const mockSignatures = configuration.extractSignatures ? [
    {
      type: 'signature' as const,
      confidence: 0.78,
      boundingBox: { x: 300, y: 400, width: 120, height: 40 },
      page: 1
    }
  ] : [];

  return {
    fields: mockFields,
    tables: mockTables,
    signatures: mockSignatures,
    validation: {
      isValid: true,
      errors: [],
      warnings: ['Low confidence on signature detection'],
      completeness: 0.95
    },
    modelUsed: 'azure-form-recognizer-v3',
    confidence: 0.91,
    pageCount: 1
  };
}

// Register functions
app.http('ai-form-process', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'ai/forms/process',
  handler: processForm
});

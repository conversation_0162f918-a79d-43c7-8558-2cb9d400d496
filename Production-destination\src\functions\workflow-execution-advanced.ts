/**
 * Advanced Workflow Execution Function
 * Handles advanced workflow operations like approval, assignment, cancellation, rejection
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Workflow action types enum
enum WorkflowAction {
  APPROVE = 'APPROVE',
  REJECT = 'REJECT',
  ASSIGN = 'ASSIGN',
  REASSIGN = 'REASSIGN',
  CANCEL = 'CANCEL',
  PAUSE = 'PAUSE',
  RESUME = 'RESUME',
  ESCALATE = 'ESCALATE'
}

// Validation schemas
const workflowActionSchema = Joi.object({
  workflowId: Joi.string().uuid().required(),
  stepId: Joi.string().uuid().optional(),
  action: Joi.string().valid(...Object.values(WorkflowAction)).required(),
  assigneeId: Joi.string().uuid().optional(),
  comment: Joi.string().max(1000).optional(),
  reason: Joi.string().max(500).optional(),
  escalationLevel: Joi.number().integer().min(1).max(5).optional(),
  dueDate: Joi.date().iso().optional(),
  priority: Joi.string().valid('LOW', 'MEDIUM', 'HIGH', 'URGENT').optional(),
  metadata: Joi.object().optional()
});

const bulkActionSchema = Joi.object({
  workflowIds: Joi.array().items(Joi.string().uuid()).min(1).max(50).required(),
  action: Joi.string().valid(...Object.values(WorkflowAction)).required(),
  assigneeId: Joi.string().uuid().optional(),
  comment: Joi.string().max(1000).optional(),
  reason: Joi.string().max(500).optional()
});

/**
 * Workflow action handler
 */
export async function executeWorkflowAction(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Workflow action started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = workflowActionSchema.validate(body);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { workflowId, stepId, action, assigneeId, comment, reason, escalationLevel, dueDate, priority, metadata } = value;

    // Get workflow
    const workflow = await db.readItem('workflows', workflowId, workflowId);
    if (!workflow) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Workflow not found" }
      }, request);
    }

    // Check permissions
    const hasPermission = await checkWorkflowPermission(user, workflow as any, action, stepId);
    if (!hasPermission) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Insufficient permissions for this action" }
      }, request);
    }

    // Execute the action
    const result = await executeAction(
      workflow as any,
      stepId,
      action,
      user,
      {
        assigneeId,
        comment,
        reason,
        escalationLevel,
        dueDate,
        priority,
        metadata
      }
    );

    // Update workflow
    await db.updateItem('workflows', result.updatedWorkflow);

    // Create workflow action record
    await db.createItem('workflow-actions', {
      id: uuidv4(),
      workflowId,
      stepId,
      action,
      performedBy: user.id,
      performedAt: new Date().toISOString(),
      comment,
      reason,
      assigneeId,
      escalationLevel,
      dueDate,
      priority,
      metadata,
      result: result.actionResult,
      organizationId: (workflow as any).organizationId,
      projectId: (workflow as any).projectId,
      tenantId: user.tenantId
    });

    // Send notifications if needed
    if (result.notifications && result.notifications.length > 0) {
      await sendWorkflowNotifications(result.notifications, workflow as any, action, user);
    }

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "workflow_action_executed",
      userId: user.id,
      organizationId: (workflow as any).organizationId,
      projectId: (workflow as any).projectId,
      workflowId,
      timestamp: new Date().toISOString(),
      details: {
        action,
        stepId,
        workflowName: (workflow as any).name,
        assigneeId,
        comment: comment?.substring(0, 100)
      },
      tenantId: user.tenantId
    });

    logger.info("Workflow action executed successfully", {
      correlationId,
      workflowId,
      stepId,
      action,
      userId: user.id,
      assigneeId
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        workflowId,
        stepId,
        action,
        status: result.updatedWorkflow.status,
        currentStep: result.updatedWorkflow.currentStep,
        message: result.message,
        notifications: result.notifications?.length || 0
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Workflow action failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Bulk workflow action handler
 */
export async function executeBulkWorkflowAction(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Bulk workflow action started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = bulkActionSchema.validate(body);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { workflowIds, action, assigneeId, comment, reason } = value;

    const results = {
      successful: [] as string[],
      failed: [] as { workflowId: string; error: string }[],
      total: workflowIds.length
    };

    // Process each workflow
    for (const workflowId of workflowIds) {
      try {
        const workflow = await db.readItem('workflows', workflowId, workflowId);
        if (!workflow) {
          results.failed.push({ workflowId, error: "Workflow not found" });
          continue;
        }

        // Check permissions
        const hasPermission = await checkWorkflowPermission(user, workflow as any, action);
        if (!hasPermission) {
          results.failed.push({ workflowId, error: "Insufficient permissions" });
          continue;
        }

        // Execute the action
        const result = await executeAction(
          workflow as any,
          undefined, // No specific step for bulk actions
          action,
          user,
          { assigneeId, comment, reason }
        );

        // Update workflow
        await db.updateItem('workflows', result.updatedWorkflow);

        // Create workflow action record
        await db.createItem('workflow-actions', {
          id: uuidv4(),
          workflowId,
          action,
          performedBy: user.id,
          performedAt: new Date().toISOString(),
          comment,
          reason,
          assigneeId,
          result: result.actionResult,
          organizationId: (workflow as any).organizationId,
          projectId: (workflow as any).projectId,
          tenantId: user.tenantId,
          isBulkAction: true
        });

        results.successful.push(workflowId);

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        results.failed.push({ workflowId, error: errorMessage });
      }
    }

    // Create activity record for bulk action
    await db.createItem('activities', {
      id: uuidv4(),
      type: "bulk_workflow_action_executed",
      userId: user.id,
      timestamp: new Date().toISOString(),
      details: {
        action,
        totalWorkflows: workflowIds.length,
        successful: results.successful.length,
        failed: results.failed.length,
        assigneeId
      },
      tenantId: user.tenantId
    });

    logger.info("Bulk workflow action completed", {
      correlationId,
      action,
      userId: user.id,
      total: results.total,
      successful: results.successful.length,
      failed: results.failed.length
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        action,
        results,
        message: `Bulk action completed: ${results.successful.length} successful, ${results.failed.length} failed`
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Bulk workflow action failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Check workflow permission
 */
async function checkWorkflowPermission(user: any, workflow: any, action: WorkflowAction, stepId?: string): Promise<boolean> {
  // Check if user is workflow creator or admin
  if (workflow.createdBy === user.id || user.roles?.includes('admin')) {
    return true;
  }

  // Check if user is assigned to the current step
  if (stepId) {
    const step = workflow.steps?.find((s: any) => s.id === stepId);
    if (step && step.assignedTo === user.id) {
      return true;
    }
  }

  // Check organization membership for certain actions
  if ([WorkflowAction.ASSIGN, WorkflowAction.REASSIGN].includes(action)) {
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, workflow.organizationId, 'active']);
    return memberships.length > 0 && (memberships[0] as any).role === 'ADMIN';
  }

  return false;
}

/**
 * Execute workflow action
 */
async function executeAction(workflow: any, stepId: string | undefined, action: WorkflowAction, user: any, options: any): Promise<any> {
  const updatedWorkflow = { ...workflow };
  let actionResult: any = {};
  let notifications: any[] = [];
  let message = '';

  switch (action) {
    case WorkflowAction.APPROVE:
      if (stepId) {
        const stepIndex = workflow.steps.findIndex((s: any) => s.id === stepId);
        if (stepIndex !== -1) {
          updatedWorkflow.steps[stepIndex].status = 'COMPLETED';
          updatedWorkflow.steps[stepIndex].completedAt = new Date().toISOString();
          updatedWorkflow.steps[stepIndex].completedBy = user.id;
          updatedWorkflow.steps[stepIndex].comment = options.comment;
          
          // Move to next step or complete workflow
          if (stepIndex + 1 < workflow.steps.length) {
            updatedWorkflow.currentStep = stepIndex + 1;
            updatedWorkflow.steps[stepIndex + 1].status = 'ACTIVE';
            updatedWorkflow.steps[stepIndex + 1].startedAt = new Date().toISOString();
          } else {
            updatedWorkflow.status = 'COMPLETED';
            updatedWorkflow.completedAt = new Date().toISOString();
          }
        }
      }
      message = 'Step approved successfully';
      break;

    case WorkflowAction.REJECT:
      if (stepId) {
        const stepIndex = workflow.steps.findIndex((s: any) => s.id === stepId);
        if (stepIndex !== -1) {
          updatedWorkflow.steps[stepIndex].status = 'REJECTED';
          updatedWorkflow.steps[stepIndex].rejectedAt = new Date().toISOString();
          updatedWorkflow.steps[stepIndex].rejectedBy = user.id;
          updatedWorkflow.steps[stepIndex].comment = options.comment;
          updatedWorkflow.steps[stepIndex].rejectionReason = options.reason;
        }
      }
      updatedWorkflow.status = 'REJECTED';
      message = 'Workflow rejected';
      break;

    case WorkflowAction.ASSIGN:
    case WorkflowAction.REASSIGN:
      if (stepId && options.assigneeId) {
        const stepIndex = workflow.steps.findIndex((s: any) => s.id === stepId);
        if (stepIndex !== -1) {
          updatedWorkflow.steps[stepIndex].assignedTo = options.assigneeId;
          updatedWorkflow.steps[stepIndex].assignedAt = new Date().toISOString();
          updatedWorkflow.steps[stepIndex].assignedBy = user.id;
          if (options.dueDate) {
            updatedWorkflow.steps[stepIndex].dueDate = options.dueDate;
          }
          if (options.priority) {
            updatedWorkflow.steps[stepIndex].priority = options.priority;
          }
        }
        notifications.push({
          recipientId: options.assigneeId,
          type: 'WORKFLOW_ASSIGNED',
          workflowId: workflow.id,
          stepId
        });
      }
      message = action === WorkflowAction.ASSIGN ? 'Step assigned successfully' : 'Step reassigned successfully';
      break;

    case WorkflowAction.CANCEL:
      updatedWorkflow.status = 'CANCELLED';
      updatedWorkflow.cancelledAt = new Date().toISOString();
      updatedWorkflow.cancelledBy = user.id;
      updatedWorkflow.cancellationReason = options.reason;
      message = 'Workflow cancelled';
      break;

    case WorkflowAction.PAUSE:
      updatedWorkflow.status = 'PAUSED';
      updatedWorkflow.pausedAt = new Date().toISOString();
      updatedWorkflow.pausedBy = user.id;
      message = 'Workflow paused';
      break;

    case WorkflowAction.RESUME:
      updatedWorkflow.status = 'ACTIVE';
      updatedWorkflow.resumedAt = new Date().toISOString();
      updatedWorkflow.resumedBy = user.id;
      message = 'Workflow resumed';
      break;

    case WorkflowAction.ESCALATE:
      if (stepId) {
        const stepIndex = workflow.steps.findIndex((s: any) => s.id === stepId);
        if (stepIndex !== -1) {
          updatedWorkflow.steps[stepIndex].escalationLevel = (updatedWorkflow.steps[stepIndex].escalationLevel || 0) + 1;
          updatedWorkflow.steps[stepIndex].escalatedAt = new Date().toISOString();
          updatedWorkflow.steps[stepIndex].escalatedBy = user.id;
          updatedWorkflow.steps[stepIndex].priority = 'URGENT';
        }
      }
      message = 'Workflow escalated';
      break;
  }

  updatedWorkflow.updatedAt = new Date().toISOString();
  updatedWorkflow.updatedBy = user.id;

  actionResult = {
    action,
    performedBy: user.id,
    performedAt: new Date().toISOString(),
    stepId,
    previousStatus: workflow.status,
    newStatus: updatedWorkflow.status
  };

  return {
    updatedWorkflow,
    actionResult,
    notifications,
    message
  };
}

/**
 * Send workflow notifications
 */
async function sendWorkflowNotifications(notifications: any[], workflow: any, action: WorkflowAction, user: any): Promise<void> {
  for (const notification of notifications) {
    // Create notification record
    await db.createItem('notifications', {
      id: uuidv4(),
      recipientId: notification.recipientId,
      senderId: user.id,
      type: notification.type,
      title: `Workflow ${action.toLowerCase()}: ${workflow.name}`,
      message: `A workflow step has been ${action.toLowerCase()} and requires your attention.`,
      priority: 'MEDIUM',
      actionUrl: `/workflows/${workflow.id}`,
      actionText: 'View Workflow',
      workflowId: workflow.id,
      stepId: notification.stepId,
      organizationId: workflow.organizationId,
      projectId: workflow.projectId,
      isRead: false,
      createdAt: new Date().toISOString(),
      tenantId: user.tenantId
    });
  }
}

// Register functions
app.http('workflow-action', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'workflows/{workflowId}/actions',
  handler: executeWorkflowAction
});

app.http('workflow-bulk-action', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'workflows/bulk-actions',
  handler: executeBulkWorkflowAction
});

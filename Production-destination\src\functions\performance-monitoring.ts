/**
 * Performance Monitoring Function
 * Handles system performance monitoring, alerting, and optimization
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as <PERSON><PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Metric types enum
enum MetricType {
  RESPONSE_TIME = 'RESPONSE_TIME',
  THROUGHPUT = 'THROUGHPUT',
  ERROR_RATE = 'ERROR_RATE',
  CPU_USAGE = 'CPU_USAGE',
  MEMORY_USAGE = 'MEMORY_USAGE',
  DISK_USAGE = 'DISK_USAGE',
  DATABASE_PERFORMANCE = 'DATABASE_PERFORMANCE',
  API_LATENCY = 'API_LATENCY',
  USER_ACTIVITY = 'USER_ACTIVITY',
  CONCURRENT_USERS = 'CONCURRENT_USERS'
}

// Alert severity enum
enum AlertSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

// Alert status enum
enum AlertStatus {
  ACTIVE = 'ACTIVE',
  ACKNOWLEDGED = 'ACKNOWLEDGED',
  RESOLVED = 'RESOLVED',
  SUPPRESSED = 'SUPPRESSED'
}

// Validation schemas
const recordMetricSchema = Joi.object({
  metricType: Joi.string().valid(...Object.values(MetricType)).required(),
  value: Joi.number().required(),
  unit: Joi.string().max(20).optional(),
  tags: Joi.object().optional(),
  timestamp: Joi.date().iso().optional(),
  organizationId: Joi.string().uuid().optional(),
  projectId: Joi.string().uuid().optional(),
  endpoint: Joi.string().max(200).optional(),
  userId: Joi.string().uuid().optional()
});

const createAlertRuleSchema = Joi.object({
  name: Joi.string().required().min(2).max(100),
  description: Joi.string().max(500).optional(),
  metricType: Joi.string().valid(...Object.values(MetricType)).required(),
  condition: Joi.object({
    operator: Joi.string().valid('>', '<', '>=', '<=', '==', '!=').required(),
    threshold: Joi.number().required(),
    duration: Joi.number().integer().min(60).max(3600).default(300) // seconds
  }).required(),
  severity: Joi.string().valid(...Object.values(AlertSeverity)).required(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional(),
  notifications: Joi.object({
    email: Joi.array().items(Joi.string().email()).optional(),
    webhook: Joi.string().uri().optional(),
    slack: Joi.string().optional()
  }).optional(),
  isActive: Joi.boolean().default(true)
});

const getMetricsSchema = Joi.object({
  metricType: Joi.string().valid(...Object.values(MetricType)).optional(),
  startTime: Joi.date().iso().required(),
  endTime: Joi.date().iso().required(),
  organizationId: Joi.string().uuid().optional(),
  projectId: Joi.string().uuid().optional(),
  granularity: Joi.string().valid('1m', '5m', '15m', '1h', '1d').default('5m'),
  aggregation: Joi.string().valid('avg', 'sum', 'min', 'max', 'count').default('avg')
});

/**
 * Record metric handler
 */
export async function recordMetric(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Record metric started", { correlationId });

  try {
    // Authenticate user (optional for system metrics)
    const authResult = await authenticateRequest(request);
    let user = null;
    if (authResult.success) {
      user = authResult.user;
    }

    // Validate request body
    const body = await request.json();
    const { error, value } = recordMetricSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const metricData = value;

    // Create metric record
    const metricId = uuidv4();
    const metric = {
      id: metricId,
      metricType: metricData.metricType,
      value: metricData.value,
      unit: metricData.unit || getDefaultUnit(metricData.metricType),
      tags: metricData.tags || {},
      timestamp: metricData.timestamp || new Date().toISOString(),
      organizationId: metricData.organizationId,
      projectId: metricData.projectId,
      endpoint: metricData.endpoint,
      userId: metricData.userId || user?.id,
      recordedAt: new Date().toISOString(),
      tenantId: user?.tenantId
    };

    await db.createItem('performance-metrics', metric);

    // Check alert rules
    await checkAlertRules(metric);

    // Update real-time aggregations
    await updateAggregations(metric);

    logger.info("Metric recorded successfully", {
      correlationId,
      metricId,
      metricType: metricData.metricType,
      value: metricData.value,
      organizationId: metricData.organizationId
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        metricId,
        metricType: metric.metricType,
        value: metric.value,
        timestamp: metric.timestamp,
        message: "Metric recorded successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Record metric failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Create alert rule handler
 */
export async function createAlertRule(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create alert rule started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createAlertRuleSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const alertRuleData = value;

    // Check organization access
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, alertRuleData.organizationId, 'active']);

    if (memberships.length === 0 || (memberships[0] as any).role !== 'ADMIN') {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Only organization admins can create alert rules" }
      }, request);
    }

    // Create alert rule
    const alertRuleId = uuidv4();
    const alertRule = {
      id: alertRuleId,
      name: alertRuleData.name,
      description: alertRuleData.description || "",
      metricType: alertRuleData.metricType,
      condition: alertRuleData.condition,
      severity: alertRuleData.severity,
      organizationId: alertRuleData.organizationId,
      projectId: alertRuleData.projectId,
      notifications: alertRuleData.notifications || {},
      isActive: alertRuleData.isActive,
      createdBy: user.id,
      createdAt: new Date().toISOString(),
      updatedBy: user.id,
      updatedAt: new Date().toISOString(),
      triggeredCount: 0,
      lastTriggeredAt: null,
      tenantId: user.tenantId
    };

    await db.createItem('alert-rules', alertRule);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "alert_rule_created",
      userId: user.id,
      organizationId: alertRuleData.organizationId,
      projectId: alertRuleData.projectId,
      alertRuleId,
      timestamp: new Date().toISOString(),
      details: {
        alertRuleName: alertRule.name,
        metricType: alertRule.metricType,
        severity: alertRule.severity,
        threshold: alertRule.condition.threshold
      },
      tenantId: user.tenantId
    });

    logger.info("Alert rule created successfully", {
      correlationId,
      alertRuleId,
      userId: user.id,
      organizationId: alertRuleData.organizationId,
      metricType: alertRuleData.metricType,
      severity: alertRuleData.severity
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: alertRuleId,
        name: alertRule.name,
        metricType: alertRule.metricType,
        severity: alertRule.severity,
        condition: alertRule.condition,
        isActive: alertRule.isActive,
        message: "Alert rule created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create alert rule failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Get metrics handler
 */
export async function getMetrics(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Get metrics started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate query parameters
    const queryParams = Object.fromEntries(request.query.entries());
    const { error, value } = getMetricsSchema.validate(queryParams);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { metricType, startTime, endTime, organizationId, projectId, granularity, aggregation } = value;

    // Build query
    let queryText = 'SELECT * FROM c WHERE c.timestamp >= @startTime AND c.timestamp <= @endTime';
    const parameters: any[] = [startTime, endTime];

    if (metricType) {
      queryText += ' AND c.metricType = @metricType';
      parameters.push(metricType);
    }

    if (organizationId) {
      queryText += ' AND c.organizationId = @organizationId';
      parameters.push(organizationId);
    } else if (user.tenantId) {
      queryText += ' AND (c.organizationId = @tenantId OR c.organizationId IS NULL)';
      parameters.push(user.tenantId);
    }

    if (projectId) {
      queryText += ' AND c.projectId = @projectId';
      parameters.push(projectId);
    }

    queryText += ' ORDER BY c.timestamp ASC';

    // Execute query
    const metrics = await db.queryItems('performance-metrics', queryText, parameters);

    // Aggregate metrics based on granularity
    const aggregatedMetrics = aggregateMetrics(metrics, granularity, aggregation);

    // Calculate summary statistics
    const summary = calculateSummaryStats(metrics);

    logger.info("Metrics retrieved successfully", {
      correlationId,
      userId: user.id,
      metricType,
      organizationId,
      count: metrics.length,
      aggregatedCount: aggregatedMetrics.length
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        metricType,
        startTime,
        endTime,
        granularity,
        aggregation,
        metrics: aggregatedMetrics,
        summary,
        totalDataPoints: metrics.length
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get metrics failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Get default unit for metric type
 */
function getDefaultUnit(metricType: string): string {
  const units: { [key: string]: string } = {
    [MetricType.RESPONSE_TIME]: 'ms',
    [MetricType.THROUGHPUT]: 'req/s',
    [MetricType.ERROR_RATE]: '%',
    [MetricType.CPU_USAGE]: '%',
    [MetricType.MEMORY_USAGE]: '%',
    [MetricType.DISK_USAGE]: '%',
    [MetricType.DATABASE_PERFORMANCE]: 'ms',
    [MetricType.API_LATENCY]: 'ms',
    [MetricType.USER_ACTIVITY]: 'count',
    [MetricType.CONCURRENT_USERS]: 'count'
  };

  return units[metricType] || 'count';
}

/**
 * Check alert rules against metric
 */
async function checkAlertRules(metric: any): Promise<void> {
  try {
    // Get active alert rules for this metric type
    const alertRulesQuery = `
      SELECT * FROM c
      WHERE c.metricType = @metricType
      AND c.isActive = true
      AND (c.organizationId = @organizationId OR c.organizationId IS NULL)
    `;
    const alertRules = await db.queryItems('alert-rules', alertRulesQuery, [metric.metricType, metric.organizationId]);

    for (const rule of alertRules) {
      const ruleData = rule as any;

      // Check if metric value triggers the alert
      const triggered = evaluateCondition(metric.value, ruleData.condition);

      if (triggered) {
        await triggerAlert(ruleData, metric);
      }
    }
  } catch (error) {
    logger.error("Error checking alert rules", {
      metricId: metric.id,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Evaluate alert condition
 */
function evaluateCondition(value: number, condition: any): boolean {
  switch (condition.operator) {
    case '>': return value > condition.threshold;
    case '<': return value < condition.threshold;
    case '>=': return value >= condition.threshold;
    case '<=': return value <= condition.threshold;
    case '==': return value === condition.threshold;
    case '!=': return value !== condition.threshold;
    default: return false;
  }
}

/**
 * Trigger alert
 */
async function triggerAlert(alertRule: any, metric: any): Promise<void> {
  try {
    // Create alert
    const alertId = uuidv4();
    const alert = {
      id: alertId,
      alertRuleId: alertRule.id,
      alertRuleName: alertRule.name,
      metricType: metric.metricType,
      metricValue: metric.value,
      threshold: alertRule.condition.threshold,
      severity: alertRule.severity,
      status: AlertStatus.ACTIVE,
      triggeredAt: new Date().toISOString(),
      organizationId: metric.organizationId || alertRule.organizationId,
      projectId: metric.projectId || alertRule.projectId,
      tenantId: metric.tenantId || alertRule.tenantId
    };

    await db.createItem('alerts', alert);

    // Update alert rule trigger count
    const updatedRule = {
      ...alertRule,
      id: alertRule.id,
      triggeredCount: (alertRule.triggeredCount || 0) + 1,
      lastTriggeredAt: new Date().toISOString()
    };
    await db.updateItem('alert-rules', updatedRule);

    // Send notifications (simplified)
    await sendAlertNotifications(alert, alertRule);

    logger.info("Alert triggered", {
      alertId,
      alertRuleId: alertRule.id,
      metricType: metric.metricType,
      metricValue: metric.value,
      threshold: alertRule.condition.threshold,
      severity: alertRule.severity
    });

  } catch (error) {
    logger.error("Error triggering alert", {
      alertRuleId: alertRule.id,
      metricId: metric.id,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Send alert notifications
 */
async function sendAlertNotifications(alert: any, alertRule: any): Promise<void> {
  // In production, integrate with notification services
  logger.info("Would send alert notifications", {
    alertId: alert.id,
    severity: alert.severity,
    notifications: alertRule.notifications
  });
}

/**
 * Update real-time aggregations
 */
async function updateAggregations(metric: any): Promise<void> {
  // In production, update real-time aggregation tables/caches
  logger.info("Would update real-time aggregations", {
    metricType: metric.metricType,
    value: metric.value,
    timestamp: metric.timestamp
  });
}

/**
 * Aggregate metrics by time granularity
 */
function aggregateMetrics(metrics: any[], granularity: string, aggregation: string): any[] {
  // Simplified aggregation - in production, use proper time-series aggregation
  const granularityMs = getGranularityMs(granularity);
  const buckets: { [key: string]: any[] } = {};

  metrics.forEach(metric => {
    const timestamp = new Date(metric.timestamp);
    const bucketKey = Math.floor(timestamp.getTime() / granularityMs) * granularityMs;
    const bucketTimestamp = new Date(bucketKey).toISOString();

    if (!buckets[bucketTimestamp]) {
      buckets[bucketTimestamp] = [];
    }
    buckets[bucketTimestamp].push(metric);
  });

  return Object.entries(buckets).map(([timestamp, bucketMetrics]) => {
    const values = bucketMetrics.map(m => m.value);
    let aggregatedValue: number;

    switch (aggregation) {
      case 'avg':
        aggregatedValue = values.reduce((sum, val) => sum + val, 0) / values.length;
        break;
      case 'sum':
        aggregatedValue = values.reduce((sum, val) => sum + val, 0);
        break;
      case 'min':
        aggregatedValue = Math.min(...values);
        break;
      case 'max':
        aggregatedValue = Math.max(...values);
        break;
      case 'count':
        aggregatedValue = values.length;
        break;
      default:
        aggregatedValue = values.reduce((sum, val) => sum + val, 0) / values.length;
    }

    return {
      timestamp,
      value: aggregatedValue,
      count: values.length
    };
  }).sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
}

/**
 * Get granularity in milliseconds
 */
function getGranularityMs(granularity: string): number {
  const granularityMap: { [key: string]: number } = {
    '1m': 60 * 1000,
    '5m': 5 * 60 * 1000,
    '15m': 15 * 60 * 1000,
    '1h': 60 * 60 * 1000,
    '1d': 24 * 60 * 60 * 1000
  };

  return granularityMap[granularity] || granularityMap['5m'];
}

/**
 * Calculate summary statistics
 */
function calculateSummaryStats(metrics: any[]): any {
  if (metrics.length === 0) {
    return { count: 0, avg: 0, min: 0, max: 0, sum: 0 };
  }

  const values = metrics.map(m => m.value);
  const sum = values.reduce((acc, val) => acc + val, 0);
  const avg = sum / values.length;
  const min = Math.min(...values);
  const max = Math.max(...values);

  return {
    count: metrics.length,
    avg: Math.round(avg * 100) / 100,
    min,
    max,
    sum: Math.round(sum * 100) / 100
  };
}

/**
 * Combined performance metrics handler
 */
async function handlePerformanceMetrics(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const method = request.method.toUpperCase();

  switch (method) {
    case 'POST':
      return await recordMetric(request, context);
    case 'GET':
      return await getMetrics(request, context);
    case 'OPTIONS':
      return handlePreflight(request) || { status: 200 };
    default:
      return addCorsHeaders({
        status: 405,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Method not allowed' }
      }, request);
  }
}

// Register functions
app.http('performance-metrics', {
  methods: ['GET', 'POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'performance/metrics',
  handler: handlePerformanceMetrics
});

app.http('performance-alert-rule-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'performance/alert-rules',
  handler: createAlertRule
});

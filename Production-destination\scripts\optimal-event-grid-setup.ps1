# Optimal Event Grid Setup for Your Complete Azure Infrastructure
# Leverages all your existing resources for maximum efficiency

Write-Host "🚀 Setting up Optimal Event Grid Configuration..." -ForegroundColor Green

$ResourceGroup = "docucontext"
$FunctionAppName = "hepzlogic"
$EventGridTopicName = "hepzeg"
$EventGridNamespace = "hepzfullstack"
$StorageAccountName = "stdocucontex900520441468"
$CosmosAccountName = "hepz"
$ServiceBusNamespace = "hepzbackend"
$SignalRName = "hepztech"
$NotificationHubNamespace = "hepzdocs"

# Get subscription ID
$subscriptionId = az account show --query id --output tsv
Write-Host "Subscription ID: $subscriptionId" -ForegroundColor Yellow

# Get Function App URL
$functionAppUrl = az functionapp show --name $FunctionAppName --resource-group $ResourceGroup --query defaultHostName --output tsv
Write-Host "Function App URL: https://$functionAppUrl" -ForegroundColor Green

Write-Host ""
Write-Host "📋 Your Azure Resources for Event Grid:" -ForegroundColor Cyan
Write-Host "   ✅ Event Grid Topic: $EventGridTopicName (Basic)" -ForegroundColor White
Write-Host "   ✅ Event Grid Namespace: $EventGridNamespace (Standard)" -ForegroundColor White
Write-Host "   ✅ Function App: $FunctionAppName" -ForegroundColor White
Write-Host "   ✅ Storage Account: $StorageAccountName" -ForegroundColor White
Write-Host "   ✅ Cosmos DB: $CosmosAccountName" -ForegroundColor White
Write-Host "   ✅ Service Bus: $ServiceBusNamespace" -ForegroundColor White
Write-Host "   ✅ SignalR: $SignalRName" -ForegroundColor White
Write-Host "   ✅ Notification Hubs: $NotificationHubNamespace" -ForegroundColor White
Write-Host ""

# 1. Create Storage System Topic
Write-Host "📦 Creating Storage System Topic..." -ForegroundColor Blue
$storageSystemTopic = "$StorageAccountName-events"
$storageAccountId = "/subscriptions/$subscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.Storage/storageAccounts/$StorageAccountName"

az eventgrid system-topic create `
    --name $storageSystemTopic `
    --resource-group $ResourceGroup `
    --source $storageAccountId `
    --topic-type "Microsoft.Storage.StorageAccounts" `
    --location "eastus"

Write-Host "✅ Storage System Topic: $storageSystemTopic" -ForegroundColor Green

# 2. Create Cosmos DB System Topic
Write-Host "📦 Creating Cosmos DB System Topic..." -ForegroundColor Blue
$cosmosSystemTopic = "$CosmosAccountName-events"
$cosmosAccountId = "/subscriptions/$subscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.DocumentDb/databaseAccounts/$CosmosAccountName"

az eventgrid system-topic create `
    --name $cosmosSystemTopic `
    --resource-group $ResourceGroup `
    --source $cosmosAccountId `
    --topic-type "Microsoft.DocumentDb.DatabaseAccounts" `
    --location "eastus"

Write-Host "✅ Cosmos DB System Topic: $cosmosSystemTopic" -ForegroundColor Green

# 3. Create Event Subscriptions for Storage Events
Write-Host "📬 Creating Storage Event Subscriptions..." -ForegroundColor Blue

# Blob events to Function App
az eventgrid event-subscription create `
    --name "storage-to-function" `
    --source-resource-id "/subscriptions/$subscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/systemTopics/$storageSystemTopic" `
    --endpoint "https://$functionAppUrl/api/eventgrid/webhook" `
    --endpoint-type webhook `
    --included-event-types "Microsoft.Storage.BlobCreated" "Microsoft.Storage.BlobDeleted" `
    --subject-begins-with "/blobServices/default/containers/documents/" `
    --max-delivery-attempts 3 `
    --event-ttl 1440

# High-volume blob events to Service Bus for batch processing
az eventgrid event-subscription create `
    --name "storage-to-servicebus" `
    --source-resource-id "/subscriptions/$subscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/systemTopics/$storageSystemTopic" `
    --endpoint "/subscriptions/$subscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.ServiceBus/namespaces/$ServiceBusNamespace/topics/blob-events" `
    --endpoint-type servicebustopic `
    --included-event-types "Microsoft.Storage.BlobCreated" `
    --subject-begins-with "/blobServices/default/containers/processed/" `
    --max-delivery-attempts 3

Write-Host "✅ Storage Event Subscriptions created" -ForegroundColor Green

# 4. Create Event Subscriptions for Cosmos DB Events
Write-Host "📬 Creating Cosmos DB Event Subscriptions..." -ForegroundColor Blue

# Cosmos DB events to Function App for real-time processing
az eventgrid event-subscription create `
    --name "cosmos-to-function" `
    --source-resource-id "/subscriptions/$subscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/systemTopics/$cosmosSystemTopic" `
    --endpoint "https://$functionAppUrl/api/eventgrid/webhook" `
    --endpoint-type webhook `
    --included-event-types "Microsoft.DocumentDb.DatabaseAccountCreated" "Microsoft.DocumentDb.DatabaseAccountUpdated" `
    --max-delivery-attempts 3 `
    --event-ttl 1440

Write-Host "✅ Cosmos DB Event Subscriptions created" -ForegroundColor Green

# 5. Create Custom Application Event Subscriptions
Write-Host "🎯 Creating Custom Application Event Subscriptions..." -ForegroundColor Blue

# High-priority events to Function App
az eventgrid event-subscription create `
    --name "custom-high-priority" `
    --source-resource-id "/subscriptions/$subscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/topics/$EventGridTopicName" `
    --endpoint "https://$functionAppUrl/api/eventgrid/webhook" `
    --endpoint-type webhook `
    --included-event-types "Document.Uploaded" "Document.Processed" "Workflow.Completed" "User.Registered" `
    --max-delivery-attempts 3 `
    --event-ttl 1440

# Analytics events to Service Bus for batch processing
az eventgrid event-subscription create `
    --name "custom-analytics" `
    --source-resource-id "/subscriptions/$subscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/topics/$EventGridTopicName" `
    --endpoint "/subscriptions/$subscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.ServiceBus/namespaces/$ServiceBusNamespace/topics/analytics-events" `
    --endpoint-type servicebustopic `
    --included-event-types "Analytics.Generated" "Performance.Alert" "System.HealthCheck" `
    --max-delivery-attempts 5

# Notification events to Service Bus for reliable delivery
az eventgrid event-subscription create `
    --name "custom-notifications" `
    --source-resource-id "/subscriptions/$subscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/topics/$EventGridTopicName" `
    --endpoint "/subscriptions/$subscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.ServiceBus/namespaces/$ServiceBusNamespace/queues/notification-queue" `
    --endpoint-type servicebusqueue `
    --included-event-types "Notification.Sent" "Document.Shared" `
    --max-delivery-attempts 5

Write-Host "✅ Custom Application Event Subscriptions created" -ForegroundColor Green

# 6. Create Event Grid Namespace Topic for Advanced Scenarios
Write-Host "🌐 Setting up Event Grid Namespace Topic..." -ForegroundColor Blue

# Create namespace topic for MQTT/CloudEvents
az eventgrid namespace topic create `
    --namespace-name $EventGridNamespace `
    --resource-group $ResourceGroup `
    --name "advanced-events" `
    --publisher-type custom `
    --input-schema cloudevents-1.0

Write-Host "✅ Event Grid Namespace Topic created for advanced scenarios" -ForegroundColor Green

# 7. Create Dead Letter Storage
Write-Host "💀 Setting up Dead Letter Storage..." -ForegroundColor Blue

az storage container create `
    --name "event-dead-letters" `
    --account-name $StorageAccountName `
    --auth-mode login

Write-Host "✅ Dead Letter Storage configured" -ForegroundColor Green

# 8. Summary
Write-Host ""
Write-Host "🎉 Optimal Event Grid Configuration Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "📊 Event Flow Summary:" -ForegroundColor Cyan
Write-Host "   📁 Storage Events → Function App (real-time) + Service Bus (batch)" -ForegroundColor White
Write-Host "   🗄️  Cosmos DB Events → Function App (real-time processing)" -ForegroundColor White
Write-Host "   📋 High-Priority Custom Events → Function App (immediate processing)" -ForegroundColor White
Write-Host "   📈 Analytics Events → Service Bus (batch processing)" -ForegroundColor White
Write-Host "   🔔 Notification Events → Service Bus (reliable delivery)" -ForegroundColor White
Write-Host "   🌐 Advanced Events → Event Grid Namespace (MQTT/CloudEvents)" -ForegroundColor White
Write-Host ""
Write-Host "🎯 Benefits of This Configuration:" -ForegroundColor Yellow
Write-Host "   ✅ Cost-effective (uses existing Function App)" -ForegroundColor White
Write-Host "   ✅ Scalable (Service Bus for high-volume events)" -ForegroundColor White
Write-Host "   ✅ Reliable (multiple retry attempts + dead letter)" -ForegroundColor White
Write-Host "   ✅ Real-time (Function App for immediate processing)" -ForegroundColor White
Write-Host "   ✅ Future-ready (Event Grid Namespace for advanced scenarios)" -ForegroundColor White
Write-Host ""
Write-Host "🔄 Next Steps:" -ForegroundColor Yellow
Write-Host "   1. Deploy your Function App: func azure functionapp publish $FunctionAppName" -ForegroundColor White
Write-Host "   2. Test event flow: node scripts/test-event-grid.js" -ForegroundColor White
Write-Host "   3. Monitor in Azure Portal: Event Grid + Service Bus + Function App metrics" -ForegroundColor White
Write-Host "   4. Set up SignalR integration for real-time notifications" -ForegroundColor White

/**
 * Health Monitoring Function
 * Handles system health monitoring, metrics collection, and alerting
 * Migrated from old-arch/src/monitoring-service/health/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as <PERSON><PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';
import { eventService } from '../shared/services/event';

// Health monitoring types and enums
enum HealthStatus {
  HEALTHY = 'HEALTHY',
  DEGRADED = 'DEGRADED',
  UNHEALTHY = 'UNHEALTHY',
  UNKNOWN = 'UNKNOWN'
}

enum ComponentType {
  DATABASE = 'DATABASE',
  CACHE = 'CACHE',
  STORAGE = 'STORAGE',
  API = 'API',
  QUEUE = 'QUEUE',
  EXTERNAL_SERVICE = 'EXTERNAL_SERVICE',
  FUNCTION = 'FUNCTION'
}

enum MetricType {
  COUNTER = 'COUNTER',
  GAUGE = 'GAUGE',
  HISTOGRAM = 'HISTOGRAM',
  TIMER = 'TIMER'
}

enum AlertSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

// Validation schemas
const recordMetricSchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  type: Joi.string().valid(...Object.values(MetricType)).required(),
  value: Joi.number().required(),
  unit: Joi.string().max(20).optional(),
  tags: Joi.object().optional(),
  timestamp: Joi.string().isoDate().optional(),
  organizationId: Joi.string().uuid().optional(),
  componentType: Joi.string().valid(...Object.values(ComponentType)).optional()
});

const createHealthCheckSchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  description: Joi.string().max(500).optional(),
  componentType: Joi.string().valid(...Object.values(ComponentType)).required(),
  endpoint: Joi.string().uri().optional(),
  checkInterval: Joi.number().min(10).max(3600).default(60), // seconds
  timeout: Joi.number().min(1).max(300).default(30), // seconds
  retryAttempts: Joi.number().min(1).max(5).default(3),
  thresholds: Joi.object({
    responseTime: Joi.number().min(1).optional(),
    errorRate: Joi.number().min(0).max(100).optional(),
    availability: Joi.number().min(0).max(100).optional()
  }).optional(),
  alerting: Joi.object({
    enabled: Joi.boolean().default(true),
    severity: Joi.string().valid(...Object.values(AlertSeverity)).default(AlertSeverity.MEDIUM),
    channels: Joi.array().items(Joi.string()).optional()
  }).optional(),
  organizationId: Joi.string().uuid().optional(),
  enabled: Joi.boolean().default(true)
});

interface RecordMetricRequest {
  name: string;
  type: MetricType;
  value: number;
  unit?: string;
  tags?: { [key: string]: string };
  timestamp?: string;
  organizationId?: string;
  componentType?: ComponentType;
}

interface HealthCheck {
  id: string;
  name: string;
  description?: string;
  componentType: ComponentType;
  endpoint?: string;
  checkInterval: number;
  timeout: number;
  retryAttempts: number;
  thresholds: {
    responseTime?: number;
    errorRate?: number;
    availability?: number;
  };
  alerting: {
    enabled: boolean;
    severity: AlertSeverity;
    channels?: string[];
  };
  status: HealthStatus;
  lastCheck?: string;
  lastSuccess?: string;
  lastFailure?: string;
  statistics: {
    totalChecks: number;
    successfulChecks: number;
    failedChecks: number;
    averageResponseTime: number;
    availability: number;
  };
  organizationId?: string;
  enabled: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  tenantId: string;
}

interface SystemHealth {
  overall: HealthStatus;
  components: Array<{
    name: string;
    type: ComponentType;
    status: HealthStatus;
    responseTime?: number;
    lastCheck: string;
    message?: string;
  }>;
  metrics: {
    uptime: number;
    responseTime: number;
    errorRate: number;
    throughput: number;
  };
  alerts: Array<{
    id: string;
    severity: AlertSeverity;
    message: string;
    component: string;
    timestamp: string;
  }>;
  timestamp: string;
}

/**
 * Get system health handler
 */
export async function getSystemHealth(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const startTime = Date.now();
  logger.info("Get system health started", { correlationId });

  try {
    // Check if this is an authenticated request or public health check
    const url = new URL(request.url);
    const isPublic = url.searchParams.get('public') === 'true';

    if (!isPublic) {
      // Authenticate user for detailed health info
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { error: 'Unauthorized', message: authResult.error }
        }, request);
      }

      const user = authResult.user;

      // Check admin access for detailed health info
      const hasAccess = await checkHealthAccess(user);
      if (!hasAccess) {
        return addCorsHeaders({
          status: 403,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { error: "Access denied to health monitoring" }
        }, request);
      }
    }

    // Perform health checks
    const systemHealth = await performSystemHealthCheck(isPublic);

    const duration = Date.now() - startTime;

    logger.info("System health check completed", {
      correlationId,
      overallStatus: systemHealth.overall,
      componentCount: systemHealth.components.length,
      alertCount: systemHealth.alerts.length,
      duration,
      isPublic
    });

    // Set appropriate status code based on health
    const statusCode = systemHealth.overall === HealthStatus.HEALTHY ? 200 :
                      systemHealth.overall === HealthStatus.DEGRADED ? 200 : 503;

    return addCorsHeaders({
      status: statusCode,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: systemHealth
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get system health failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 503,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        overall: HealthStatus.UNHEALTHY,
        error: "Health check failed",
        timestamp: new Date().toISOString()
      }
    }, request);
  }
}

/**
 * Record metric handler
 */
export async function recordMetric(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Record metric started", { correlationId });

  try {
    // Validate request body
    const body = await request.json();
    const { error, value } = recordMetricSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const metricRequest: RecordMetricRequest = value;

    // Record metric
    const metricId = await recordMetricData(metricRequest);

    // Update aggregated metrics
    await updateAggregatedMetrics(metricRequest);

    // Check for alerts
    await checkMetricAlerts(metricRequest);

    logger.info("Metric recorded successfully", {
      correlationId,
      metricId,
      metricName: metricRequest.name,
      metricType: metricRequest.type,
      metricValue: metricRequest.value
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        metricId,
        name: metricRequest.name,
        type: metricRequest.type,
        value: metricRequest.value,
        timestamp: metricRequest.timestamp || new Date().toISOString(),
        message: "Metric recorded successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Record metric failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Create health check handler
 */
export async function createHealthCheck(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create health check started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Check admin access
    const hasAccess = await checkHealthAccess(user);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to health monitoring" }
      }, request);
    }

    // Validate request body
    const body = await request.json();
    const { error, value } = createHealthCheckSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const healthCheckRequest = value;

    // Create health check
    const healthCheckId = uuidv4();
    const now = new Date().toISOString();

    const healthCheck: HealthCheck = {
      id: healthCheckId,
      name: healthCheckRequest.name,
      description: healthCheckRequest.description,
      componentType: healthCheckRequest.componentType,
      endpoint: healthCheckRequest.endpoint,
      checkInterval: healthCheckRequest.checkInterval,
      timeout: healthCheckRequest.timeout,
      retryAttempts: healthCheckRequest.retryAttempts,
      thresholds: healthCheckRequest.thresholds || {},
      alerting: healthCheckRequest.alerting || {
        enabled: true,
        severity: AlertSeverity.MEDIUM
      },
      status: HealthStatus.UNKNOWN,
      statistics: {
        totalChecks: 0,
        successfulChecks: 0,
        failedChecks: 0,
        averageResponseTime: 0,
        availability: 0
      },
      organizationId: healthCheckRequest.organizationId,
      enabled: healthCheckRequest.enabled,
      createdBy: user.id,
      createdAt: now,
      updatedAt: now,
      tenantId: user.tenantId || user.id
    };

    await db.createItem('health-checks', healthCheck);

    // Schedule health check
    await scheduleHealthCheck(healthCheck);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "health_check_created",
      userId: user.id,
      organizationId: healthCheckRequest.organizationId,
      timestamp: now,
      details: {
        healthCheckId,
        healthCheckName: healthCheckRequest.name,
        componentType: healthCheckRequest.componentType,
        checkInterval: healthCheckRequest.checkInterval,
        enabled: healthCheckRequest.enabled
      },
      tenantId: user.tenantId
    });

    logger.info("Health check created successfully", {
      correlationId,
      healthCheckId,
      healthCheckName: healthCheckRequest.name,
      componentType: healthCheckRequest.componentType,
      createdBy: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: healthCheckId,
        name: healthCheckRequest.name,
        componentType: healthCheckRequest.componentType,
        checkInterval: healthCheckRequest.checkInterval,
        enabled: healthCheckRequest.enabled,
        status: HealthStatus.UNKNOWN,
        createdAt: now,
        message: "Health check created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create health check failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkHealthAccess(user: any): Promise<boolean> {
  try {
    // Check if user has admin or monitoring role
    return user.roles?.includes('admin') || user.roles?.includes('monitoring_admin');
  } catch (error) {
    logger.error('Failed to check health access', { error, userId: user.id });
    return false;
  }
}

async function performSystemHealthCheck(isPublic: boolean): Promise<SystemHealth> {
  try {
    const components = [];
    const alerts = [];
    let overallStatus = HealthStatus.HEALTHY;

    // Check core components
    const coreComponents = [
      { name: 'Database', type: ComponentType.DATABASE, check: checkDatabaseHealth },
      { name: 'Cache', type: ComponentType.CACHE, check: checkCacheHealth },
      { name: 'Storage', type: ComponentType.STORAGE, check: checkStorageHealth }
    ];

    for (const component of coreComponents) {
      try {
        const startTime = Date.now();
        const health = await component.check();
        const responseTime = Date.now() - startTime;

        components.push({
          name: component.name,
          type: component.type,
          status: health.status,
          responseTime,
          lastCheck: new Date().toISOString(),
          message: health.message
        });

        // Update overall status
        if (health.status === HealthStatus.UNHEALTHY) {
          overallStatus = HealthStatus.UNHEALTHY;
        } else if (health.status === HealthStatus.DEGRADED && overallStatus === HealthStatus.HEALTHY) {
          overallStatus = HealthStatus.DEGRADED;
        }

        // Add alerts for unhealthy components
        if (health.status !== HealthStatus.HEALTHY) {
          alerts.push({
            id: uuidv4(),
            severity: health.status === HealthStatus.UNHEALTHY ? AlertSeverity.CRITICAL : AlertSeverity.MEDIUM,
            message: `${component.name} is ${health.status.toLowerCase()}: ${health.message}`,
            component: component.name,
            timestamp: new Date().toISOString()
          });
        }

      } catch (error) {
        components.push({
          name: component.name,
          type: component.type,
          status: HealthStatus.UNHEALTHY,
          lastCheck: new Date().toISOString(),
          message: error instanceof Error ? error.message : 'Health check failed'
        });
        overallStatus = HealthStatus.UNHEALTHY;
      }
    }

    // Get system metrics
    const metrics = await getSystemMetrics();

    // Get recent alerts if not public
    if (!isPublic) {
      const recentAlerts = await getRecentAlerts();
      alerts.push(...recentAlerts);
    }

    return {
      overall: overallStatus,
      components,
      metrics,
      alerts: isPublic ? [] : alerts,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    logger.error('Failed to perform system health check', { error });
    return {
      overall: HealthStatus.UNHEALTHY,
      components: [],
      metrics: {
        uptime: 0,
        responseTime: 0,
        errorRate: 100,
        throughput: 0
      },
      alerts: [],
      timestamp: new Date().toISOString()
    };
  }
}

async function checkDatabaseHealth(): Promise<{ status: HealthStatus; message: string }> {
  try {
    // Simple database connectivity check
    const testQuery = 'SELECT 1';
    await db.queryItems('health-checks', testQuery, []);

    return {
      status: HealthStatus.HEALTHY,
      message: 'Database connection successful'
    };
  } catch (error) {
    return {
      status: HealthStatus.UNHEALTHY,
      message: error instanceof Error ? error.message : 'Database connection failed'
    };
  }
}

async function checkCacheHealth(): Promise<{ status: HealthStatus; message: string }> {
  try {
    // Simple cache connectivity check
    const testKey = `health_check_${Date.now()}`;
    await redis.set(testKey, 'test');
    const value = await redis.get(testKey);
    await redis.del(testKey);

    if (value === 'test') {
      return {
        status: HealthStatus.HEALTHY,
        message: 'Cache connection successful'
      };
    } else {
      return {
        status: HealthStatus.DEGRADED,
        message: 'Cache read/write test failed'
      };
    }
  } catch (error) {
    return {
      status: HealthStatus.UNHEALTHY,
      message: error instanceof Error ? error.message : 'Cache connection failed'
    };
  }
}

async function checkStorageHealth(): Promise<{ status: HealthStatus; message: string }> {
  try {
    // Simplified storage health check
    // In production, this would check blob storage connectivity
    return {
      status: HealthStatus.HEALTHY,
      message: 'Storage service operational'
    };
  } catch (error) {
    return {
      status: HealthStatus.UNHEALTHY,
      message: error instanceof Error ? error.message : 'Storage service unavailable'
    };
  }
}

async function getSystemMetrics(): Promise<any> {
  try {
    // Get cached metrics
    const uptime = await redis.get('metrics:uptime') || '0';
    const responseTime = await redis.get('metrics:avg_response_time') || '0';
    const errorRate = await redis.get('metrics:error_rate') || '0';
    const throughput = await redis.get('metrics:throughput') || '0';

    return {
      uptime: parseFloat(uptime),
      responseTime: parseFloat(responseTime),
      errorRate: parseFloat(errorRate),
      throughput: parseFloat(throughput)
    };
  } catch (error) {
    logger.error('Failed to get system metrics', { error });
    return {
      uptime: 0,
      responseTime: 0,
      errorRate: 0,
      throughput: 0
    };
  }
}

async function getRecentAlerts(): Promise<any[]> {
  try {
    // Get recent alerts from the last hour
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();
    const alertsQuery = 'SELECT * FROM c WHERE c.timestamp >= @timestamp ORDER BY c.timestamp DESC';
    const alerts = await db.queryItems('alerts', alertsQuery, [oneHourAgo]);

    return alerts.slice(0, 10); // Return last 10 alerts
  } catch (error) {
    logger.error('Failed to get recent alerts', { error });
    return [];
  }
}

async function recordMetricData(metric: RecordMetricRequest): Promise<string> {
  try {
    const metricId = uuidv4();
    const timestamp = metric.timestamp || new Date().toISOString();

    const metricRecord = {
      id: metricId,
      name: metric.name,
      type: metric.type,
      value: metric.value,
      unit: metric.unit,
      tags: metric.tags || {},
      timestamp,
      organizationId: metric.organizationId,
      componentType: metric.componentType,
      tenantId: metric.organizationId || 'system'
    };

    await db.createItem('metrics', metricRecord);

    // Also store in time-series format for quick access
    const timeSeriesKey = `metrics:${metric.name}:${timestamp.split('T')[0]}`;
    await redis.lpush(timeSeriesKey, JSON.stringify({
      value: metric.value,
      timestamp,
      tags: metric.tags
    }));
    await redis.expire(timeSeriesKey, 86400 * 30); // 30 days

    return metricId;

  } catch (error) {
    logger.error('Failed to record metric data', { error, metric });
    throw error;
  }
}

async function updateAggregatedMetrics(metric: RecordMetricRequest): Promise<void> {
  try {
    const aggregateKey = `metrics:aggregate:${metric.name}`;

    // Update running averages and counts
    await redis.hincrby(aggregateKey, 'count', 1);
    await redis.hincrbyfloat(aggregateKey, 'sum', metric.value);
    await redis.hset(aggregateKey, 'last_value', metric.value.toString());
    await redis.hset(aggregateKey, 'last_updated', new Date().toISOString());
    await redis.expire(aggregateKey, 86400 * 7); // 7 days

  } catch (error) {
    logger.error('Failed to update aggregated metrics', { error, metric });
  }
}

async function checkMetricAlerts(metric: RecordMetricRequest): Promise<void> {
  try {
    // Simplified alert checking - in production, this would be more sophisticated
    if (metric.name === 'error_rate' && metric.value > 5) {
      await createAlert({
        severity: AlertSeverity.HIGH,
        message: `High error rate detected: ${metric.value}%`,
        component: metric.componentType || 'UNKNOWN',
        metricName: metric.name,
        metricValue: metric.value
      });
    }

    if (metric.name === 'response_time' && metric.value > 5000) {
      await createAlert({
        severity: AlertSeverity.MEDIUM,
        message: `High response time detected: ${metric.value}ms`,
        component: metric.componentType || 'UNKNOWN',
        metricName: metric.name,
        metricValue: metric.value
      });
    }

  } catch (error) {
    logger.error('Failed to check metric alerts', { error, metric });
  }
}

async function createAlert(alertData: any): Promise<void> {
  try {
    const alert = {
      id: uuidv4(),
      severity: alertData.severity,
      message: alertData.message,
      component: alertData.component,
      metricName: alertData.metricName,
      metricValue: alertData.metricValue,
      timestamp: new Date().toISOString(),
      acknowledged: false,
      tenantId: 'system'
    };

    await db.createItem('alerts', alert);

    // Publish alert event
    await eventService.publishEvent({
      type: 'AlertCreated',
      aggregateId: alert.id,
      aggregateType: 'Alert',
      version: 1,
      data: alert,
      tenantId: 'system'
    });

  } catch (error) {
    logger.error('Failed to create alert', { error, alertData });
  }
}

async function scheduleHealthCheck(healthCheck: HealthCheck): Promise<void> {
  try {
    // In production, this would schedule the health check to run at intervals
    logger.info('Health check scheduled', {
      healthCheckId: healthCheck.id,
      interval: healthCheck.checkInterval
    });
  } catch (error) {
    logger.error('Failed to schedule health check', { error, healthCheckId: healthCheck.id });
  }
}

// Register functions
app.http('health-system', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'anonymous',
  route: 'system/health-status',
  handler: getSystemHealth
});

app.http('metric-record', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'metrics',
  handler: recordMetric
});

app.http('health-check-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'management/health-checks/create',
  handler: createHealthCheck
});

/**
 * Document Versions Function
 * Handles document version history and management
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Validation schemas
const getVersionsSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20)
});

interface DocumentVersion {
  id: string;
  documentId: string;
  versionNumber: number;
  createdBy: string;
  createdAt: string;
  comment?: string;
  size: number;
  hash: string;
  blobName: string;
  contentType: string;
  isActive: boolean;
  changes?: {
    type: string;
    description: string;
    timestamp: string;
  }[];
}

/**
 * Get document versions handler
 */
export async function getDocumentVersions(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const documentId = request.params.id;

  if (!documentId) {
    return addCorsHeaders({
      status: 400,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: 'Document ID is required' }
    }, request);
  }

  logger.info("Get document versions started", { correlationId, documentId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate query parameters
    const queryParams = Object.fromEntries(request.query.entries());
    const { error, value } = getVersionsSchema.validate(queryParams);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { page, limit } = value;

    // First, verify the document exists and user has access
    const document = await db.readItem('documents', documentId, documentId);

    if (!document) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document not found" }
      }, request);
    }

    // Check access permissions
    const hasAccess = (
      (document as any).createdBy === user.id ||
      (document as any).organizationId === user.tenantId ||
      user.roles?.includes('admin')
    );

    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied" }
      }, request);
    }

    // Query for document versions
    const query = 'SELECT * FROM c WHERE c.documentId = @documentId ORDER BY c.versionNumber DESC';
    const parameters = [documentId];

    // Execute query with pagination
    const offset = (page - 1) * limit;
    const paginatedQuery = `${query} OFFSET ${offset} LIMIT ${limit}`;

    const versions = await db.queryItems('documentVersions', paginatedQuery, parameters);

    // Get total count for pagination
    const countQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.documentId = @documentId';
    const countResult = await db.queryItems('documentVersions', countQuery, parameters);
    const total = Number(countResult[0]) || 0;

    // Format versions for response
    const formattedVersions = versions.map((version: any) => ({
      versionNumber: version.versionNumber,
      createdBy: version.createdBy,
      createdAt: version.createdAt,
      comment: version.comment,
      size: version.size,
      hash: version.hash,
      isActive: version.isActive,
      changes: version.changes || []
    }));

    logger.info("Document versions retrieved successfully", {
      correlationId,
      documentId,
      userId: user.id,
      versionsCount: versions.length,
      page,
      limit
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        documentId,
        versions: formattedVersions,
        pagination: {
          page,
          limit,
          total,
          hasMore: (page * limit) < total
        }
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get document versions failed", {
      correlationId,
      documentId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Create document version handler
 */
export async function createDocumentVersion(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const documentId = request.params.id;

  if (!documentId) {
    return addCorsHeaders({
      status: 400,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: 'Document ID is required' }
    }, request);
  }

  logger.info("Create document version started", { correlationId, documentId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;
    const body = await request.json();

    // Verify the document exists and user has access
    const document = await db.readItem('documents', documentId, documentId);

    if (!document) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document not found" }
      }, request);
    }

    // Check access permissions
    const hasAccess = (
      (document as any).createdBy === user.id ||
      (document as any).organizationId === user.tenantId ||
      user.roles?.includes('admin')
    );

    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied" }
      }, request);
    }

    // Get the current highest version number
    const latestVersionQuery = 'SELECT TOP 1 * FROM c WHERE c.documentId = @documentId ORDER BY c.versionNumber DESC';
    const latestVersions = await db.queryItems('documentVersions', latestVersionQuery, [documentId]);
    const latestVersion = latestVersions[0] as any;
    const newVersionNumber = latestVersion ? latestVersion.versionNumber + 1 : 1;

    // Parse and validate body
    const bodyData = body as any;

    // Create new version record
    const newVersion: DocumentVersion = {
      id: `${documentId}_v${newVersionNumber}`,
      documentId,
      versionNumber: newVersionNumber,
      createdBy: user.id,
      createdAt: new Date().toISOString(),
      comment: bodyData.comment || `Version ${newVersionNumber}`,
      size: (document as any).size || 0,
      hash: bodyData.hash || '',
      blobName: (document as any).blobName || '',
      contentType: (document as any).contentType || '',
      isActive: true,
      changes: bodyData.changes || []
    };

    // Deactivate previous version if it exists
    if (latestVersion) {
      const updatedPreviousVersion = {
        ...latestVersion,
        id: latestVersion.id,
        isActive: false
      };
      await db.updateItem('documentVersions', updatedPreviousVersion);
    }

    // Save new version
    await db.createItem('documentVersions', newVersion);

    // Update document with new version info
    const updatedDocument = {
      ...document,
      id: (document as any).id,
      version: newVersionNumber,
      updatedAt: new Date().toISOString(),
      updatedBy: user.id
    };
    await db.updateItem('documents', updatedDocument);

    logger.info("Document version created successfully", {
      correlationId,
      documentId,
      versionNumber: newVersionNumber,
      userId: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        documentId,
        versionNumber: newVersionNumber,
        message: "Document version created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create document version failed", {
      correlationId,
      documentId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Combined document versions handler
 */
async function handleDocumentVersions(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const method = request.method.toUpperCase();

  switch (method) {
    case 'GET':
      return await getDocumentVersions(request, context);
    case 'POST':
      return await createDocumentVersion(request, context);
    case 'OPTIONS':
      return handlePreflight(request) || { status: 200 };
    default:
      return addCorsHeaders({
        status: 405,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Method not allowed' }
      }, request);
  }
}

// Register functions
app.http('document-versions', {
  methods: ['GET', 'POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'documents/{id}/versions',
  handler: handleDocumentVersions
});

/**
 * Mobile API Function
 * Handles mobile-optimized endpoints and Progressive Web App features
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Mobile device types enum
enum DeviceType {
  IOS = 'IOS',
  ANDROID = 'ANDROID',
  WEB = 'WEB',
  TABLET = 'TABLET',
  DESKTOP = 'DESKTOP'
}

// Sync status enum
enum SyncStatus {
  PENDING = 'PENDING',
  SYNCING = 'SYNCING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CONFLICT = 'CONFLICT'
}

// Validation schemas
const registerDeviceSchema = Joi.object({
  deviceId: Joi.string().required().max(100),
  deviceType: Joi.string().valid(...Object.values(DeviceType)).required(),
  deviceName: Joi.string().required().max(100),
  osVersion: Joi.string().max(50).optional(),
  appVersion: Joi.string().max(50).optional(),
  pushToken: Joi.string().max(500).optional(),
  capabilities: Joi.object({
    offline: Joi.boolean().default(false),
    camera: Joi.boolean().default(false),
    location: Joi.boolean().default(false),
    biometric: Joi.boolean().default(false),
    notifications: Joi.boolean().default(false)
  }).optional(),
  organizationId: Joi.string().uuid().required()
});

const syncRequestSchema = Joi.object({
  deviceId: Joi.string().required(),
  lastSyncTimestamp: Joi.date().iso().optional(),
  changes: Joi.array().items(
    Joi.object({
      id: Joi.string().uuid().required(),
      type: Joi.string().valid('CREATE', 'UPDATE', 'DELETE').required(),
      entity: Joi.string().required(),
      data: Joi.object().optional(),
      timestamp: Joi.date().iso().required(),
      clientId: Joi.string().uuid().required()
    })
  ).optional(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional()
});

const offlineDataSchema = Joi.object({
  deviceId: Joi.string().required(),
  organizationId: Joi.string().uuid().required(),
  projectIds: Joi.array().items(Joi.string().uuid()).optional(),
  dataTypes: Joi.array().items(Joi.string().valid('documents', 'workflows', 'projects', 'users')).default(['documents']),
  maxItems: Joi.number().integer().min(1).max(1000).default(100)
});

/**
 * Register mobile device handler
 */
export async function registerDevice(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Register device started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = registerDeviceSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const deviceData = value;

    // Check organization access
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, deviceData.organizationId, 'active']);

    if (memberships.length === 0) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Check if device already exists
    const existingDeviceQuery = 'SELECT * FROM c WHERE c.deviceId = @deviceId AND c.userId = @userId';
    const existingDevices = await db.queryItems('mobile-devices', existingDeviceQuery, [deviceData.deviceId, user.id]);

    let device;
    if (existingDevices.length > 0) {
      // Update existing device
      device = {
        ...(existingDevices[0] as any),
        id: (existingDevices[0] as any).id,
        deviceName: deviceData.deviceName,
        osVersion: deviceData.osVersion,
        appVersion: deviceData.appVersion,
        pushToken: deviceData.pushToken,
        capabilities: deviceData.capabilities || {},
        lastActiveAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      await db.updateItem('mobile-devices', device);
    } else {
      // Create new device
      const deviceId = uuidv4();
      device = {
        id: deviceId,
        deviceId: deviceData.deviceId,
        deviceType: deviceData.deviceType,
        deviceName: deviceData.deviceName,
        osVersion: deviceData.osVersion,
        appVersion: deviceData.appVersion,
        pushToken: deviceData.pushToken,
        capabilities: deviceData.capabilities || {},
        userId: user.id,
        organizationId: deviceData.organizationId,
        isActive: true,
        registeredAt: new Date().toISOString(),
        lastActiveAt: new Date().toISOString(),
        lastSyncAt: null,
        syncStatus: SyncStatus.PENDING,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        tenantId: user.tenantId
      };
      await db.createItem('mobile-devices', device);
    }

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "mobile_device_registered",
      userId: user.id,
      organizationId: deviceData.organizationId,
      timestamp: new Date().toISOString(),
      details: {
        deviceId: deviceData.deviceId,
        deviceType: deviceData.deviceType,
        deviceName: deviceData.deviceName,
        isUpdate: existingDevices.length > 0
      },
      tenantId: user.tenantId
    });

    logger.info("Device registered successfully", {
      correlationId,
      deviceId: deviceData.deviceId,
      userId: user.id,
      deviceType: deviceData.deviceType,
      isUpdate: existingDevices.length > 0
    });

    return addCorsHeaders({
      status: existingDevices.length > 0 ? 200 : 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        deviceId: device.deviceId,
        deviceType: device.deviceType,
        capabilities: device.capabilities,
        syncStatus: device.syncStatus,
        lastSyncAt: device.lastSyncAt,
        message: existingDevices.length > 0 ? "Device updated successfully" : "Device registered successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Register device failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Sync data handler
 */
export async function syncData(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Sync data started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = syncRequestSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { deviceId, lastSyncTimestamp, changes, organizationId, projectId } = value;

    // Get device
    const deviceQuery = 'SELECT * FROM c WHERE c.deviceId = @deviceId AND c.userId = @userId';
    const devices = await db.queryItems('mobile-devices', deviceQuery, [deviceId, user.id]);

    if (devices.length === 0) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Device not found" }
      }, request);
    }

    const device = devices[0] as any;

    // Update device sync status
    const updatedDevice = {
      ...device,
      id: device.id,
      syncStatus: SyncStatus.SYNCING,
      lastActiveAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    await db.updateItem('mobile-devices', updatedDevice);

    // Process incoming changes from device
    const conflicts: any[] = [];
    const appliedChanges: any[] = [];

    if (changes && changes.length > 0) {
      for (const change of changes) {
        try {
          const result = await processDeviceChange(change, user, organizationId, projectId);
          if (result.conflict) {
            conflicts.push({
              clientId: change.clientId,
              conflict: result.conflict,
              serverData: result.serverData
            });
          } else {
            appliedChanges.push({
              clientId: change.clientId,
              serverId: result.serverId
            });
          }
        } catch (error) {
          conflicts.push({
            clientId: change.clientId,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }
    }

    // Get server changes since last sync
    const serverChanges = await getServerChanges(
      user,
      organizationId,
      projectId,
      lastSyncTimestamp || device.lastSyncAt
    );

    // Update device sync completion
    const finalDevice = {
      ...updatedDevice,
      id: device.id,
      syncStatus: conflicts.length > 0 ? SyncStatus.CONFLICT : SyncStatus.COMPLETED,
      lastSyncAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    await db.updateItem('mobile-devices', finalDevice);

    // Create sync record
    await db.createItem('sync-records', {
      id: uuidv4(),
      deviceId,
      userId: user.id,
      organizationId,
      projectId,
      syncTimestamp: new Date().toISOString(),
      incomingChanges: changes?.length || 0,
      outgoingChanges: serverChanges.length,
      conflicts: conflicts.length,
      appliedChanges: appliedChanges.length,
      status: conflicts.length > 0 ? SyncStatus.CONFLICT : SyncStatus.COMPLETED,
      tenantId: user.tenantId
    });

    logger.info("Data sync completed", {
      correlationId,
      deviceId,
      userId: user.id,
      incomingChanges: changes?.length || 0,
      outgoingChanges: serverChanges.length,
      conflicts: conflicts.length,
      appliedChanges: appliedChanges.length
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        syncTimestamp: finalDevice.lastSyncAt,
        serverChanges,
        appliedChanges,
        conflicts,
        hasConflicts: conflicts.length > 0,
        message: conflicts.length > 0 ? "Sync completed with conflicts" : "Sync completed successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Sync data failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Get offline data handler
 */
export async function getOfflineData(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Get offline data started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate query parameters
    const queryParams = Object.fromEntries(request.query.entries());
    const { error, value } = offlineDataSchema.validate(queryParams);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { deviceId, organizationId, projectIds, dataTypes, maxItems } = value;

    // Verify device ownership
    const deviceQuery = 'SELECT * FROM c WHERE c.deviceId = @deviceId AND c.userId = @userId';
    const devices = await db.queryItems('mobile-devices', deviceQuery, [deviceId, user.id]);

    if (devices.length === 0) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Device not found" }
      }, request);
    }

    // Get offline data for each requested type
    const offlineData: any = {};

    for (const dataType of dataTypes) {
      switch (dataType) {
        case 'documents':
          offlineData.documents = await getOfflineDocuments(user, organizationId, projectIds, maxItems);
          break;
        case 'workflows':
          offlineData.workflows = await getOfflineWorkflows(user, organizationId, projectIds, maxItems);
          break;
        case 'projects':
          offlineData.projects = await getOfflineProjects(user, organizationId, maxItems);
          break;
        case 'users':
          offlineData.users = await getOfflineUsers(user, organizationId, maxItems);
          break;
      }
    }

    // Update device last active timestamp
    const device = devices[0] as any;
    const updatedDevice = {
      ...device,
      id: device.id,
      lastActiveAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    await db.updateItem('mobile-devices', updatedDevice);

    logger.info("Offline data retrieved successfully", {
      correlationId,
      deviceId,
      userId: user.id,
      organizationId,
      dataTypes,
      totalItems: Object.values(offlineData).reduce((sum: number, items: any) => sum + (items?.length || 0), 0)
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        deviceId,
        organizationId,
        dataTypes,
        data: offlineData,
        generatedAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get offline data failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Process device change
 */
async function processDeviceChange(change: any, user: any, organizationId: string, projectId?: string): Promise<any> {
  // Simplified conflict detection and resolution
  // In production, implement proper conflict resolution strategies

  try {
    switch (change.entity) {
      case 'document':
        return await processDocumentChange(change, user, organizationId, projectId);
      case 'workflow':
        return await processWorkflowChange(change, user, organizationId, projectId);
      default:
        throw new Error(`Unsupported entity type: ${change.entity}`);
    }
  } catch (error) {
    return {
      conflict: true,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Get server changes since timestamp
 */
async function getServerChanges(user: any, organizationId: string, projectId?: string, since?: string): Promise<any[]> {
  const changes: any[] = [];
  const sinceDate = since || new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();

  // Get document changes
  const documentQuery = `
    SELECT * FROM c
    WHERE c.organizationId = @orgId
    AND c.updatedAt > @since
    ORDER BY c.updatedAt DESC
    OFFSET 0 LIMIT 100
  `;
  const documents = await db.queryItems('documents', documentQuery, [organizationId, sinceDate]);

  documents.forEach((doc: any) => {
    changes.push({
      id: doc.id,
      type: 'UPDATE',
      entity: 'document',
      data: doc,
      timestamp: doc.updatedAt
    });
  });

  return changes;
}

// Helper functions for offline data retrieval
async function getOfflineDocuments(user: any, organizationId: string, projectIds?: string[], maxItems: number = 100): Promise<any[]> {
  let query = 'SELECT * FROM c WHERE c.organizationId = @organizationId ORDER BY c.updatedAt DESC OFFSET 0 LIMIT @limit';
  const params: any[] = [organizationId, maxItems];

  if (projectIds && projectIds.length > 0) {
    // Use ARRAY_CONTAINS with the array directly - now supported with updated parameter names
    query = 'SELECT * FROM c WHERE c.organizationId = @organizationId AND ARRAY_CONTAINS(@projectIds, c.projectId) ORDER BY c.updatedAt DESC OFFSET 0 LIMIT @limit';
    params.splice(1, 0, projectIds);
  }

  return await db.queryItems('documents', query, params);
}

async function getOfflineWorkflows(user: any, organizationId: string, projectIds?: string[], maxItems: number = 100): Promise<any[]> {
  let query = 'SELECT * FROM c WHERE c.organizationId = @orgId ORDER BY c.updatedAt DESC OFFSET 0 LIMIT @limit';
  const params = [organizationId, maxItems];

  return await db.queryItems('workflows', query, params);
}

async function getOfflineProjects(user: any, organizationId: string, maxItems: number = 100): Promise<any[]> {
  const query = 'SELECT * FROM c WHERE c.organizationId = @orgId ORDER BY c.updatedAt DESC OFFSET 0 LIMIT @limit';
  return await db.queryItems('projects', query, [organizationId, maxItems]);
}

async function getOfflineUsers(user: any, organizationId: string, maxItems: number = 100): Promise<any[]> {
  const query = 'SELECT c.id, c.name, c.email, c.avatar FROM c WHERE c.tenantId = @tenantId ORDER BY c.name ASC OFFSET 0 LIMIT @limit';
  return await db.queryItems('users', query, [user.tenantId, maxItems]);
}

async function processDocumentChange(change: any, user: any, organizationId: string, projectId?: string): Promise<any> {
  // Simplified implementation
  return {
    conflict: false,
    serverId: change.id
  };
}

async function processWorkflowChange(change: any, user: any, organizationId: string, projectId?: string): Promise<any> {
  // Simplified implementation
  return {
    conflict: false,
    serverId: change.id
  };
}

// Register functions
app.http('mobile-device-register', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'mobile/devices/register',
  handler: registerDevice
});

app.http('mobile-sync', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'mobile/sync',
  handler: syncData
});

app.http('mobile-offline-data', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'mobile/offline-data',
  handler: getOfflineData
});

/**
 * Search Indexing Function
 * Handles document indexing and search operations
 * Migrated from old-arch/src/search-service/document-indexer/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { BlobServiceClient } from "@azure/storage-blob";
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';
import { eventService } from '../shared/services/event';

// Search types and enums
enum IndexStatus {
  PENDING = 'PENDING',
  INDEXING = 'INDEXING',
  INDEXED = 'INDEXED',
  FAILED = 'FAILED',
  OUTDATED = 'OUTDATED'
}

enum SearchType {
  FULL_TEXT = 'FULL_TEXT',
  SEMANTIC = 'SEMANTIC',
  HYBRID = 'HYBRID',
  FACETED = 'FACETED'
}

// Validation schemas
const indexDocumentSchema = Joi.object({
  documentId: Joi.string().uuid().required(),
  forceReindex: Joi.boolean().default(false),
  extractContent: Joi.boolean().default(true),
  generateEmbeddings: Joi.boolean().default(true),
  updateMetadata: Joi.boolean().default(true)
});

const searchDocumentsSchema = Joi.object({
  query: Joi.string().min(1).max(500).required(),
  type: Joi.string().valid(...Object.values(SearchType)).default(SearchType.HYBRID),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional(),
  filters: Joi.object({
    documentTypes: Joi.array().items(Joi.string()).optional(),
    categories: Joi.array().items(Joi.string()).optional(),
    tags: Joi.array().items(Joi.string()).optional(),
    dateRange: Joi.object({
      startDate: Joi.string().isoDate().optional(),
      endDate: Joi.string().isoDate().optional()
    }).optional(),
    authors: Joi.array().items(Joi.string().uuid()).optional(),
    minScore: Joi.number().min(0).max(1).optional()
  }).optional(),
  options: Joi.object({
    page: Joi.number().min(1).default(1),
    limit: Joi.number().min(1).max(100).default(20),
    includeHighlights: Joi.boolean().default(true),
    includeFacets: Joi.boolean().default(false),
    includeContent: Joi.boolean().default(false),
    sortBy: Joi.string().valid('relevance', 'date', 'title', 'author').default('relevance'),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc')
  }).optional()
});

interface IndexDocumentRequest {
  documentId: string;
  forceReindex?: boolean;
  extractContent?: boolean;
  generateEmbeddings?: boolean;
  updateMetadata?: boolean;
}

interface SearchDocumentsRequest {
  query: string;
  type?: SearchType;
  organizationId: string;
  projectId?: string;
  filters?: {
    documentTypes?: string[];
    categories?: string[];
    tags?: string[];
    dateRange?: {
      startDate?: string;
      endDate?: string;
    };
    authors?: string[];
    minScore?: number;
  };
  options?: {
    page?: number;
    limit?: number;
    includeHighlights?: boolean;
    includeFacets?: boolean;
    includeContent?: boolean;
    sortBy?: string;
    sortOrder?: string;
  };
}

interface SearchIndex {
  id: string;
  documentId: string;
  title: string;
  content: string;
  contentVector?: number[];
  metadata: {
    fileName: string;
    contentType: string;
    size: number;
    author: string;
    createdAt: string;
    updatedAt: string;
    category?: string;
    tags: string[];
    organizationId: string;
    projectId?: string;
  };
  extractedEntities: Array<{
    name: string;
    type: string;
    confidence: number;
  }>;
  keywords: string[];
  language: string;
  status: IndexStatus;
  indexedAt: string;
  tenantId: string;
}

interface SearchResult {
  documentId: string;
  title: string;
  snippet: string;
  score: number;
  highlights?: Array<{
    field: string;
    fragments: string[];
  }>;
  metadata: any;
}

/**
 * Index document handler
 */
export async function indexDocument(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Index document started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = indexDocumentSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const indexRequest: IndexDocumentRequest = value;

    // Get document
    const document = await db.readItem('documents', indexRequest.documentId, indexRequest.documentId);
    if (!document) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document not found" }
      }, request);
    }

    const documentData = document as any;

    // Check document access
    const hasAccess = await checkDocumentAccess(documentData, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to document" }
      }, request);
    }

    // Check if document is already indexed and not forcing reindex
    const existingIndex = await getExistingIndex(indexRequest.documentId);
    if (existingIndex && !indexRequest.forceReindex) {
      return addCorsHeaders({
        status: 200,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          documentId: indexRequest.documentId,
          status: existingIndex.status,
          indexedAt: existingIndex.indexedAt,
          message: "Document already indexed"
        }
      }, request);
    }

    // Start indexing process
    const indexResult = await performDocumentIndexing(documentData, indexRequest);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "document_indexed",
      userId: user.id,
      organizationId: documentData.organizationId,
      projectId: documentData.projectId,
      documentId: indexRequest.documentId,
      timestamp: new Date().toISOString(),
      details: {
        documentName: documentData.name,
        indexStatus: indexResult.status,
        extractedContent: indexRequest.extractContent,
        generatedEmbeddings: indexRequest.generateEmbeddings,
        keywordCount: indexResult.keywords?.length || 0,
        entityCount: indexResult.extractedEntities?.length || 0
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'DocumentIndexed',
      aggregateId: indexRequest.documentId,
      aggregateType: 'Document',
      version: 1,
      data: {
        document: documentData,
        indexResult,
        indexedBy: user.id
      },
      userId: user.id,
      organizationId: documentData.organizationId,
      tenantId: user.tenantId
    });

    logger.info("Document indexed successfully", {
      correlationId,
      documentId: indexRequest.documentId,
      documentName: documentData.name,
      indexStatus: indexResult.status,
      indexedBy: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        documentId: indexRequest.documentId,
        status: indexResult.status,
        indexedAt: indexResult.indexedAt,
        keywordCount: indexResult.keywords?.length || 0,
        entityCount: indexResult.extractedEntities?.length || 0,
        message: "Document indexed successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Index document failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Search documents handler
 */
export async function searchDocuments(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Search documents started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Parse query parameters
    const url = new URL(request.url);
    const queryParams = {
      query: url.searchParams.get('query') || '',
      type: url.searchParams.get('type') || SearchType.HYBRID,
      organizationId: url.searchParams.get('organizationId') || '',
      projectId: url.searchParams.get('projectId') || undefined,
      page: parseInt(url.searchParams.get('page') || '1'),
      limit: parseInt(url.searchParams.get('limit') || '20'),
      includeHighlights: url.searchParams.get('includeHighlights') === 'true',
      includeFacets: url.searchParams.get('includeFacets') === 'true',
      includeContent: url.searchParams.get('includeContent') === 'true',
      sortBy: url.searchParams.get('sortBy') || 'relevance',
      sortOrder: url.searchParams.get('sortOrder') || 'desc'
    };

    // Validate search request
    const { error, value } = searchDocumentsSchema.validate({
      ...queryParams,
      options: {
        page: queryParams.page,
        limit: queryParams.limit,
        includeHighlights: queryParams.includeHighlights,
        includeFacets: queryParams.includeFacets,
        includeContent: queryParams.includeContent,
        sortBy: queryParams.sortBy,
        sortOrder: queryParams.sortOrder
      }
    });

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const searchRequest: SearchDocumentsRequest = value;

    // Check organization access
    const hasAccess = await checkOrganizationAccess(searchRequest.organizationId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Perform search
    const searchResults = await performDocumentSearch(searchRequest, user);

    // Store search in history
    await storeSearchHistory(searchRequest, user, searchResults.totalCount);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "documents_searched",
      userId: user.id,
      organizationId: searchRequest.organizationId,
      projectId: searchRequest.projectId,
      timestamp: new Date().toISOString(),
      details: {
        query: searchRequest.query,
        searchType: searchRequest.type,
        resultCount: searchResults.totalCount,
        page: searchRequest.options?.page || 1
      },
      tenantId: user.tenantId
    });

    logger.info("Document search completed", {
      correlationId,
      query: searchRequest.query,
      searchType: searchRequest.type,
      resultCount: searchResults.totalCount,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        query: searchRequest.query,
        results: searchResults.results,
        pagination: {
          page: searchRequest.options?.page || 1,
          limit: searchRequest.options?.limit || 20,
          totalCount: searchResults.totalCount,
          totalPages: Math.ceil(searchResults.totalCount / (searchRequest.options?.limit || 20))
        },
        facets: searchResults.facets,
        searchTime: searchResults.searchTime,
        suggestions: searchResults.suggestions
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Search documents failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkDocumentAccess(document: any, userId: string): Promise<boolean> {
  try {
    // Check if user is the owner
    if (document.createdBy === userId) {
      return true;
    }

    // Check organization membership
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [document.organizationId, userId, 'ACTIVE']);

    return memberships.length > 0;
  } catch (error) {
    logger.error('Failed to check document access', { error, documentId: document.id, userId });
    return false;
  }
}

async function checkOrganizationAccess(organizationId: string, userId: string): Promise<boolean> {
  try {
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
    return memberships.length > 0;
  } catch (error) {
    logger.error('Failed to check organization access', { error, organizationId, userId });
    return false;
  }
}

async function getExistingIndex(documentId: string): Promise<any> {
  try {
    return await db.readItem('search-index', documentId, documentId);
  } catch (error) {
    logger.error('Failed to get existing index', { error, documentId });
    return null;
  }
}

async function performDocumentIndexing(document: any, indexRequest: IndexDocumentRequest): Promise<SearchIndex> {
  try {
    const now = new Date().toISOString();

    // Extract content if requested
    let content = '';
    if (indexRequest.extractContent) {
      content = await extractDocumentContent(document);
    }

    // Generate embeddings if requested
    let contentVector: number[] | undefined;
    if (indexRequest.generateEmbeddings) {
      contentVector = await generateContentEmbeddings(content);
    }

    // Extract entities and keywords
    const extractedEntities = await extractEntities(content);
    const keywords = await extractKeywords(content);

    // Create search index
    const searchIndex: SearchIndex = {
      id: document.id,
      documentId: document.id,
      title: document.name,
      content,
      contentVector,
      metadata: {
        fileName: document.name,
        contentType: document.contentType || 'application/octet-stream',
        size: document.size || 0,
        author: document.createdBy,
        createdAt: document.createdAt,
        updatedAt: document.updatedAt,
        category: document.category,
        tags: document.tags || [],
        organizationId: document.organizationId,
        projectId: document.projectId
      },
      extractedEntities,
      keywords,
      language: 'en', // Simplified - would detect language in production
      status: IndexStatus.INDEXED,
      indexedAt: now,
      tenantId: document.tenantId
    };

    // Store in search index
    await db.upsertItem('search-index', searchIndex);

    // Cache frequently accessed data in Redis
    await redis.setex(`search:doc:${document.id}`, 3600, JSON.stringify({
      title: searchIndex.title,
      content: searchIndex.content.substring(0, 1000), // First 1000 chars
      keywords: searchIndex.keywords,
      metadata: searchIndex.metadata
    }));

    return searchIndex;

  } catch (error) {
    logger.error('Failed to perform document indexing', { error, documentId: document.id });

    // Create failed index record
    const failedIndex: SearchIndex = {
      id: document.id,
      documentId: document.id,
      title: document.name,
      content: '',
      metadata: {
        fileName: document.name,
        contentType: document.contentType || 'application/octet-stream',
        size: document.size || 0,
        author: document.createdBy,
        createdAt: document.createdAt,
        updatedAt: document.updatedAt,
        organizationId: document.organizationId,
        projectId: document.projectId,
        tags: []
      },
      extractedEntities: [],
      keywords: [],
      language: 'en',
      status: IndexStatus.FAILED,
      indexedAt: new Date().toISOString(),
      tenantId: document.tenantId
    };

    await db.upsertItem('search-index', failedIndex);
    return failedIndex;
  }
}

async function extractDocumentContent(document: any): Promise<string> {
  try {
    // In production, this would extract content from the actual document
    // For now, return mock content
    return `Mock extracted content from document: ${document.name}. This would contain the actual text content extracted from the document using appropriate text extraction libraries.`;
  } catch (error) {
    logger.error('Failed to extract document content', { error, documentId: document.id });
    return '';
  }
}

async function generateContentEmbeddings(content: string): Promise<number[]> {
  try {
    // In production, this would generate actual embeddings using AI models
    // For now, return mock embeddings
    const mockEmbedding = Array.from({ length: 384 }, () => Math.random() * 2 - 1);
    return mockEmbedding;
  } catch (error) {
    logger.error('Failed to generate content embeddings', { error });
    return [];
  }
}

async function extractEntities(content: string): Promise<Array<{ name: string; type: string; confidence: number }>> {
  try {
    // In production, this would use NLP models to extract entities
    // For now, return mock entities
    return [
      { name: 'Acme Corporation', type: 'organization', confidence: 0.95 },
      { name: 'John Smith', type: 'person', confidence: 0.88 },
      { name: 'New York', type: 'location', confidence: 0.92 }
    ];
  } catch (error) {
    logger.error('Failed to extract entities', { error });
    return [];
  }
}

async function extractKeywords(content: string): Promise<string[]> {
  try {
    // In production, this would use keyword extraction algorithms
    // For now, return mock keywords
    const words = content.toLowerCase().split(/\W+/).filter(word => word.length > 3);
    const uniqueWords = [...new Set(words)];
    return uniqueWords.slice(0, 20); // Top 20 keywords
  } catch (error) {
    logger.error('Failed to extract keywords', { error });
    return [];
  }
}

async function performDocumentSearch(searchRequest: SearchDocumentsRequest, user: any): Promise<any> {
  try {
    const startTime = Date.now();

    // Build search query
    let query = 'SELECT * FROM c WHERE c.metadata.organizationId = @orgId';
    const parameters = [searchRequest.organizationId];

    if (searchRequest.projectId) {
      query += ' AND c.metadata.projectId = @projectId';
      parameters.push(searchRequest.projectId);
    }

    // Add text search (simplified)
    if (searchRequest.query) {
      query += ' AND (CONTAINS(LOWER(c.title), @query) OR CONTAINS(LOWER(c.content), @query) OR ARRAY_CONTAINS(c.keywords, @query))';
      parameters.push(searchRequest.query.toLowerCase());
    }

    // Add filters
    if (searchRequest.filters?.documentTypes && searchRequest.filters.documentTypes.length > 0) {
      query += ' AND c.metadata.contentType IN (@param1)';
      parameters.push(searchRequest.filters.documentTypes.join(','));
    }

    if (searchRequest.filters?.tags && searchRequest.filters.tags.length > 0) {
      query += ' AND ARRAY_CONTAINS(c.metadata.tags, @tag)';
      parameters.push(searchRequest.filters.tags[0]); // Simplified - would handle multiple tags
    }

    // Add date range filter
    if (searchRequest.filters?.dateRange?.startDate) {
      query += ' AND c.metadata.createdAt >= @startDate';
      parameters.push(searchRequest.filters.dateRange.startDate);
    }

    if (searchRequest.filters?.dateRange?.endDate) {
      query += ' AND c.metadata.createdAt <= @endDate';
      parameters.push(searchRequest.filters.dateRange.endDate);
    }

    // Add sorting
    const sortBy = searchRequest.options?.sortBy || 'relevance';
    if (sortBy === 'date') {
      query += ' ORDER BY c.metadata.createdAt DESC';
    } else if (sortBy === 'title') {
      query += ' ORDER BY c.title ASC';
    }

    // Execute search
    const searchResults = await db.queryItems('search-index', query, parameters);

    // Calculate relevance scores (simplified)
    const scoredResults = searchResults.map((result: any) => {
      let score = 0.5; // Base score

      if (searchRequest.query) {
        const queryLower = searchRequest.query.toLowerCase();
        if (result.title.toLowerCase().includes(queryLower)) score += 0.3;
        if (result.content.toLowerCase().includes(queryLower)) score += 0.2;
        if (result.keywords.some((k: string) => k.includes(queryLower))) score += 0.1;
      }

      return {
        ...result,
        score: Math.min(score, 1.0)
      };
    });

    // Filter by minimum score
    const filteredResults = searchRequest.filters?.minScore
      ? scoredResults.filter((r: any) => r.score >= searchRequest.filters!.minScore!)
      : scoredResults;

    // Sort by score if relevance sorting
    if (sortBy === 'relevance') {
      filteredResults.sort((a: any, b: any) => b.score - a.score);
    }

    // Paginate
    const page = searchRequest.options?.page || 1;
    const limit = searchRequest.options?.limit || 20;
    const startIndex = (page - 1) * limit;
    const paginatedResults = filteredResults.slice(startIndex, startIndex + limit);

    // Format results
    const formattedResults: SearchResult[] = paginatedResults.map((result: any) => ({
      documentId: result.documentId,
      title: result.title,
      snippet: result.content.substring(0, 200) + '...',
      score: result.score,
      highlights: searchRequest.options?.includeHighlights ? generateHighlights(result, searchRequest.query) : undefined,
      metadata: {
        fileName: result.metadata.fileName,
        contentType: result.metadata.contentType,
        author: result.metadata.author,
        createdAt: result.metadata.createdAt,
        tags: result.metadata.tags
      }
    }));

    const searchTime = Date.now() - startTime;

    return {
      results: formattedResults,
      totalCount: filteredResults.length,
      facets: searchRequest.options?.includeFacets ? generateFacets(searchResults) : undefined,
      searchTime,
      suggestions: generateSearchSuggestions(searchRequest.query)
    };

  } catch (error) {
    logger.error('Failed to perform document search', { error, query: searchRequest.query });
    return {
      results: [],
      totalCount: 0,
      searchTime: 0,
      suggestions: []
    };
  }
}

function generateHighlights(result: any, query: string): Array<{ field: string; fragments: string[] }> {
  if (!query) return [];

  const highlights = [];
  const queryLower = query.toLowerCase();

  // Title highlights
  if (result.title.toLowerCase().includes(queryLower)) {
    highlights.push({
      field: 'title',
      fragments: [result.title.replace(new RegExp(query, 'gi'), `<mark>$&</mark>`)]
    });
  }

  // Content highlights
  if (result.content.toLowerCase().includes(queryLower)) {
    const contentFragment = result.content.substring(0, 200);
    highlights.push({
      field: 'content',
      fragments: [contentFragment.replace(new RegExp(query, 'gi'), `<mark>$&</mark>`)]
    });
  }

  return highlights;
}

function generateFacets(searchResults: any[]): any {
  const facets: {
    contentTypes: Record<string, number>;
    authors: Record<string, number>;
    tags: Record<string, number>;
  } = {
    contentTypes: {},
    authors: {},
    tags: {}
  };

  searchResults.forEach(result => {
    // Content type facets
    const contentType = result.metadata.contentType;
    facets.contentTypes[contentType] = (facets.contentTypes[contentType] || 0) + 1;

    // Author facets
    const author = result.metadata.author;
    facets.authors[author] = (facets.authors[author] || 0) + 1;

    // Tag facets
    if (result.metadata.tags && Array.isArray(result.metadata.tags)) {
      result.metadata.tags.forEach((tag: string) => {
        facets.tags[tag] = (facets.tags[tag] || 0) + 1;
      });
    }
  });

  return facets;
}

function generateSearchSuggestions(query: string): string[] {
  // In production, this would use search analytics and ML models
  // For now, return simple suggestions
  if (!query) return [];

  return [
    `${query} document`,
    `${query} report`,
    `${query} analysis`
  ];
}

async function storeSearchHistory(searchRequest: SearchDocumentsRequest, user: any, resultCount: number): Promise<void> {
  try {
    await db.createItem('search-history', {
      id: uuidv4(),
      userId: user.id,
      organizationId: searchRequest.organizationId,
      projectId: searchRequest.projectId,
      query: searchRequest.query,
      searchType: searchRequest.type,
      resultCount,
      timestamp: new Date().toISOString(),
      tenantId: user.tenantId
    });
  } catch (error) {
    logger.error('Failed to store search history', { error, userId: user.id, query: searchRequest.query });
  }
}

// Register functions
app.http('search-index-document', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'search/index',
  handler: indexDocument
});

app.http('search-documents', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'search/documents',
  handler: searchDocuments
});

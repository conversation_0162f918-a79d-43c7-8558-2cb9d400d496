/**
 * Notification Service
 * Handles sending notifications via various channels (in-app, email, push)
 */

import { logger } from '../utils/logger';
import { db } from './database';
import { v4 as uuidv4 } from 'uuid';

export interface NotificationRequest {
  userId: string;
  type: string;
  title: string;
  message: string;
  resourceId?: string;
  resourceType?: string;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  metadata?: any;
  channels?: NotificationChannelType[];
  organizationId?: string;
  projectId?: string;
}

export enum NotificationChannelType {
  IN_APP = 'in_app',
  EMAIL = 'email',
  PUSH = 'push',
  WEBHOOK = 'webhook',
  SLACK = 'slack'
}

export interface NotificationChannel {
  type: 'inApp' | 'email' | 'push' | 'webhook';
  enabled: boolean;
  configuration?: any;
}

export interface InAppNotificationRequest {
  userId: string;
  type: string;
  title: string;
  message: string;
  resourceId?: string;
  resourceType?: string;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  metadata?: any;
}

export interface EmailNotificationRequest {
  to: string;
  subject: string;
  body: string;
  template?: string;
  templateData?: any;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
}

export interface PushNotificationRequest {
  userId: string;
  title: string;
  body: string;
  data?: any;
  badge?: number;
  sound?: string;
}

export class NotificationService {
  /**
   * Send a notification through multiple channels
   */
  async sendNotification(request: NotificationRequest): Promise<{
    success: boolean;
    notificationId?: string;
    channels: { type: string; success: boolean; error?: string }[];
  }> {
    try {
      logger.info('Sending notification', {
        userId: request.userId,
        type: request.type,
        title: request.title
      });

      const results: { type: string; success: boolean; error?: string }[] = [];
      let notificationId: string | undefined;

      // Always send in-app notification
      try {
        const inAppResult = await this.sendInAppNotification({
          userId: request.userId,
          type: request.type,
          title: request.title,
          message: request.message,
          resourceId: request.resourceId,
          resourceType: request.resourceType,
          priority: request.priority || 'normal',
          metadata: request.metadata
        });

        notificationId = inAppResult.notificationId;
        results.push({ type: 'inApp', success: true });
      } catch (error) {
        logger.error('Failed to send in-app notification', { error, userId: request.userId });
        results.push({ type: 'inApp', success: false, error: error instanceof Error ? error.message : String(error) });
      }

      // Send through additional channels if specified
      if (request.channels) {
        for (const channelType of request.channels) {
          try {
            switch (channelType) {
              case NotificationChannelType.EMAIL:
                await this.sendEmailNotification({
                  to: request.userId, // This should be resolved to email
                  subject: request.title,
                  body: request.message,
                  priority: request.priority
                });
                results.push({ type: 'email', success: true });
                break;

              case NotificationChannelType.PUSH:
                await this.sendPushNotification({
                  userId: request.userId,
                  title: request.title,
                  body: request.message,
                  data: request.metadata
                });
                results.push({ type: 'push', success: true });
                break;

              case NotificationChannelType.WEBHOOK:
                await this.sendWebhookNotification(request, {});
                results.push({ type: 'webhook', success: true });
                break;

              case NotificationChannelType.SLACK:
                // Slack notification implementation would go here
                logger.info('Slack notification would be sent', { userId: request.userId });
                results.push({ type: 'slack', success: true });
                break;

              default:
                logger.warn('Unknown notification channel type', { type: channelType });
            }
          } catch (error) {
            logger.error(`Failed to send ${channelType} notification`, { error, userId: request.userId });
            results.push({
              type: channelType,
              success: false,
              error: error instanceof Error ? error.message : String(error)
            });
          }
        }
      }

      const overallSuccess = results.some(r => r.success);

      logger.info('Notification sending completed', {
        userId: request.userId,
        notificationId,
        overallSuccess,
        results
      });

      return {
        success: overallSuccess,
        notificationId,
        channels: results
      };

    } catch (error) {
      logger.error('Failed to send notification', { error, request });
      throw error;
    }
  }

  /**
   * Send in-app notification
   */
  async sendInAppNotification(request: InAppNotificationRequest): Promise<{
    success: boolean;
    notificationId: string;
  }> {
    try {
      const notificationId = uuidv4();
      const notification = {
        id: notificationId,
        userId: request.userId,
        type: request.type,
        title: request.title,
        message: request.message,
        resourceType: request.resourceType,
        resourceId: request.resourceId,
        priority: request.priority || 'normal',
        status: 'unread',
        createdAt: new Date().toISOString(),
        metadata: request.metadata,
        tenantId: request.userId // Simplified tenant resolution
      };

      await db.createItem('notifications', notification);

      logger.info('In-app notification sent successfully', {
        notificationId,
        userId: request.userId,
        type: request.type
      });

      return {
        success: true,
        notificationId
      };

    } catch (error) {
      logger.error('Failed to send in-app notification', { error, request });
      throw error;
    }
  }

  /**
   * Send email notification (simplified implementation)
   */
  async sendEmailNotification(request: EmailNotificationRequest): Promise<{
    success: boolean;
    messageId?: string;
  }> {
    try {
      // This is a simplified implementation
      // In production, this would integrate with email service like SendGrid, Postmark, etc.

      logger.info('Email notification would be sent', {
        to: request.to,
        subject: request.subject,
        priority: request.priority
      });

      // Simulate email sending
      const messageId = uuidv4();

      return {
        success: true,
        messageId
      };

    } catch (error) {
      logger.error('Failed to send email notification', { error, request });
      throw error;
    }
  }

  /**
   * Send push notification (simplified implementation)
   */
  async sendPushNotification(request: PushNotificationRequest): Promise<{
    success: boolean;
    messageId?: string;
  }> {
    try {
      // This is a simplified implementation
      // In production, this would integrate with Azure Notification Hubs or similar service

      logger.info('Push notification would be sent', {
        userId: request.userId,
        title: request.title,
        body: request.body
      });

      // Simulate push notification sending
      const messageId = uuidv4();

      return {
        success: true,
        messageId
      };

    } catch (error) {
      logger.error('Failed to send push notification', { error, request });
      throw error;
    }
  }

  /**
   * Send webhook notification
   */
  async sendWebhookNotification(request: NotificationRequest, webhookConfig: any): Promise<{
    success: boolean;
    responseStatus?: number;
  }> {
    try {
      if (!webhookConfig?.url) {
        throw new Error('Webhook URL not configured');
      }

      const payload = {
        type: 'notification',
        data: {
          userId: request.userId,
          notificationType: request.type,
          title: request.title,
          message: request.message,
          resourceId: request.resourceId,
          resourceType: request.resourceType,
          priority: request.priority,
          metadata: request.metadata,
          timestamp: new Date().toISOString()
        }
      };

      const response = await fetch(webhookConfig.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'DocuContext-Notifications/1.0',
          ...(webhookConfig.headers || {})
        },
        body: JSON.stringify(payload)
      });

      logger.info('Webhook notification sent', {
        url: webhookConfig.url,
        status: response.status,
        userId: request.userId
      });

      return {
        success: response.ok,
        responseStatus: response.status
      };

    } catch (error) {
      logger.error('Failed to send webhook notification', { error, request, webhookConfig });
      throw error;
    }
  }

  /**
   * Get user notification preferences
   */
  async getUserNotificationPreferences(userId: string): Promise<{
    inApp: boolean;
    email: boolean;
    push: boolean;
    channels: NotificationChannel[];
  }> {
    try {
      const user = await db.readItem('users', userId, userId);

      if (!user) {
        throw new Error('User not found');
      }

      const preferences = (user as any).preferences?.notifications || {};

      return {
        inApp: preferences.inApp !== false, // Default to true
        email: preferences.email !== false, // Default to true
        push: preferences.push || false, // Default to false
        channels: preferences.channels || []
      };

    } catch (error) {
      logger.error('Failed to get user notification preferences', { error, userId });
      throw error;
    }
  }

  /**
   * Mark notification as read
   */
  async markNotificationAsRead(notificationId: string, userId: string): Promise<boolean> {
    try {
      const notification = await db.readItem('notifications', notificationId, notificationId);

      if (!notification) {
        throw new Error('Notification not found');
      }

      if ((notification as any).userId !== userId) {
        throw new Error('Access denied');
      }

      const updatedNotification = {
        ...(notification as any),
        status: 'read',
        readAt: new Date().toISOString()
      };

      await db.updateItem('notifications', updatedNotification);

      logger.info('Notification marked as read', { notificationId, userId });

      return true;

    } catch (error) {
      logger.error('Failed to mark notification as read', { error, notificationId, userId });
      throw error;
    }
  }

  /**
   * Get user notifications with pagination
   */
  async getUserNotifications(userId: string, options: {
    page?: number;
    limit?: number;
    status?: 'unread' | 'read' | 'all';
    type?: string;
  } = {}): Promise<{
    notifications: any[];
    total: number;
    unreadCount: number;
    page: number;
    limit: number;
    hasMore: boolean;
  }> {
    try {
      const page = options.page || 1;
      const limit = Math.min(options.limit || 20, 100);
      const offset = (page - 1) * limit;

      let query = 'SELECT * FROM c WHERE c.userId = @userId';
      const parameters = [{ name: '@userId', value: userId }];

      if (options.status && options.status !== 'all') {
        query += ' AND c.status = @status';
        parameters.push({ name: '@status', value: options.status });
      }

      if (options.type) {
        query += ' AND c.type = @type';
        parameters.push({ name: '@type', value: options.type });
      }

      query += ' ORDER BY c.createdAt DESC';

      const notifications = await db.queryItems('notifications', query, parameters);

      // Get unread count
      const unreadQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.userId = @userId AND c.status = @status';
      const unreadResult = await db.queryItems('notifications', unreadQuery, [userId, 'unread']);
      const unreadCount = Number(unreadResult[0]) || 0;

      const total = notifications.length;
      const paginatedNotifications = notifications.slice(offset, offset + limit);

      return {
        notifications: paginatedNotifications,
        total,
        unreadCount,
        page,
        limit,
        hasMore: offset + limit < total
      };

    } catch (error) {
      logger.error('Failed to get user notifications', { error, userId, options });
      throw error;
    }
  }
}

// Export singleton instance
export const notificationService = new NotificationService();

/**
 * Workflow Scheduling Function
 * Handles workflow scheduling, triggers, and automated execution
 * Migrated from old-arch/src/workflow-service/scheduler/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { eventService } from '../shared/services/event';

// Scheduling types and enums
enum ScheduleType {
  ONCE = 'ONCE',
  RECURRING = 'RECURRING',
  CRON = 'CRON',
  EVENT_BASED = 'EVENT_BASED'
}

enum TriggerType {
  TIME_BASED = 'TIME_BASED',
  DOCUMENT_UPLOAD = 'DOCUMENT_UPLOAD',
  DOCUMENT_UPDATE = 'DOCUMENT_UPDATE',
  USER_ACTION = 'USER_ACTION',
  EXTERNAL_EVENT = 'EXTERNAL_EVENT',
  CONDITION_MET = 'CONDITION_MET'
}

enum ScheduleStatus {
  ACTIVE = 'ACTIVE',
  PAUSED = 'PAUSED',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

// Validation schemas
const createScheduleSchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  description: Joi.string().max(500).optional(),
  workflowId: Joi.string().uuid().required(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional(),
  scheduleType: Joi.string().valid(...Object.values(ScheduleType)).required(),
  trigger: Joi.object({
    type: Joi.string().valid(...Object.values(TriggerType)).required(),
    configuration: Joi.object({
      // Time-based triggers
      startDate: Joi.string().isoDate().optional(),
      endDate: Joi.string().isoDate().optional(),
      cronExpression: Joi.string().optional(),
      interval: Joi.object({
        value: Joi.number().min(1).optional(),
        unit: Joi.string().valid('minutes', 'hours', 'days', 'weeks', 'months').optional()
      }).optional(),
      timezone: Joi.string().default('UTC'),

      // Event-based triggers
      eventType: Joi.string().optional(),
      conditions: Joi.array().items(Joi.object({
        field: Joi.string().required(),
        operator: Joi.string().valid('equals', 'not_equals', 'contains', 'greater_than', 'less_than', 'exists').required(),
        value: Joi.any().optional()
      })).optional(),

      // Document-based triggers
      documentFilters: Joi.object({
        categories: Joi.array().items(Joi.string()).optional(),
        tags: Joi.array().items(Joi.string()).optional(),
        contentTypes: Joi.array().items(Joi.string()).optional(),
        authors: Joi.array().items(Joi.string().uuid()).optional()
      }).optional()
    }).required()
  }).required(),
  parameters: Joi.object().optional(),
  settings: Joi.object({
    maxExecutions: Joi.number().min(1).optional(),
    retryAttempts: Joi.number().min(0).max(5).default(3),
    retryDelay: Joi.number().min(1000).max(300000).default(5000),
    timeout: Joi.number().min(30000).max(3600000).default(300000),
    allowConcurrent: Joi.boolean().default(false),
    notifyOnFailure: Joi.boolean().default(true),
    notifyOnSuccess: Joi.boolean().default(false)
  }).optional()
});

const updateScheduleStatusSchema = Joi.object({
  scheduleId: Joi.string().uuid().required(),
  status: Joi.string().valid(...Object.values(ScheduleStatus)).required(),
  reason: Joi.string().max(500).optional()
});

interface CreateScheduleRequest {
  name: string;
  description?: string;
  workflowId: string;
  organizationId: string;
  projectId?: string;
  scheduleType: ScheduleType;
  trigger: {
    type: TriggerType;
    configuration: {
      startDate?: string;
      endDate?: string;
      cronExpression?: string;
      interval?: {
        value?: number;
        unit?: string;
      };
      timezone?: string;
      eventType?: string;
      conditions?: Array<{
        field: string;
        operator: string;
        value?: any;
      }>;
      documentFilters?: {
        categories?: string[];
        tags?: string[];
        contentTypes?: string[];
        authors?: string[];
      };
    };
  };
  parameters?: any;
  settings?: {
    maxExecutions?: number;
    retryAttempts?: number;
    retryDelay?: number;
    timeout?: number;
    allowConcurrent?: boolean;
    notifyOnFailure?: boolean;
    notifyOnSuccess?: boolean;
  };
}

interface WorkflowSchedule {
  id: string;
  name: string;
  description?: string;
  workflowId: string;
  organizationId: string;
  projectId?: string;
  scheduleType: ScheduleType;
  status: ScheduleStatus;
  trigger: {
    type: TriggerType;
    configuration: any;
  };
  parameters: any;
  settings: {
    maxExecutions?: number;
    retryAttempts: number;
    retryDelay: number;
    timeout: number;
    allowConcurrent: boolean;
    notifyOnFailure: boolean;
    notifyOnSuccess: boolean;
  };
  execution: {
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    lastExecution?: string;
    nextExecution?: string;
    averageExecutionTime?: number;
  };
  createdBy: string;
  createdAt: string;
  updatedBy: string;
  updatedAt: string;
  tenantId: string;
}

/**
 * Create workflow schedule handler
 */
export async function createWorkflowSchedule(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create workflow schedule started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createScheduleSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const scheduleRequest: CreateScheduleRequest = value;

    // Check organization access
    const hasAccess = await checkOrganizationAccess(scheduleRequest.organizationId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Validate workflow exists and is accessible
    const workflow = await db.readItem('workflows', scheduleRequest.workflowId, scheduleRequest.workflowId);
    if (!workflow) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Workflow not found" }
      }, request);
    }

    const workflowData = workflow as any;
    if (workflowData.organizationId !== scheduleRequest.organizationId) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to workflow" }
      }, request);
    }

    // Check schedule creation limits
    const canCreate = await checkScheduleCreationLimits(scheduleRequest.organizationId);
    if (!canCreate.allowed) {
      return addCorsHeaders({
        status: 429,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: canCreate.reason }
      }, request);
    }

    // Validate schedule configuration
    const validationResult = validateScheduleConfiguration(scheduleRequest);
    if (!validationResult.valid) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Schedule Configuration Error',
          message: validationResult.errors.join(', ')
        }
      }, request);
    }

    // Create workflow schedule
    const scheduleId = uuidv4();
    const now = new Date().toISOString();

    const workflowSchedule: WorkflowSchedule = {
      id: scheduleId,
      name: scheduleRequest.name,
      description: scheduleRequest.description,
      workflowId: scheduleRequest.workflowId,
      organizationId: scheduleRequest.organizationId,
      projectId: scheduleRequest.projectId,
      scheduleType: scheduleRequest.scheduleType,
      status: ScheduleStatus.ACTIVE,
      trigger: scheduleRequest.trigger,
      parameters: scheduleRequest.parameters || {},
      settings: {
        retryAttempts: 3,
        retryDelay: 5000,
        timeout: 300000,
        allowConcurrent: false,
        notifyOnFailure: true,
        notifyOnSuccess: false,
        ...scheduleRequest.settings
      },
      execution: {
        totalExecutions: 0,
        successfulExecutions: 0,
        failedExecutions: 0,
        nextExecution: calculateNextExecution(scheduleRequest)
      },
      createdBy: user.id,
      createdAt: now,
      updatedBy: user.id,
      updatedAt: now,
      tenantId: user.tenantId || user.id
    };

    await db.createItem('workflow-schedules', workflowSchedule);

    // Register schedule with scheduler service
    await registerScheduleWithScheduler(workflowSchedule);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "workflow_schedule_created",
      userId: user.id,
      organizationId: scheduleRequest.organizationId,
      projectId: scheduleRequest.projectId,
      timestamp: now,
      details: {
        scheduleId,
        scheduleName: scheduleRequest.name,
        workflowId: scheduleRequest.workflowId,
        workflowName: workflowData.name,
        scheduleType: scheduleRequest.scheduleType,
        triggerType: scheduleRequest.trigger.type,
        nextExecution: workflowSchedule.execution.nextExecution
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'WorkflowScheduleCreated',
      aggregateId: scheduleId,
      aggregateType: 'WorkflowSchedule',
      version: 1,
      data: {
        schedule: workflowSchedule,
        workflow: workflowData,
        createdBy: user.id
      },
      userId: user.id,
      organizationId: scheduleRequest.organizationId,
      tenantId: user.tenantId
    });

    logger.info("Workflow schedule created successfully", {
      correlationId,
      scheduleId,
      scheduleName: scheduleRequest.name,
      workflowId: scheduleRequest.workflowId,
      scheduleType: scheduleRequest.scheduleType,
      createdBy: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: scheduleId,
        name: scheduleRequest.name,
        workflowId: scheduleRequest.workflowId,
        workflowName: workflowData.name,
        scheduleType: scheduleRequest.scheduleType,
        status: ScheduleStatus.ACTIVE,
        nextExecution: workflowSchedule.execution.nextExecution,
        createdAt: now,
        message: "Workflow schedule created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create workflow schedule failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Update schedule status handler
 */
export async function updateScheduleStatus(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Update schedule status started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = updateScheduleStatusSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const updateRequest = value;

    // Get schedule
    const schedule = await db.readItem('workflow-schedules', updateRequest.scheduleId, updateRequest.scheduleId);
    if (!schedule) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Workflow schedule not found" }
      }, request);
    }

    const scheduleData = schedule as any;

    // Check access
    const hasAccess = await checkOrganizationAccess(scheduleData.organizationId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to workflow schedule" }
      }, request);
    }

    // Update schedule status
    const updatedSchedule = {
      ...scheduleData,
      id: updateRequest.scheduleId,
      status: updateRequest.status,
      updatedBy: user.id,
      updatedAt: new Date().toISOString()
    };

    await db.updateItem('workflow-schedules', updatedSchedule);

    // Update scheduler service
    if (updateRequest.status === ScheduleStatus.PAUSED || updateRequest.status === ScheduleStatus.CANCELLED) {
      await unregisterScheduleFromScheduler(updateRequest.scheduleId);
    } else if (updateRequest.status === ScheduleStatus.ACTIVE && scheduleData.status !== ScheduleStatus.ACTIVE) {
      await registerScheduleWithScheduler(updatedSchedule);
    }

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "workflow_schedule_status_updated",
      userId: user.id,
      organizationId: scheduleData.organizationId,
      projectId: scheduleData.projectId,
      timestamp: new Date().toISOString(),
      details: {
        scheduleId: updateRequest.scheduleId,
        scheduleName: scheduleData.name,
        oldStatus: scheduleData.status,
        newStatus: updateRequest.status,
        reason: updateRequest.reason
      },
      tenantId: user.tenantId
    });

    logger.info("Workflow schedule status updated successfully", {
      correlationId,
      scheduleId: updateRequest.scheduleId,
      scheduleName: scheduleData.name,
      oldStatus: scheduleData.status,
      newStatus: updateRequest.status,
      updatedBy: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        scheduleId: updateRequest.scheduleId,
        scheduleName: scheduleData.name,
        oldStatus: scheduleData.status,
        newStatus: updateRequest.status,
        updatedAt: updatedSchedule.updatedAt,
        message: "Workflow schedule status updated successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Update schedule status failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkOrganizationAccess(organizationId: string, userId: string): Promise<boolean> {
  try {
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
    return memberships.length > 0;
  } catch (error) {
    logger.error('Failed to check organization access', { error, organizationId, userId });
    return false;
  }
}

async function checkScheduleCreationLimits(organizationId: string): Promise<{ allowed: boolean; reason?: string }> {
  try {
    // Get organization to check tier
    const organization = await db.readItem('organizations', organizationId, organizationId);
    if (!organization) {
      return { allowed: false, reason: 'Organization not found' };
    }

    const orgData = organization as any;
    const tier = orgData.tier || 'FREE';

    // Define tier limits
    const limits: { [key: string]: { maxSchedules: number } } = {
      'FREE': { maxSchedules: 5 },
      'PROFESSIONAL': { maxSchedules: 50 },
      'ENTERPRISE': { maxSchedules: -1 } // Unlimited
    };

    const limit = limits[tier] || limits['FREE'];

    if (limit.maxSchedules === -1) {
      return { allowed: true };
    }

    // Check current schedule count
    const scheduleCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.status IN (@active, @paused)';
    const countResult = await db.queryItems('workflow-schedules', scheduleCountQuery, [organizationId, ScheduleStatus.ACTIVE, ScheduleStatus.PAUSED]);
    const currentCount = Number(countResult[0]) || 0;

    if (currentCount >= limit.maxSchedules) {
      return {
        allowed: false,
        reason: `Workflow schedule limit reached (${limit.maxSchedules})`
      };
    }

    return { allowed: true };

  } catch (error) {
    logger.error('Failed to check schedule creation limits', { error, organizationId });
    return { allowed: false, reason: 'Failed to check limits' };
  }
}

function validateScheduleConfiguration(scheduleRequest: CreateScheduleRequest): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Validate time-based triggers
  if (scheduleRequest.trigger.type === TriggerType.TIME_BASED) {
    if (scheduleRequest.scheduleType === ScheduleType.CRON && !scheduleRequest.trigger.configuration.cronExpression) {
      errors.push('Cron expression is required for CRON schedule type');
    }

    if (scheduleRequest.scheduleType === ScheduleType.RECURRING && !scheduleRequest.trigger.configuration.interval) {
      errors.push('Interval is required for RECURRING schedule type');
    }

    if (scheduleRequest.trigger.configuration.startDate && scheduleRequest.trigger.configuration.endDate) {
      const startDate = new Date(scheduleRequest.trigger.configuration.startDate);
      const endDate = new Date(scheduleRequest.trigger.configuration.endDate);

      if (startDate >= endDate) {
        errors.push('End date must be after start date');
      }
    }
  }

  // Validate event-based triggers
  if (scheduleRequest.trigger.type === TriggerType.EXTERNAL_EVENT && !scheduleRequest.trigger.configuration.eventType) {
    errors.push('Event type is required for event-based triggers');
  }

  // Validate condition-based triggers
  if (scheduleRequest.trigger.type === TriggerType.CONDITION_MET &&
      (!scheduleRequest.trigger.configuration.conditions || scheduleRequest.trigger.configuration.conditions.length === 0)) {
    errors.push('Conditions are required for condition-based triggers');
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

function calculateNextExecution(scheduleRequest: CreateScheduleRequest): string | undefined {
  const now = new Date();

  if (scheduleRequest.scheduleType === ScheduleType.ONCE) {
    return scheduleRequest.trigger.configuration.startDate || new Date(now.getTime() + 60000).toISOString(); // 1 minute from now
  }

  if (scheduleRequest.scheduleType === ScheduleType.RECURRING && scheduleRequest.trigger.configuration.interval) {
    const interval = scheduleRequest.trigger.configuration.interval;
    const startDate = scheduleRequest.trigger.configuration.startDate ? new Date(scheduleRequest.trigger.configuration.startDate) : now;

    let nextExecution = new Date(startDate);

    switch (interval.unit) {
      case 'minutes':
        nextExecution.setMinutes(nextExecution.getMinutes() + (interval.value || 1));
        break;
      case 'hours':
        nextExecution.setHours(nextExecution.getHours() + (interval.value || 1));
        break;
      case 'days':
        nextExecution.setDate(nextExecution.getDate() + (interval.value || 1));
        break;
      case 'weeks':
        nextExecution.setDate(nextExecution.getDate() + (interval.value || 1) * 7);
        break;
      case 'months':
        nextExecution.setMonth(nextExecution.getMonth() + (interval.value || 1));
        break;
    }

    return nextExecution.toISOString();
  }

  if (scheduleRequest.scheduleType === ScheduleType.CRON) {
    // Simplified cron calculation - in production, use a proper cron library
    return new Date(now.getTime() + 60000).toISOString(); // 1 minute from now
  }

  return undefined; // Event-based schedules don't have predetermined next execution
}

async function registerScheduleWithScheduler(schedule: WorkflowSchedule): Promise<void> {
  try {
    // In production, this would register the schedule with a proper scheduler service
    // For now, we'll just log the registration
    logger.info('Schedule registered with scheduler', {
      scheduleId: schedule.id,
      scheduleName: schedule.name,
      scheduleType: schedule.scheduleType,
      nextExecution: schedule.execution.nextExecution
    });
  } catch (error) {
    logger.error('Failed to register schedule with scheduler', { error, scheduleId: schedule.id });
  }
}

async function unregisterScheduleFromScheduler(scheduleId: string): Promise<void> {
  try {
    // In production, this would unregister the schedule from the scheduler service
    // For now, we'll just log the unregistration
    logger.info('Schedule unregistered from scheduler', { scheduleId });
  } catch (error) {
    logger.error('Failed to unregister schedule from scheduler', { error, scheduleId });
  }
}

// Register functions
app.http('workflow-schedule-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'workflows/schedules',
  handler: createWorkflowSchedule
});

app.http('workflow-schedule-status-update', {
  methods: ['PUT', 'OPTIONS'],
  authLevel: 'function',
  route: 'workflows/schedules/status',
  handler: updateScheduleStatus
});

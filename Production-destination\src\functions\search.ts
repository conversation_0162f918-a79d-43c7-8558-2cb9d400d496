/**
 * Search Function
 * Handles searching across documents, projects, and organizations
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Search types enum
enum SearchType {
  ALL = 'ALL',
  DOCUMENTS = 'DOCUMENTS',
  PROJECTS = 'PROJECTS',
  ORGANIZATIONS = 'ORGANIZATIONS',
  WORKFLOWS = 'WORKFLOWS',
  USERS = 'USERS'
}

// Validation schema
const searchSchema = Joi.object({
  query: Joi.string().required().min(1).max(200),
  type: Joi.string().valid(...Object.values(SearchType)).default(SearchType.ALL),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  organizationId: Joi.string().uuid().optional(),
  projectId: Joi.string().uuid().optional(),
  filters: Joi.object({
    contentType: Joi.string().optional(),
    tags: Joi.array().items(Joi.string()).optional(),
    dateFrom: Joi.date().iso().optional(),
    dateTo: Joi.date().iso().optional(),
    createdBy: Joi.string().uuid().optional()
  }).optional()
});

interface SearchResult {
  id: string;
  type: string;
  title: string;
  description?: string;
  content?: string;
  url: string;
  score: number;
  highlights: string[];
  metadata: {
    createdAt: string;
    createdBy: string;
    organizationId?: string;
    projectId?: string;
    contentType?: string;
    tags?: string[];
  };
}

/**
 * Search handler
 */
export async function search(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Search started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate query parameters
    const queryParams = Object.fromEntries(request.query.entries());
    const { error, value } = searchSchema.validate(queryParams);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { query, type, page, limit, organizationId, projectId, filters } = value;

    let allResults: SearchResult[] = [];

    // Search documents
    if (type === SearchType.ALL || type === SearchType.DOCUMENTS) {
      const documentResults = await searchDocuments(query, user, organizationId, projectId, filters);
      allResults.push(...documentResults);
    }

    // Search projects
    if (type === SearchType.ALL || type === SearchType.PROJECTS) {
      const projectResults = await searchProjects(query, user, organizationId, filters);
      allResults.push(...projectResults);
    }

    // Search organizations
    if (type === SearchType.ALL || type === SearchType.ORGANIZATIONS) {
      const organizationResults = await searchOrganizations(query, user, filters);
      allResults.push(...organizationResults);
    }

    // Search workflows
    if (type === SearchType.ALL || type === SearchType.WORKFLOWS) {
      const workflowResults = await searchWorkflows(query, user, organizationId, projectId, filters);
      allResults.push(...workflowResults);
    }

    // Sort results by score (relevance)
    allResults.sort((a, b) => b.score - a.score);

    // Apply pagination
    const total = allResults.length;
    const offset = (page - 1) * limit;
    const paginatedResults = allResults.slice(offset, offset + limit);

    // Group results by type for summary
    const resultsByType = allResults.reduce((acc: any, result) => {
      acc[result.type] = (acc[result.type] || 0) + 1;
      return acc;
    }, {});

    logger.info("Search completed successfully", {
      correlationId,
      userId: user.id,
      query,
      type,
      totalResults: total,
      page,
      limit
    });

    // Create response
    const response = {
      query,
      type,
      results: paginatedResults,
      total,
      page,
      limit,
      hasMore: offset + limit < total,
      summary: {
        totalResults: total,
        resultsByType
      }
    };

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: response
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Search failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Search documents
 */
async function searchDocuments(query: string, user: any, organizationId?: string, projectId?: string, filters?: any): Promise<SearchResult[]> {
  let queryText = `
    SELECT * FROM c 
    WHERE (CONTAINS(LOWER(c.name), LOWER(@query)) 
           OR CONTAINS(LOWER(c.description), LOWER(@query))
           OR CONTAINS(LOWER(c.extractedText), LOWER(@query)))
  `;
  const parameters: any[] = [query];

  // Add access control
  queryText += ' AND (c.createdBy = @userId OR c.organizationId = @tenantId)';
  parameters.push(user.id, user.tenantId);

  // Add filters
  if (organizationId) {
    queryText += ' AND c.organizationId = @organizationId';
    parameters.push(organizationId);
  }

  if (projectId) {
    queryText += ' AND c.projectId = @projectId';
    parameters.push(projectId);
  }

  if (filters?.contentType) {
    queryText += ' AND c.contentType = @contentType';
    parameters.push(filters.contentType);
  }

  if (filters?.dateFrom) {
    queryText += ' AND c.createdAt >= @dateFrom';
    parameters.push(filters.dateFrom);
  }

  if (filters?.dateTo) {
    queryText += ' AND c.createdAt <= @dateTo';
    parameters.push(filters.dateTo);
  }

  const documents = await db.queryItems('documents', queryText, parameters);

  return documents.map((doc: any) => ({
    id: doc.id,
    type: 'DOCUMENT',
    title: doc.name,
    description: doc.description,
    content: doc.extractedText?.substring(0, 200) + '...',
    url: `/documents/${doc.id}`,
    score: calculateRelevanceScore(query, doc.name, doc.description, doc.extractedText),
    highlights: extractHighlights(query, [doc.name, doc.description, doc.extractedText]),
    metadata: {
      createdAt: doc.createdAt,
      createdBy: doc.createdBy,
      organizationId: doc.organizationId,
      projectId: doc.projectId,
      contentType: doc.contentType,
      tags: doc.tags
    }
  }));
}

/**
 * Search projects
 */
async function searchProjects(query: string, user: any, organizationId?: string, filters?: any): Promise<SearchResult[]> {
  let queryText = `
    SELECT * FROM c 
    WHERE (CONTAINS(LOWER(c.name), LOWER(@query)) 
           OR CONTAINS(LOWER(c.description), LOWER(@query)))
    AND ARRAY_CONTAINS(c.memberIds, @userId)
  `;
  const parameters: any[] = [query, user.id];

  if (organizationId) {
    queryText += ' AND c.organizationId = @organizationId';
    parameters.push(organizationId);
  }

  const projects = await db.queryItems('projects', queryText, parameters);

  return projects.map((project: any) => ({
    id: project.id,
    type: 'PROJECT',
    title: project.name,
    description: project.description,
    url: `/projects/${project.id}`,
    score: calculateRelevanceScore(query, project.name, project.description),
    highlights: extractHighlights(query, [project.name, project.description]),
    metadata: {
      createdAt: project.createdAt,
      createdBy: project.createdBy,
      organizationId: project.organizationId,
      tags: project.tags
    }
  }));
}

/**
 * Search organizations
 */
async function searchOrganizations(query: string, user: any, filters?: any): Promise<SearchResult[]> {
  // Get user's organization memberships
  const membershipQuery = 'SELECT DISTINCT c.organizationId FROM c WHERE c.userId = @userId AND c.status = @status';
  const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, 'active']);
  
  if (memberships.length === 0) return [];

  const orgIds = memberships.map((m: any) => m.organizationId);
  
  let queryText = `
    SELECT * FROM c 
    WHERE (CONTAINS(LOWER(c.name), LOWER(@query)) 
           OR CONTAINS(LOWER(c.description), LOWER(@query)))
    AND ARRAY_CONTAINS(@orgIds, c.id)
  `;
  const parameters: any[] = [query, orgIds];

  const organizations = await db.queryItems('organizations', queryText, parameters);

  return organizations.map((org: any) => ({
    id: org.id,
    type: 'ORGANIZATION',
    title: org.name,
    description: org.description,
    url: `/organizations/${org.id}`,
    score: calculateRelevanceScore(query, org.name, org.description),
    highlights: extractHighlights(query, [org.name, org.description]),
    metadata: {
      createdAt: org.createdAt,
      createdBy: org.createdBy
    }
  }));
}

/**
 * Search workflows
 */
async function searchWorkflows(query: string, user: any, organizationId?: string, projectId?: string, filters?: any): Promise<SearchResult[]> {
  let queryText = `
    SELECT * FROM c 
    WHERE (CONTAINS(LOWER(c.name), LOWER(@query)) 
           OR CONTAINS(LOWER(c.description), LOWER(@query)))
    AND (c.createdBy = @userId OR c.organizationId = @tenantId)
  `;
  const parameters: any[] = [query, user.id, user.tenantId];

  if (organizationId) {
    queryText += ' AND c.organizationId = @organizationId';
    parameters.push(organizationId);
  }

  if (projectId) {
    queryText += ' AND c.projectId = @projectId';
    parameters.push(projectId);
  }

  const workflows = await db.queryItems('workflows', queryText, parameters);

  return workflows.map((workflow: any) => ({
    id: workflow.id,
    type: 'WORKFLOW',
    title: workflow.name,
    description: workflow.description,
    url: `/workflows/${workflow.id}`,
    score: calculateRelevanceScore(query, workflow.name, workflow.description),
    highlights: extractHighlights(query, [workflow.name, workflow.description]),
    metadata: {
      createdAt: workflow.createdAt,
      createdBy: workflow.createdBy,
      organizationId: workflow.organizationId,
      projectId: workflow.projectId
    }
  }));
}

/**
 * Calculate relevance score
 */
function calculateRelevanceScore(query: string, ...texts: string[]): number {
  const queryLower = query.toLowerCase();
  let score = 0;

  texts.forEach((text, index) => {
    if (!text) return;
    
    const textLower = text.toLowerCase();
    
    // Exact match gets highest score
    if (textLower.includes(queryLower)) {
      score += (10 - index) * 10;
    }
    
    // Word matches
    const queryWords = queryLower.split(' ');
    queryWords.forEach(word => {
      if (textLower.includes(word)) {
        score += (10 - index) * 2;
      }
    });
  });

  return score;
}

/**
 * Extract highlights
 */
function extractHighlights(query: string, texts: string[]): string[] {
  const highlights: string[] = [];
  const queryLower = query.toLowerCase();

  texts.forEach(text => {
    if (!text) return;
    
    const textLower = text.toLowerCase();
    const index = textLower.indexOf(queryLower);
    
    if (index !== -1) {
      const start = Math.max(0, index - 50);
      const end = Math.min(text.length, index + queryLower.length + 50);
      const highlight = text.substring(start, end);
      highlights.push(highlight);
    }
  });

  return highlights.slice(0, 3); // Limit to 3 highlights
}

// Register functions
app.http('search', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'search',
  handler: search
});

/**
 * Document Versioning Function
 * Handles document version management, comparison, and restoration
 * Migrated from old-arch/src/document-service/versioning/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { BlobServiceClient } from "@azure/storage-blob";
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { notificationService } from '../shared/services/notification';
import { eventService } from '../shared/services/event';

// Version types and enums
enum VersionType {
  MAJOR = 'major',
  MINOR = 'minor',
  PATCH = 'patch',
  AUTO = 'auto'
}

enum VersionStatus {
  ACTIVE = 'active',
  ARCHIVED = 'archived',
  DELETED = 'deleted'
}

// Validation schemas
const createVersionSchema = Joi.object({
  documentId: Joi.string().uuid().required(),
  versionType: Joi.string().valid(...Object.values(VersionType)).default(VersionType.AUTO),
  comment: Joi.string().max(500).optional(),
  tags: Joi.array().items(Joi.string().max(50)).max(10).default([]),
  isMinor: Joi.boolean().default(false)
});

const restoreVersionSchema = Joi.object({
  documentId: Joi.string().uuid().required(),
  versionId: Joi.string().uuid().required(),
  comment: Joi.string().max(500).optional(),
  createBackup: Joi.boolean().default(true)
});

interface CreateVersionRequest {
  documentId: string;
  versionType: VersionType;
  comment?: string;
  tags: string[];
  isMinor: boolean;
}

interface RestoreVersionRequest {
  documentId: string;
  versionId: string;
  comment?: string;
  createBackup: boolean;
}

interface DocumentVersion {
  id: string;
  documentId: string;
  versionNumber: string;
  versionType: VersionType;
  status: VersionStatus;
  comment?: string;
  tags: string[];
  fileSize: number;
  contentHash: string;
  blobName: string;
  createdBy: string;
  createdAt: string;
  metadata: {
    changes: string[];
    previousVersionId?: string;
    isRestored: boolean;
    restoredFrom?: string;
  };
}

/**
 * Create document version handler
 */
export async function createDocumentVersion(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create document version started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createVersionSchema.validate(body);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const versionRequest: CreateVersionRequest = value;

    // Get document and verify access
    const document = await db.readItem('documents', versionRequest.documentId, versionRequest.documentId);
    if (!document) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document not found" }
      }, request);
    }

    const documentData = document as any;

    // Check edit permissions
    const hasEditPermission = await checkDocumentEditPermission(documentData, user);
    if (!hasEditPermission) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Insufficient permissions to create document versions" }
      }, request);
    }

    // Get current version number
    const currentVersionNumber = await getCurrentVersionNumber(versionRequest.documentId);
    const newVersionNumber = calculateNextVersionNumber(currentVersionNumber, versionRequest.versionType);

    // Create version from current document
    const versionId = uuidv4();
    const now = new Date().toISOString();

    // Copy current document to version blob storage
    const versionBlobName = await createVersionBlob(documentData, versionId);

    // Calculate content hash for integrity
    const contentHash = await calculateContentHash(documentData.blobName);

    // Detect changes from previous version
    const changes = await detectDocumentChanges(versionRequest.documentId, documentData);

    const documentVersion: DocumentVersion = {
      id: versionId,
      documentId: versionRequest.documentId,
      versionNumber: newVersionNumber,
      versionType: versionRequest.versionType,
      status: VersionStatus.ACTIVE,
      comment: versionRequest.comment,
      tags: versionRequest.tags,
      fileSize: documentData.size || 0,
      contentHash,
      blobName: versionBlobName,
      createdBy: user.id,
      createdAt: now,
      metadata: {
        changes,
        previousVersionId: await getPreviousVersionId(versionRequest.documentId),
        isRestored: false
      }
    };

    await db.createItem('document-versions', documentVersion);

    // Update document with latest version info
    const updatedDocument = {
      ...documentData,
      currentVersionId: versionId,
      currentVersionNumber: newVersionNumber,
      versionCount: (documentData.versionCount || 0) + 1,
      updatedAt: now,
      updatedBy: user.id
    };

    await db.updateItem('documents', updatedDocument);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "document_version_created",
      userId: user.id,
      organizationId: documentData.organizationId,
      projectId: documentData.projectId,
      documentId: versionRequest.documentId,
      timestamp: now,
      details: {
        versionId,
        versionNumber: newVersionNumber,
        versionType: versionRequest.versionType,
        documentName: documentData.name,
        changeCount: changes.length,
        comment: versionRequest.comment
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'DocumentVersionCreated',
      aggregateId: versionId,
      aggregateType: 'DocumentVersion',
      version: 1,
      data: {
        version: documentVersion,
        document: documentData,
        createdBy: user.id
      },
      userId: user.id,
      organizationId: documentData.organizationId,
      tenantId: user.tenantId
    });

    // Send notification for major versions
    if (versionRequest.versionType === VersionType.MAJOR) {
      await notificationService.sendNotification({
        userId: user.id,
        type: 'DOCUMENT_VERSION_CREATED',
        title: 'Major document version created',
        message: `Version ${newVersionNumber} of "${documentData.name}" has been created.`,
        priority: 'normal',
        metadata: {
          documentId: versionRequest.documentId,
          documentName: documentData.name,
          versionId,
          versionNumber: newVersionNumber,
          versionType: versionRequest.versionType,
          organizationId: documentData.organizationId
        },
        organizationId: documentData.organizationId,
        projectId: documentData.projectId
      });
    }

    logger.info("Document version created successfully", {
      correlationId,
      versionId,
      documentId: versionRequest.documentId,
      versionNumber: newVersionNumber,
      versionType: versionRequest.versionType,
      changeCount: changes.length,
      userId: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: versionId,
        documentId: versionRequest.documentId,
        documentName: documentData.name,
        versionNumber: newVersionNumber,
        versionType: versionRequest.versionType,
        comment: versionRequest.comment,
        tags: versionRequest.tags,
        fileSize: documentVersion.fileSize,
        changes: changes,
        createdAt: now,
        message: "Document version created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create document version failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Restore document version handler
 */
export async function restoreDocumentVersion(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Restore document version started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = restoreVersionSchema.validate(body);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const restoreRequest: RestoreVersionRequest = value;

    // Get document and version
    const [document, version] = await Promise.all([
      db.readItem('documents', restoreRequest.documentId, restoreRequest.documentId),
      db.readItem('document-versions', restoreRequest.versionId, restoreRequest.versionId)
    ]);

    if (!document) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document not found" }
      }, request);
    }

    if (!version) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Version not found" }
      }, request);
    }

    const documentData = document as any;
    const versionData = version as any;

    // Verify version belongs to document
    if (versionData.documentId !== restoreRequest.documentId) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Version does not belong to the specified document" }
      }, request);
    }

    // Check edit permissions
    const hasEditPermission = await checkDocumentEditPermission(documentData, user);
    if (!hasEditPermission) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Insufficient permissions to restore document versions" }
      }, request);
    }

    const now = new Date().toISOString();

    // Create backup of current version if requested
    if (restoreRequest.createBackup) {
      await createDocumentVersion({
        ...request,
        json: async () => ({
          documentId: restoreRequest.documentId,
          versionType: VersionType.AUTO,
          comment: `Backup before restoring to version ${versionData.versionNumber}`,
          tags: ['backup', 'pre-restore'],
          isMinor: true
        })
      } as any, context);
    }

    // Restore version content to document
    await restoreVersionContent(documentData, versionData);

    // Create new version record for the restoration
    const restorationVersionId = uuidv4();
    const currentVersionNumber = await getCurrentVersionNumber(restoreRequest.documentId);
    const newVersionNumber = calculateNextVersionNumber(currentVersionNumber, VersionType.MINOR);

    const restorationVersion: DocumentVersion = {
      id: restorationVersionId,
      documentId: restoreRequest.documentId,
      versionNumber: newVersionNumber,
      versionType: VersionType.MINOR,
      status: VersionStatus.ACTIVE,
      comment: restoreRequest.comment || `Restored from version ${versionData.versionNumber}`,
      tags: ['restored'],
      fileSize: versionData.fileSize,
      contentHash: versionData.contentHash,
      blobName: documentData.blobName, // Current document blob
      createdBy: user.id,
      createdAt: now,
      metadata: {
        changes: [`Restored from version ${versionData.versionNumber}`],
        previousVersionId: documentData.currentVersionId,
        isRestored: true,
        restoredFrom: restoreRequest.versionId
      }
    };

    await db.createItem('document-versions', restorationVersion);

    // Update document
    const updatedDocument = {
      ...documentData,
      currentVersionId: restorationVersionId,
      currentVersionNumber: newVersionNumber,
      versionCount: (documentData.versionCount || 0) + 1,
      updatedAt: now,
      updatedBy: user.id
    };

    await db.updateItem('documents', updatedDocument);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "document_version_restored",
      userId: user.id,
      organizationId: documentData.organizationId,
      projectId: documentData.projectId,
      documentId: restoreRequest.documentId,
      timestamp: now,
      details: {
        restoredVersionId: restoreRequest.versionId,
        restoredVersionNumber: versionData.versionNumber,
        newVersionId: restorationVersionId,
        newVersionNumber,
        documentName: documentData.name,
        comment: restoreRequest.comment,
        backupCreated: restoreRequest.createBackup
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'DocumentVersionRestored',
      aggregateId: restorationVersionId,
      aggregateType: 'DocumentVersion',
      version: 1,
      data: {
        document: documentData,
        restoredVersion: versionData,
        newVersion: restorationVersion,
        restoredBy: user.id
      },
      userId: user.id,
      organizationId: documentData.organizationId,
      tenantId: user.tenantId
    });

    // Send notification
    await notificationService.sendNotification({
      userId: user.id,
      type: 'DOCUMENT_VERSION_RESTORED',
      title: 'Document version restored',
      message: `Document "${documentData.name}" has been restored to version ${versionData.versionNumber}.`,
      priority: 'normal',
      metadata: {
        documentId: restoreRequest.documentId,
        documentName: documentData.name,
        restoredVersionId: restoreRequest.versionId,
        restoredVersionNumber: versionData.versionNumber,
        newVersionId: restorationVersionId,
        newVersionNumber,
        organizationId: documentData.organizationId
      },
      organizationId: documentData.organizationId,
      projectId: documentData.projectId
    });

    logger.info("Document version restored successfully", {
      correlationId,
      documentId: restoreRequest.documentId,
      restoredVersionId: restoreRequest.versionId,
      restoredVersionNumber: versionData.versionNumber,
      newVersionId: restorationVersionId,
      newVersionNumber,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        documentId: restoreRequest.documentId,
        documentName: documentData.name,
        restoredVersionId: restoreRequest.versionId,
        restoredVersionNumber: versionData.versionNumber,
        newVersionId: restorationVersionId,
        newVersionNumber,
        backupCreated: restoreRequest.createBackup,
        message: "Document version restored successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Restore document version failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkDocumentEditPermission(document: any, user: any): Promise<boolean> {
  // Document owner has edit permission
  if (document.createdBy === user.id) {
    return true;
  }

  // Check organization membership
  if (document.organizationId === user.tenantId) {
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, document.organizationId, 'ACTIVE']);
    
    if (memberships.length > 0) {
      const membership = memberships[0] as any;
      return membership.role === 'OWNER' || membership.role === 'ADMIN' || membership.role === 'MEMBER';
    }
  }

  return false;
}

async function getCurrentVersionNumber(documentId: string): Promise<string> {
  try {
    const versionQuery = 'SELECT * FROM c WHERE c.documentId = @docId ORDER BY c.createdAt DESC';
    const versions = await db.queryItems('document-versions', versionQuery, [documentId]);
    
    if (versions.length === 0) {
      return '0.0.0';
    }

    return (versions[0] as any).versionNumber;
  } catch (error) {
    logger.error('Failed to get current version number', { error, documentId });
    return '0.0.0';
  }
}

function calculateNextVersionNumber(currentVersion: string, versionType: VersionType): string {
  const [major, minor, patch] = currentVersion.split('.').map(Number);

  switch (versionType) {
    case VersionType.MAJOR:
      return `${major + 1}.0.0`;
    case VersionType.MINOR:
      return `${major}.${minor + 1}.0`;
    case VersionType.PATCH:
      return `${major}.${minor}.${patch + 1}`;
    case VersionType.AUTO:
    default:
      return `${major}.${minor}.${patch + 1}`;
  }
}

async function createVersionBlob(document: any, versionId: string): Promise<string> {
  try {
    const blobServiceClient = new BlobServiceClient(
      process.env.AZURE_STORAGE_CONNECTION_STRING || ""
    );
    const containerClient = blobServiceClient.getContainerClient(
      process.env.DOCUMENT_CONTAINER || "documents"
    );

    // Copy current document blob to version blob
    const sourceBlobClient = containerClient.getBlobClient(document.blobName);
    const versionBlobName = `versions/${document.organizationId}/${document.id}/${versionId}`;
    const versionBlobClient = containerClient.getBlobClient(versionBlobName);

    await versionBlobClient.beginCopyFromURL(sourceBlobClient.url);

    return versionBlobName;
  } catch (error) {
    logger.error('Failed to create version blob', { error, documentId: document.id, versionId });
    throw error;
  }
}

async function calculateContentHash(blobName: string): Promise<string> {
  // Simplified hash calculation - in production, use proper content hashing
  return `hash_${Date.now()}_${Math.random().toString(36).substring(7)}`;
}

async function detectDocumentChanges(documentId: string, document: any): Promise<string[]> {
  // Simplified change detection - in production, implement proper diff analysis
  return [
    'Content updated',
    'Metadata modified',
    'File size changed'
  ];
}

async function getPreviousVersionId(documentId: string): Promise<string | undefined> {
  try {
    const versionQuery = 'SELECT * FROM c WHERE c.documentId = @docId ORDER BY c.createdAt DESC OFFSET 0 LIMIT 1';
    const versions = await db.queryItems('document-versions', versionQuery, [documentId]);
    
    return versions.length > 0 ? (versions[0] as any).id : undefined;
  } catch (error) {
    logger.error('Failed to get previous version ID', { error, documentId });
    return undefined;
  }
}

async function restoreVersionContent(document: any, version: any): Promise<void> {
  try {
    const blobServiceClient = new BlobServiceClient(
      process.env.AZURE_STORAGE_CONNECTION_STRING || ""
    );
    const containerClient = blobServiceClient.getContainerClient(
      process.env.DOCUMENT_CONTAINER || "documents"
    );

    // Copy version blob back to current document blob
    const versionBlobClient = containerClient.getBlobClient(version.blobName);
    const documentBlobClient = containerClient.getBlobClient(document.blobName);

    await documentBlobClient.beginCopyFromURL(versionBlobClient.url);

    logger.info('Version content restored successfully', {
      documentId: document.id,
      versionId: version.id,
      versionNumber: version.versionNumber
    });
  } catch (error) {
    logger.error('Failed to restore version content', { 
      error, 
      documentId: document.id, 
      versionId: version.id 
    });
    throw error;
  }
}

// Register functions
app.http('document-version-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'documents/{documentId}/versions',
  handler: createDocumentVersion
});

app.http('document-version-restore', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'documents/{documentId}/versions/{versionId}/restore',
  handler: restoreDocumentVersion
});

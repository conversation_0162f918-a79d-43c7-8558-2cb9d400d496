/**
 * Queue Storage Handlers for Azure Functions
 * Handles queue-based message processing for async operations
 */

import { InvocationContext, app } from '@azure/functions';
import { QueueServiceClient, QueueClient } from '@azure/storage-queue';
import { logger } from '../shared/utils/logger';
import { db } from '../shared/services/database';
import { publishEvent, EventType } from './event-grid-handlers';

// Queue clients
let queueServiceClient: QueueServiceClient | null = null;
const queueClients: Map<string, QueueClient> = new Map();

/**
 * Initialize queue service client
 */
function getQueueServiceClient(): QueueServiceClient {
  if (!queueServiceClient) {
    queueServiceClient = new QueueServiceClient(
      process.env.AZURE_STORAGE_CONNECTION_STRING || ""
    );
  }
  return queueServiceClient;
}

/**
 * Get queue client for specific queue
 */
function getQueueClient(queueName: string): QueueClient {
  if (!queueClients.has(queueName)) {
    const serviceClient = getQueueServiceClient();
    queueClients.set(queueName, serviceClient.getQueueClient(queueName));
  }
  return queueClients.get(queueName)!;
}

/**
 * Document processing queue handler
 * Processes documents asynchronously
 */
async function documentProcessingQueueHandler(queueItem: unknown, context: InvocationContext): Promise<void> {
  logger.info('Document processing queue handler triggered', {
    queueItem,
    messageId: context.triggerMetadata?.id
  });

  try {
    const message = typeof queueItem === 'string' ? JSON.parse(queueItem) : queueItem;
    const { documentId, processingType, priority = 'normal' } = message as any;

    if (!documentId || !processingType) {
      throw new Error('Invalid queue message: missing documentId or processingType');
    }

    // Get document from database
    const documents = await db.queryItems<any>('documents',
      'SELECT * FROM c WHERE c.id = @documentId',
      [documentId]
    );

    if (documents.length === 0) {
      throw new Error(`Document not found: ${documentId}`);
    }

    const document = documents[0];

    // Update document status
    await db.updateItem('documents', {
      ...document,
      status: 'processing',
      processingStartedAt: new Date().toISOString(),
      processingType
    });

    // Process based on type
    let processingResult;
    switch (processingType) {
      case 'ai-analysis':
        processingResult = await processAIAnalysis(document);
        break;
      case 'text-extraction':
        processingResult = await processTextExtraction(document);
        break;
      case 'image-analysis':
        processingResult = await processImageAnalysis(document);
        break;
      case 'pdf-processing':
        processingResult = await processPDFDocument(document);
        break;
      default:
        processingResult = await processGenericDocument(document);
    }

    // Update document with results
    await db.updateItem('documents', {
      ...document,
      status: 'processed',
      processedAt: new Date().toISOString(),
      processingResult,
      extractedText: processingResult.extractedText || document.extractedText
    });

    // Publish processing completed event
    await publishEvent(
      EventType.DOCUMENT_PROCESSED,
      `documents/${documentId}`,
      {
        documentId,
        processingType,
        result: processingResult,
        timestamp: new Date().toISOString()
      }
    );

    logger.info('Document processing completed', {
      documentId,
      processingType,
      resultSize: JSON.stringify(processingResult).length
    });

  } catch (error) {
    logger.error('Document processing queue handler failed', {
      queueItem,
      error: error instanceof Error ? error.message : String(error)
    });

    // Handle failed processing
    if (typeof queueItem === 'object' && queueItem !== null && 'documentId' in queueItem) {
      const documentId = (queueItem as any).documentId;
      try {
        const documents = await db.queryItems<any>('documents',
          'SELECT * FROM c WHERE c.id = @documentId',
          [documentId]
        );

        if (documents.length > 0) {
          const document = documents[0];
          await db.updateItem('documents', {
            ...document,
            status: 'processing_failed',
            error: error instanceof Error ? error.message : String(error),
            failedAt: new Date().toISOString()
          });
        }
      } catch (updateError) {
        logger.error('Failed to update document status after processing failure', { updateError });
      }
    }
  }
}

/**
 * Notification queue handler
 * Sends notifications asynchronously
 */
async function notificationQueueHandler(queueItem: unknown, context: InvocationContext): Promise<void> {
  logger.info('Notification queue handler triggered', {
    queueItem,
    messageId: context.triggerMetadata?.id
  });

  try {
    const message = typeof queueItem === 'string' ? JSON.parse(queueItem) : queueItem;
    const {
      userId,
      type,
      title,
      message: notificationMessage,
      data = {},
      channels = ['in-app']
    } = message as any;

    if (!userId || !type || !title || !notificationMessage) {
      throw new Error('Invalid notification message: missing required fields');
    }

    // Create notification record
    const notificationId = `notif-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const notification = {
      id: notificationId,
      userId,
      type,
      title,
      message: notificationMessage,
      data,
      channels,
      status: 'pending',
      createdAt: new Date().toISOString()
    };

    await db.createItem('notifications', notification);

    // Send notification through specified channels
    const results = [];
    for (const channel of channels) {
      try {
        let result;
        switch (channel) {
          case 'email':
            result = await sendEmailNotification(notification);
            break;
          case 'push':
            result = await sendPushNotification(notification);
            break;
          case 'sms':
            result = await sendSMSNotification(notification);
            break;
          case 'in-app':
          default:
            result = await sendInAppNotification(notification);
            break;
        }
        results.push({ channel, success: true, result });
      } catch (channelError) {
        results.push({
          channel,
          success: false,
          error: channelError instanceof Error ? channelError.message : String(channelError)
        });
      }
    }

    // Update notification status
    const allSuccessful = results.every(r => r.success);
    await db.updateItem('notifications', {
      ...notification,
      status: allSuccessful ? 'sent' : 'partial_failure',
      sentAt: new Date().toISOString(),
      deliveryResults: results
    });

    // Publish notification sent event
    await publishEvent(
      EventType.NOTIFICATION_SENT,
      `notifications/${notificationId}`,
      {
        notificationId,
        userId,
        type,
        channels,
        results,
        timestamp: new Date().toISOString()
      }
    );

    logger.info('Notification processing completed', {
      notificationId,
      userId,
      type,
      successfulChannels: results.filter(r => r.success).length
    });

  } catch (error) {
    logger.error('Notification queue handler failed', {
      queueItem,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Email generation queue handler
 * Generates and sends emails asynchronously
 */
async function emailQueueHandler(queueItem: unknown, context: InvocationContext): Promise<void> {
  logger.info('Email queue handler triggered', {
    queueItem,
    messageId: context.triggerMetadata?.id
  });

  try {
    const message = typeof queueItem === 'string' ? JSON.parse(queueItem) : queueItem;
    const {
      to,
      subject,
      template,
      templateData = {},
      priority = 'normal',
      scheduledFor
    } = message as any;

    if (!to || !subject || !template) {
      throw new Error('Invalid email message: missing required fields');
    }

    // Check if email is scheduled for future
    if (scheduledFor && new Date(scheduledFor) > new Date()) {
      logger.info('Email is scheduled for future, re-queuing', {
        to,
        subject,
        scheduledFor
      });

      // Re-queue with delay
      const delayMs = new Date(scheduledFor).getTime() - Date.now();
      await queueEmailWithDelay(message, delayMs);
      return;
    }

    // Generate email content from template
    const emailContent = await generateEmailFromTemplate(template, templateData);

    // Send email
    const emailResult = await sendEmail({
      to,
      subject,
      html: emailContent.html,
      text: emailContent.text,
      priority
    });

    // Log email sent
    await db.createItem('email-logs', {
      id: `email-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      to,
      subject,
      template,
      status: emailResult.success ? 'sent' : 'failed',
      sentAt: new Date().toISOString(),
      messageId: emailResult.messageId,
      error: emailResult.error
    });

    logger.info('Email sent successfully', {
      to,
      subject,
      template,
      messageId: emailResult.messageId
    });

  } catch (error) {
    logger.error('Email queue handler failed', {
      queueItem,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Dead letter queue handler
 * Handles messages that failed processing multiple times
 */
async function deadLetterQueueHandler(queueItem: unknown, context: InvocationContext): Promise<void> {
  logger.warn('Dead letter queue handler triggered', {
    queueItem,
    messageId: context.triggerMetadata?.id
  });

  try {
    const message = typeof queueItem === 'string' ? JSON.parse(queueItem) : queueItem;

    // Log dead letter message
    await db.createItem('dead-letter-messages', {
      id: `dead-letter-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      originalMessage: message,
      receivedAt: new Date().toISOString(),
      source: context.triggerMetadata?.queueName || 'unknown'
    });

    // Publish alert for dead letter message
    await publishEvent(
      EventType.PERFORMANCE_ALERT,
      'queues/dead-letter',
      {
        alertType: 'dead_letter_message',
        severity: 'medium',
        message,
        source: context.triggerMetadata?.queueName,
        timestamp: new Date().toISOString()
      }
    );

    logger.warn('Dead letter message logged', {
      source: context.triggerMetadata?.queueName
    });

  } catch (error) {
    logger.error('Dead letter queue handler failed', {
      queueItem,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

// Placeholder functions for processing operations
async function processAIAnalysis(document: any): Promise<any> {
  // Implement AI analysis logic
  return { type: 'ai-analysis', confidence: 0.95, entities: [] };
}

async function processTextExtraction(document: any): Promise<any> {
  // Implement text extraction logic
  return { type: 'text-extraction', extractedText: 'Sample extracted text', wordCount: 100 };
}

async function processImageAnalysis(document: any): Promise<any> {
  // Implement image analysis logic
  return { type: 'image-analysis', objects: [], text: '', confidence: 0.9 };
}

async function processPDFDocument(document: any): Promise<any> {
  // Implement PDF processing logic
  return { type: 'pdf-processing', pages: 5, extractedText: 'PDF content', metadata: {} };
}

async function processGenericDocument(document: any): Promise<any> {
  // Implement generic document processing
  return { type: 'generic', processed: true, timestamp: new Date().toISOString() };
}

// Placeholder notification functions
async function sendEmailNotification(notification: any): Promise<any> {
  return { success: true, messageId: 'email-123' };
}

async function sendPushNotification(notification: any): Promise<any> {
  return { success: true, messageId: 'push-123' };
}

async function sendSMSNotification(notification: any): Promise<any> {
  return { success: true, messageId: 'sms-123' };
}

async function sendInAppNotification(notification: any): Promise<any> {
  return { success: true, messageId: 'inapp-123' };
}

async function generateEmailFromTemplate(template: string, data: any): Promise<any> {
  return { html: '<p>Email content</p>', text: 'Email content' };
}

async function sendEmail(emailData: any): Promise<any> {
  return { success: true, messageId: 'email-456' };
}

async function queueEmailWithDelay(message: any, delayMs: number): Promise<void> {
  // Implement delayed queuing logic
}

// Register queue triggers
app.storageQueue('documentProcessingQueue', {
  queueName: 'document-processing',
  connection: 'AzureWebJobsStorage',
  handler: documentProcessingQueueHandler
});

app.storageQueue('notificationQueue', {
  queueName: 'notifications',
  connection: 'AzureWebJobsStorage',
  handler: notificationQueueHandler
});

app.storageQueue('emailQueue', {
  queueName: 'emails',
  connection: 'AzureWebJobsStorage',
  handler: emailQueueHandler
});

app.storageQueue('deadLetterQueue', {
  queueName: 'dead-letter-queue',
  connection: 'AzureWebJobsStorage',
  handler: deadLetterQueueHandler
});

// Export queue utilities for use by other modules
export { getQueueClient, getQueueServiceClient };

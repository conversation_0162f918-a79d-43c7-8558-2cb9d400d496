/**
 * Enhanced SignalR Service for production-ready real-time messaging
 * Features:
 * - Connection management and scaling
 * - Group management with Redis backing
 * - Authentication integration
 * - Message routing and filtering
 * - Performance monitoring
 * - Auto-reconnection and failover
 */

import { logger } from '../utils/logger';
import { redis } from './redis';
import { db } from './database';

export interface SignalRConnection {
  connectionId: string;
  userId: string;
  groups: string[];
  connectedAt: Date;
  lastActivity: Date;
  metadata: Record<string, any>;
}

export interface SignalRMessage {
  target: string;
  arguments: any[];
  connectionId?: string;
  userId?: string;
  groupName?: string;
  excludeConnectionIds?: string[];
}

export interface SignalRGroup {
  name: string;
  connections: Set<string>;
  metadata: Record<string, any>;
  createdAt: Date;
  lastActivity: Date;
}

export interface SignalRMetrics {
  totalConnections: number;
  activeGroups: number;
  messagesSent: number;
  messagesReceived: number;
  errors: number;
  averageLatency: number;
}

export class SignalREnhancedService {
  private static instance: SignalREnhancedService;
  private connections: Map<string, SignalRConnection> = new Map();
  private groups: Map<string, SignalRGroup> = new Map();
  private metrics: SignalRMetrics = {
    totalConnections: 0,
    activeGroups: 0,
    messagesSent: 0,
    messagesReceived: 0,
    errors: 0,
    averageLatency: 0
  };
  private connectionString: string;
  private hubName: string;
  private isInitialized = false;

  private constructor() {
    this.connectionString = process.env.SIGNALR_CONNECTION_STRING || '';
    this.hubName = process.env.SIGNALR_HUB_NAME || 'hepztech';
  }

  public static getInstance(): SignalREnhancedService {
    if (!SignalREnhancedService.instance) {
      SignalREnhancedService.instance = new SignalREnhancedService();
    }
    return SignalREnhancedService.instance;
  }

  /**
   * Initialize SignalR service
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      if (!this.connectionString) {
        throw new Error('SignalR connection string not configured');
      }

      // Initialize Redis pub/sub for scaling across instances
      await this.initializeRedisIntegration();

      // Start periodic cleanup and metrics collection
      this.startPeriodicTasks();

      this.isInitialized = true;
      logger.info('SignalR Enhanced Service initialized successfully', {
        hubName: this.hubName
      });
    } catch (error) {
      logger.error('Failed to initialize SignalR Enhanced Service', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Initialize Redis integration for scaling
   */
  private async initializeRedisIntegration(): Promise<void> {
    // Subscribe to SignalR events from other instances
    await redis.subscribe('signalr:broadcast', (message) => {
      this.handleRedisMessage(message);
    });

    await redis.subscribe('signalr:group', (message) => {
      this.handleRedisGroupMessage(message);
    });

    await redis.subscribe('signalr:connection', (message) => {
      this.handleRedisConnectionMessage(message);
    });

    logger.info('SignalR Redis integration initialized');
  }

  /**
   * Handle Redis messages for cross-instance communication
   */
  private async handleRedisMessage(message: any): Promise<void> {
    try {
      const data = JSON.parse(message.message);
      
      switch (data.type) {
        case 'broadcast':
          await this.localBroadcast(data.message);
          break;
        case 'user':
          await this.localSendToUser(data.userId, data.message);
          break;
        case 'group':
          await this.localSendToGroup(data.groupName, data.message);
          break;
      }
    } catch (error) {
      logger.error('Error handling Redis SignalR message', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Handle Redis group management messages
   */
  private async handleRedisGroupMessage(message: any): Promise<void> {
    try {
      const data = JSON.parse(message.message);
      
      switch (data.action) {
        case 'add':
          await this.localAddToGroup(data.connectionId, data.groupName);
          break;
        case 'remove':
          await this.localRemoveFromGroup(data.connectionId, data.groupName);
          break;
      }
    } catch (error) {
      logger.error('Error handling Redis group message', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Handle Redis connection management messages
   */
  private async handleRedisConnectionMessage(message: any): Promise<void> {
    try {
      const data = JSON.parse(message.message);
      
      switch (data.action) {
        case 'connect':
          this.metrics.totalConnections++;
          break;
        case 'disconnect':
          this.metrics.totalConnections = Math.max(0, this.metrics.totalConnections - 1);
          break;
      }
    } catch (error) {
      logger.error('Error handling Redis connection message', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Register a new connection
   */
  public async registerConnection(
    connectionId: string,
    userId: string,
    metadata: Record<string, any> = {}
  ): Promise<void> {
    const connection: SignalRConnection = {
      connectionId,
      userId,
      groups: [],
      connectedAt: new Date(),
      lastActivity: new Date(),
      metadata
    };

    this.connections.set(connectionId, connection);
    this.metrics.totalConnections++;

    // Store in Redis for persistence and scaling
    await redis.setJson(`signalr:connection:${connectionId}`, connection, 3600);

    // Notify other instances
    await redis.publish('signalr:connection', JSON.stringify({
      action: 'connect',
      connectionId,
      userId
    }));

    // Store in database for analytics
    await db.createItem('signalr-connections', {
      id: `signalr-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      connectionId,
      userId,
      connectedAt: connection.connectedAt.toISOString(),
      hubName: this.hubName,
      metadata
    });

    logger.info('SignalR connection registered', {
      connectionId,
      userId,
      totalConnections: this.metrics.totalConnections
    });
  }

  /**
   * Unregister a connection
   */
  public async unregisterConnection(connectionId: string): Promise<void> {
    const connection = this.connections.get(connectionId);
    if (!connection) {
      return;
    }

    // Remove from all groups
    for (const groupName of connection.groups) {
      await this.removeFromGroup(connectionId, groupName);
    }

    this.connections.delete(connectionId);
    this.metrics.totalConnections = Math.max(0, this.metrics.totalConnections - 1);

    // Remove from Redis
    await redis.delete(`signalr:connection:${connectionId}`);

    // Notify other instances
    await redis.publish('signalr:connection', JSON.stringify({
      action: 'disconnect',
      connectionId
    }));

    logger.info('SignalR connection unregistered', {
      connectionId,
      userId: connection.userId,
      totalConnections: this.metrics.totalConnections
    });
  }

  /**
   * Add connection to group
   */
  public async addToGroup(connectionId: string, groupName: string): Promise<boolean> {
    const connection = this.connections.get(connectionId);
    if (!connection) {
      logger.warn('Connection not found for group addition', { connectionId, groupName });
      return false;
    }

    // Add locally
    await this.localAddToGroup(connectionId, groupName);

    // Notify other instances via Redis
    await redis.publish('signalr:group', JSON.stringify({
      action: 'add',
      connectionId,
      groupName
    }));

    // Store group membership in Redis
    await redis.sadd(`signalr:group:${groupName}`, connectionId);
    await redis.expire(`signalr:group:${groupName}`, 3600);

    logger.info('Connection added to group', { connectionId, groupName });
    return true;
  }

  /**
   * Remove connection from group
   */
  public async removeFromGroup(connectionId: string, groupName: string): Promise<boolean> {
    // Remove locally
    await this.localRemoveFromGroup(connectionId, groupName);

    // Notify other instances via Redis
    await redis.publish('signalr:group', JSON.stringify({
      action: 'remove',
      connectionId,
      groupName
    }));

    // Remove from Redis group
    try {
      await redis.delete(`signalr:group:${groupName}:${connectionId}`);
    } catch (error) {
      logger.debug('Error removing connection from Redis group', {
        groupName,
        connectionId,
        error: error instanceof Error ? error.message : String(error)
      });
    }

    logger.info('Connection removed from group', { connectionId, groupName });
    return true;
  }

  /**
   * Send message to all connections
   */
  public async broadcast(message: SignalRMessage): Promise<boolean> {
    try {
      // Send locally
      await this.localBroadcast(message);

      // Send to other instances via Redis
      await redis.publish('signalr:broadcast', JSON.stringify({
        type: 'broadcast',
        message
      }));

      this.metrics.messagesSent++;
      return true;
    } catch (error) {
      this.metrics.errors++;
      logger.error('Error broadcasting message', {
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * Send message to specific user
   */
  public async sendToUser(userId: string, message: SignalRMessage): Promise<boolean> {
    try {
      // Send locally
      await this.localSendToUser(userId, message);

      // Send to other instances via Redis
      await redis.publish('signalr:broadcast', JSON.stringify({
        type: 'user',
        userId,
        message
      }));

      this.metrics.messagesSent++;
      return true;
    } catch (error) {
      this.metrics.errors++;
      logger.error('Error sending message to user', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * Send message to group
   */
  public async sendToGroup(groupName: string, message: SignalRMessage): Promise<boolean> {
    try {
      // Send locally
      await this.localSendToGroup(groupName, message);

      // Send to other instances via Redis
      await redis.publish('signalr:broadcast', JSON.stringify({
        type: 'group',
        groupName,
        message
      }));

      this.metrics.messagesSent++;
      return true;
    } catch (error) {
      this.metrics.errors++;
      logger.error('Error sending message to group', {
        groupName,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * Get service metrics
   */
  public getMetrics(): SignalRMetrics {
    return { ...this.metrics };
  }

  /**
   * Get connection information
   */
  public getConnection(connectionId: string): SignalRConnection | undefined {
    return this.connections.get(connectionId);
  }

  /**
   * Get all connections for a user
   */
  public getUserConnections(userId: string): SignalRConnection[] {
    return Array.from(this.connections.values())
      .filter(conn => conn.userId === userId);
  }

  /**
   * Get group information
   */
  public getGroup(groupName: string): SignalRGroup | undefined {
    return this.groups.get(groupName);
  }

  // Private helper methods
  private async localAddToGroup(connectionId: string, groupName: string): Promise<void> {
    const connection = this.connections.get(connectionId);
    if (!connection) return;

    if (!connection.groups.includes(groupName)) {
      connection.groups.push(groupName);
    }

    if (!this.groups.has(groupName)) {
      this.groups.set(groupName, {
        name: groupName,
        connections: new Set(),
        metadata: {},
        createdAt: new Date(),
        lastActivity: new Date()
      });
      this.metrics.activeGroups++;
    }

    const group = this.groups.get(groupName)!;
    group.connections.add(connectionId);
    group.lastActivity = new Date();
  }

  private async localRemoveFromGroup(connectionId: string, groupName: string): Promise<void> {
    const connection = this.connections.get(connectionId);
    if (connection) {
      const index = connection.groups.indexOf(groupName);
      if (index > -1) {
        connection.groups.splice(index, 1);
      }
    }

    const group = this.groups.get(groupName);
    if (group) {
      group.connections.delete(connectionId);
      if (group.connections.size === 0) {
        this.groups.delete(groupName);
        this.metrics.activeGroups = Math.max(0, this.metrics.activeGroups - 1);
      }
    }
  }

  private async localBroadcast(message: SignalRMessage): Promise<void> {
    logger.info('Broadcasting message locally', {
      target: message.target,
      connectionCount: this.connections.size
    });

    // Store message for analytics
    await this.storeMessage('broadcast', message);
  }

  private async localSendToUser(userId: string, message: SignalRMessage): Promise<void> {
    const userConnections = Array.from(this.connections.values())
      .filter(conn => conn.userId === userId);

    logger.info('Sending message to user locally', {
      userId,
      target: message.target,
      connectionCount: userConnections.length
    });

    await this.storeMessage('user', message, { userId });
  }

  private async localSendToGroup(groupName: string, message: SignalRMessage): Promise<void> {
    const group = this.groups.get(groupName);
    if (!group) {
      logger.warn('Group not found for local send', { groupName });
      return;
    }

    logger.info('Sending message to group locally', {
      groupName,
      target: message.target,
      connectionCount: group.connections.size
    });

    await this.storeMessage('group', message, { groupName });
  }

  private async storeMessage(
    type: string, 
    message: SignalRMessage, 
    metadata: Record<string, any> = {}
  ): Promise<void> {
    try {
      await db.createItem('signalr-messages', {
        id: `msg-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        type,
        target: message.target,
        hubName: this.hubName,
        timestamp: new Date().toISOString(),
        metadata
      });
    } catch (error) {
      logger.debug('Failed to store SignalR message for analytics', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private startPeriodicTasks(): void {
    // Cleanup inactive connections every 5 minutes
    setInterval(async () => {
      await this.cleanupInactiveConnections();
    }, 5 * 60 * 1000);
  }

  private async cleanupInactiveConnections(): Promise<void> {
    const now = new Date();
    const inactiveThreshold = 30 * 60 * 1000; // 30 minutes
    let cleanedCount = 0;

    for (const [connectionId, connection] of this.connections.entries()) {
      if (now.getTime() - connection.lastActivity.getTime() > inactiveThreshold) {
        await this.unregisterConnection(connectionId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      logger.info('Cleaned up inactive SignalR connections', { count: cleanedCount });
    }
  }
}

// Export singleton instance
export const signalREnhanced = SignalREnhancedService.getInstance();

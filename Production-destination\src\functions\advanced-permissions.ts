/**
 * Advanced Permissions Function
 * Handles sophisticated role-based access control and fine-grained permissions
 * Migrated from old-arch/src/shared/middleware/authorization.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';
// import { eventService } from '../shared/services/event'; // Unused for now

// Advanced permission types and enums
enum SystemRole {
  SYSTEM_ADMIN = 'SYSTEM_ADMIN',
  PLATFORM_ADMIN = 'PLATFORM_ADMIN',
  SUPPORT_ADMIN = 'SUPPORT_ADMIN',
  SECURITY_ADMIN = 'SECURITY_ADMIN'
}

enum OrganizationRole {
  OWNER = 'OWNER',
  ADMIN = 'ADMIN',
  MANAGER = 'MANAGER',
  MEMBER = 'MEMBER',
  VIEWER = 'VIEWER',
  GUEST = 'GUEST'
}

enum PermissionAction {
  CREATE = 'CREATE',
  READ = 'READ',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  SHARE = 'SHARE',
  MANAGE = 'MANAGE',
  APPROVE = 'APPROVE',
  EXECUTE = 'EXECUTE',
  ADMIN = 'ADMIN'
}

enum ResourceType {
  DOCUMENT = 'DOCUMENT',
  WORKFLOW = 'WORKFLOW',
  PROJECT = 'PROJECT',
  ORGANIZATION = 'ORGANIZATION',
  USER = 'USER',
  TEMPLATE = 'TEMPLATE',
  INTEGRATION = 'INTEGRATION',
  REPORT = 'REPORT'
}

// Validation schemas
const createRoleSchema = Joi.object({
  name: Joi.string().min(2).max(50).required(),
  description: Joi.string().max(500).optional(),
  organizationId: Joi.string().uuid().required(),
  permissions: Joi.array().items(Joi.object({
    resource: Joi.string().valid(...Object.values(ResourceType)).required(),
    actions: Joi.array().items(Joi.string().valid(...Object.values(PermissionAction))).min(1).required(),
    conditions: Joi.object({
      ownedOnly: Joi.boolean().default(false),
      departmentOnly: Joi.boolean().default(false),
      projectOnly: Joi.boolean().default(false),
      timeRestrictions: Joi.object().optional(),
      ipRestrictions: Joi.array().items(Joi.string().ip()).optional()
    }).optional()
  })).min(1).required(),
  hierarchy: Joi.object({
    level: Joi.number().min(1).max(10).default(5),
    inheritsFrom: Joi.string().uuid().optional(),
    canDelegate: Joi.boolean().default(false)
  }).optional(),
  isActive: Joi.boolean().default(true)
});

const checkPermissionSchema = Joi.object({
  userId: Joi.string().uuid().required(),
  resource: Joi.string().valid(...Object.values(ResourceType)).required(),
  action: Joi.string().valid(...Object.values(PermissionAction)).required(),
  context: Joi.object({
    organizationId: Joi.string().uuid().optional(),
    projectId: Joi.string().uuid().optional(),
    resourceId: Joi.string().uuid().optional(),
    departmentId: Joi.string().uuid().optional(),
    metadata: Joi.object().optional()
  }).optional()
});

interface AdvancedRole {
  id: string;
  name: string;
  description?: string;
  organizationId: string;
  permissions: Array<{
    resource: ResourceType;
    actions: PermissionAction[];
    conditions?: {
      ownedOnly?: boolean;
      departmentOnly?: boolean;
      projectOnly?: boolean;
      timeRestrictions?: any;
      ipRestrictions?: string[];
    };
  }>;
  hierarchy: {
    level: number;
    inheritsFrom?: string;
    canDelegate: boolean;
  };
  statistics: {
    userCount: number;
    lastUsed?: string;
    createdCount: number;
  };
  isActive: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  tenantId: string;
}

interface PermissionCheck {
  userId: string;
  resource: ResourceType;
  action: PermissionAction;
  context?: {
    organizationId?: string;
    projectId?: string;
    resourceId?: string;
    departmentId?: string;
    metadata?: any;
  };
}

/**
 * Create advanced role handler
 */
export async function createAdvancedRole(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;
  logger.info("Create advanced role started", { correlationId });

  try {
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    const body = await request.json();
    const { error, value } = createRoleSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const roleRequest = value;

    // Check admin access
    const hasAccess = await checkAdvancedPermissionAccess(user, roleRequest.organizationId);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to role management" }
      }, request);
    }

    // Create advanced role
    const roleId = uuidv4();
    const now = new Date().toISOString();

    const advancedRole: AdvancedRole = {
      id: roleId,
      name: roleRequest.name,
      description: roleRequest.description,
      organizationId: roleRequest.organizationId,
      permissions: roleRequest.permissions,
      hierarchy: {
        level: 5,
        canDelegate: false,
        ...roleRequest.hierarchy
      },
      statistics: {
        userCount: 0,
        createdCount: 0
      },
      isActive: roleRequest.isActive,
      createdBy: user.id,
      createdAt: now,
      updatedAt: now,
      tenantId: user.tenantId || user.id
    };

    await db.createItem('advanced-roles', advancedRole);

    // Cache role for quick access
    await cacheAdvancedRole(advancedRole);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "advanced_role_created",
      userId: user.id,
      organizationId: roleRequest.organizationId,
      timestamp: now,
      details: {
        roleId,
        roleName: roleRequest.name,
        permissionCount: roleRequest.permissions.length,
        hierarchyLevel: advancedRole.hierarchy.level
      },
      tenantId: user.tenantId
    });

    logger.info("Advanced role created successfully", {
      correlationId,
      roleId,
      roleName: roleRequest.name,
      permissionCount: roleRequest.permissions.length,
      createdBy: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        roleId,
        name: roleRequest.name,
        permissionCount: roleRequest.permissions.length,
        hierarchyLevel: advancedRole.hierarchy.level,
        createdAt: now,
        message: "Advanced role created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create advanced role failed", { correlationId, error: errorMessage });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Check advanced permission handler
 */
export async function checkAdvancedPermission(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;
  logger.info("Check advanced permission started", { correlationId });

  try {
    const body = await request.json();
    const { error, value } = checkPermissionSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const permissionCheck: PermissionCheck = value;

    // Perform comprehensive permission check
    const permissionResult = await performAdvancedPermissionCheck(permissionCheck);

    logger.info("Advanced permission checked", {
      correlationId,
      userId: permissionCheck.userId,
      resource: permissionCheck.resource,
      action: permissionCheck.action,
      allowed: permissionResult.allowed,
      reason: permissionResult.reason
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        allowed: permissionResult.allowed,
        reason: permissionResult.reason,
        roles: permissionResult.roles,
        conditions: permissionResult.conditions,
        checkedAt: new Date().toISOString()
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Check advanced permission failed", { correlationId, error: errorMessage });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkAdvancedPermissionAccess(user: any, organizationId: string): Promise<boolean> {
  try {
    // Check if user has system admin role
    if (user.systemRoles?.includes(SystemRole.SYSTEM_ADMIN)) {
      return true;
    }

    // Check organization admin access
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, user.id, 'ACTIVE']);

    if (memberships.length > 0) {
      const membership = memberships[0] as any;
      return membership.role === OrganizationRole.OWNER || membership.role === OrganizationRole.ADMIN;
    }

    return false;
  } catch (error) {
    logger.error('Failed to check advanced permission access', { error, userId: user.id, organizationId });
    return false;
  }
}

async function cacheAdvancedRole(role: AdvancedRole): Promise<void> {
  try {
    const cacheKey = `advanced_role:${role.id}`;
    await redis.setex(cacheKey, 3600, JSON.stringify(role)); // 1 hour cache

    // Cache in organization role list
    const orgRolesKey = `org_advanced_roles:${role.organizationId}`;
    await redis.sadd(orgRolesKey, role.id);
    await redis.expire(orgRolesKey, 3600);

  } catch (error) {
    logger.error('Failed to cache advanced role', { error, roleId: role.id });
  }
}

async function performAdvancedPermissionCheck(permissionCheck: PermissionCheck): Promise<any> {
  try {
    // Get user's roles and permissions
    const userRoles = await getUserAdvancedRoles(permissionCheck.userId, permissionCheck.context?.organizationId);

    // Check system-level permissions first
    const systemPermission = await checkSystemPermissions(permissionCheck.userId, permissionCheck.resource, permissionCheck.action);
    if (systemPermission.allowed) {
      return {
        allowed: true,
        reason: 'System-level permission granted',
        roles: ['SYSTEM_ADMIN'],
        conditions: {}
      };
    }

    // Check role-based permissions
    for (const role of userRoles) {
      const rolePermission = await checkRolePermissions(role, permissionCheck);
      if (rolePermission.allowed) {
        return {
          allowed: true,
          reason: `Permission granted via role: ${role.name}`,
          roles: [role.name],
          conditions: rolePermission.conditions
        };
      }
    }

    // Check inherited permissions
    const inheritedPermission = await checkInheritedPermissions(userRoles, permissionCheck);
    if (inheritedPermission.allowed) {
      return inheritedPermission;
    }

    return {
      allowed: false,
      reason: 'No matching permissions found',
      roles: userRoles.map(r => r.name),
      conditions: {}
    };

  } catch (error) {
    logger.error('Failed to perform advanced permission check', { error, permissionCheck });
    return {
      allowed: false,
      reason: 'Permission check failed',
      roles: [],
      conditions: {}
    };
  }
}

async function getUserAdvancedRoles(userId: string, organizationId?: string): Promise<AdvancedRole[]> {
  try {
    // Try cache first
    const cacheKey = `user_advanced_roles:${userId}:${organizationId || 'global'}`;
    const cached = await redis.get(cacheKey);

    if (cached) {
      return JSON.parse(cached);
    }

    // Query user role assignments
    let query = 'SELECT * FROM c WHERE c.userId = @userId';
    const parameters = [userId];

    if (organizationId) {
      query += ' AND c.organizationId = @orgId';
      parameters.push(organizationId);
    }

    const roleAssignments = await db.queryItems('user-role-assignments', query, parameters);

    // Get role details
    const roles: AdvancedRole[] = [];
    for (const assignment of roleAssignments) {
      const assignmentData = assignment as any;
      const role = await db.readItem('advanced-roles', assignmentData.roleId, assignmentData.roleId);
      if (role && (role as any).isActive) {
        roles.push(role as AdvancedRole);
      }
    }

    // Cache for 15 minutes
    await redis.setex(cacheKey, 900, JSON.stringify(roles));

    return roles;

  } catch (error) {
    logger.error('Failed to get user advanced roles', { error, userId, organizationId });
    return [];
  }
}

async function checkSystemPermissions(userId: string, resource: ResourceType, action: PermissionAction): Promise<any> {
  try {
    // Get user system roles
    const userQuery = 'SELECT * FROM c WHERE c.id = @userId';
    const users = await db.queryItems('users', userQuery, [userId]);

    if (users.length === 0) {
      return { allowed: false, reason: 'User not found' };
    }

    const user = users[0] as any;
    const systemRoles = user.systemRoles || [];

    // System admins have all permissions
    if (systemRoles.includes(SystemRole.SYSTEM_ADMIN)) {
      return { allowed: true, reason: 'System admin access' };
    }

    // Platform admins have most permissions
    if (systemRoles.includes(SystemRole.PLATFORM_ADMIN)) {
      const restrictedActions = [PermissionAction.DELETE];
      if (!restrictedActions.includes(action)) {
        return { allowed: true, reason: 'Platform admin access' };
      }
    }

    return { allowed: false, reason: 'No system-level permissions' };

  } catch (error) {
    logger.error('Failed to check system permissions', { error, userId, resource, action });
    return { allowed: false, reason: 'System permission check failed' };
  }
}

async function checkRolePermissions(role: AdvancedRole, permissionCheck: PermissionCheck): Promise<any> {
  try {
    // Find matching permission in role
    const matchingPermission = role.permissions.find(p =>
      p.resource === permissionCheck.resource &&
      p.actions.includes(permissionCheck.action)
    );

    if (!matchingPermission) {
      return { allowed: false, reason: 'No matching permission in role' };
    }

    // Check conditions if present
    if (matchingPermission.conditions) {
      const conditionCheck = await evaluatePermissionConditions(
        matchingPermission.conditions,
        permissionCheck
      );

      if (!conditionCheck.allowed) {
        return conditionCheck;
      }
    }

    return {
      allowed: true,
      reason: `Permission granted via role: ${role.name}`,
      conditions: matchingPermission.conditions || {}
    };

  } catch (error) {
    logger.error('Failed to check role permissions', { error, roleId: role.id, permissionCheck });
    return { allowed: false, reason: 'Role permission check failed' };
  }
}

async function evaluatePermissionConditions(conditions: any, permissionCheck: PermissionCheck): Promise<any> {
  try {
    // Check ownership condition
    if (conditions.ownedOnly && permissionCheck.context?.resourceId) {
      const isOwner = await checkResourceOwnership(
        permissionCheck.userId,
        permissionCheck.context.resourceId,
        permissionCheck.resource
      );

      if (!isOwner) {
        return { allowed: false, reason: 'Resource not owned by user' };
      }
    }

    // Check department condition
    if (conditions.departmentOnly && permissionCheck.context?.departmentId) {
      const inDepartment = await checkDepartmentMembership(
        permissionCheck.userId,
        permissionCheck.context.departmentId
      );

      if (!inDepartment) {
        return { allowed: false, reason: 'User not in required department' };
      }
    }

    // Check time restrictions
    if (conditions.timeRestrictions) {
      const timeAllowed = checkTimeRestrictions(conditions.timeRestrictions);
      if (!timeAllowed) {
        return { allowed: false, reason: 'Access not allowed at this time' };
      }
    }

    // Check IP restrictions
    if (conditions.ipRestrictions && conditions.ipRestrictions.length > 0) {
      // In a real implementation, you'd get the client IP from the request
      const clientIP = '127.0.0.1'; // Mock IP
      if (!conditions.ipRestrictions.includes(clientIP)) {
        return { allowed: false, reason: 'Access not allowed from this IP' };
      }
    }

    return { allowed: true, reason: 'All conditions satisfied' };

  } catch (error) {
    logger.error('Failed to evaluate permission conditions', { error, conditions, permissionCheck });
    return { allowed: false, reason: 'Condition evaluation failed' };
  }
}

async function checkResourceOwnership(userId: string, resourceId: string, resourceType: ResourceType): Promise<boolean> {
  try {
    const collectionMap: { [key in ResourceType]: string } = {
      [ResourceType.DOCUMENT]: 'documents',
      [ResourceType.WORKFLOW]: 'workflows',
      [ResourceType.PROJECT]: 'projects',
      [ResourceType.ORGANIZATION]: 'organizations',
      [ResourceType.USER]: 'users',
      [ResourceType.TEMPLATE]: 'templates',
      [ResourceType.INTEGRATION]: 'integrations',
      [ResourceType.REPORT]: 'reports'
    };

    const collection = collectionMap[resourceType];
    const resource = await db.readItem(collection, resourceId, resourceId);

    return !!(resource && (resource as any).createdBy === userId);

  } catch (error) {
    logger.error('Failed to check resource ownership', { error, userId, resourceId, resourceType });
    return false;
  }
}

async function checkDepartmentMembership(userId: string, departmentId: string): Promise<boolean> {
  try {
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.departmentId = @deptId AND c.status = @status';
    const memberships = await db.queryItems('department-members', membershipQuery, [userId, departmentId, 'ACTIVE']);

    return memberships.length > 0;

  } catch (error) {
    logger.error('Failed to check department membership', { error, userId, departmentId });
    return false;
  }
}

function checkTimeRestrictions(timeRestrictions: any): boolean {
  try {
    const now = new Date();
    const currentHour = now.getHours();
    const currentDay = now.getDay();

    // Check allowed hours
    if (timeRestrictions.allowedHours && !timeRestrictions.allowedHours.includes(currentHour)) {
      return false;
    }

    // Check allowed days
    if (timeRestrictions.allowedDays && !timeRestrictions.allowedDays.includes(currentDay)) {
      return false;
    }

    return true;

  } catch (error) {
    logger.error('Failed to check time restrictions', { error, timeRestrictions });
    return false;
  }
}

async function checkInheritedPermissions(roles: AdvancedRole[], permissionCheck: PermissionCheck): Promise<any> {
  try {
    // Check if any role inherits permissions that would grant access
    for (const role of roles) {
      if (role.hierarchy.inheritsFrom) {
        const parentRole = await db.readItem('advanced-roles', role.hierarchy.inheritsFrom, role.hierarchy.inheritsFrom);
        if (parentRole) {
          const parentPermission = await checkRolePermissions(parentRole as AdvancedRole, permissionCheck);
          if (parentPermission.allowed) {
            return {
              allowed: true,
              reason: `Permission inherited from role: ${(parentRole as any).name}`,
              roles: [role.name, (parentRole as any).name],
              conditions: parentPermission.conditions
            };
          }
        }
      }
    }

    return { allowed: false, reason: 'No inherited permissions found' };

  } catch (error) {
    logger.error('Failed to check inherited permissions', { error, permissionCheck });
    return { allowed: false, reason: 'Inherited permission check failed' };
  }
}

// Register functions
app.http('advanced-role-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'management/advanced-roles',
  handler: createAdvancedRole
});

app.http('advanced-permission-check', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'permissions/check-advanced',
  handler: checkAdvancedPermission
});

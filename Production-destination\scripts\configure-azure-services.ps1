# Azure Services Configuration Script
# This script configures Service Bus topics, subscriptions, and verifies Event Grid setup

param(
    [string]$ResourceGroup = "docucontext",
    [string]$ServiceBusNamespace = "hepzbackend",
    [string]$EventGridTopic = "hepzeg"
)

Write-Host "🚀 Configuring Azure Services for Production-destination..." -ForegroundColor Green

# Function to check if resource exists
function Test-AzureResource {
    param($Command)
    try {
        $result = Invoke-Expression $Command 2>$null
        return $result -ne $null
    }
    catch {
        return $false
    }
}

# Configure Service Bus Topics and Subscriptions
Write-Host "📋 Configuring Service Bus..." -ForegroundColor Yellow

$serviceBusEntities = @(
    @{
        Type = "topic"
        Name = "analytics-events"
        Subscription = "analytics-aggregator"
    },
    @{
        Type = "topic" 
        Name = "document-collaboration"
        Subscription = "collaboration-processor"
    },
    @{
        Type = "topic"
        Name = "monitoring-events" 
        Subscription = "system-monitor"
    },
    @{
        Type = "queue"
        Name = "workflow-orchestration"
    }
)

foreach ($entity in $serviceBusEntities) {
    if ($entity.Type -eq "topic") {
        # Create topic
        Write-Host "Creating topic: $($entity.Name)" -ForegroundColor Cyan
        az servicebus topic create --namespace-name $ServiceBusNamespace --resource-group $ResourceGroup --name $entity.Name --max-size 1024
        
        if ($entity.Subscription) {
            # Create subscription
            Write-Host "Creating subscription: $($entity.Subscription) for topic: $($entity.Name)" -ForegroundColor Cyan
            az servicebus topic subscription create --namespace-name $ServiceBusNamespace --resource-group $ResourceGroup --topic-name $entity.Name --name $entity.Subscription --max-delivery-count 10
        }
    }
    elseif ($entity.Type -eq "queue") {
        # Create queue
        Write-Host "Creating queue: $($entity.Name)" -ForegroundColor Cyan
        az servicebus queue create --namespace-name $ServiceBusNamespace --resource-group $ResourceGroup --name $entity.Name --max-size 1024
    }
}

# Verify Service Bus configuration
Write-Host "🔍 Verifying Service Bus configuration..." -ForegroundColor Yellow
az servicebus topic list --namespace-name $ServiceBusNamespace --resource-group $ResourceGroup --output table
az servicebus queue list --namespace-name $ServiceBusNamespace --resource-group $ResourceGroup --output table

# Verify Event Grid configuration
Write-Host "🔍 Verifying Event Grid configuration..." -ForegroundColor Yellow
az eventgrid topic show --name $EventGridTopic --resource-group $ResourceGroup --output table

# Test Event Grid endpoint
Write-Host "🧪 Testing Event Grid endpoint..." -ForegroundColor Yellow
$eventGridEndpoint = az eventgrid topic show --name $EventGridTopic --resource-group $ResourceGroup --query "endpoint" --output tsv
Write-Host "Event Grid Endpoint: $eventGridEndpoint" -ForegroundColor Green

Write-Host "✅ Azure Services configuration completed!" -ForegroundColor Green
Write-Host "📝 Next steps:" -ForegroundColor Yellow
Write-Host "   1. Restart your Azure Functions to pick up the new Service Bus entities" -ForegroundColor White
Write-Host "   2. Monitor the logs to ensure no more 404 errors" -ForegroundColor White
Write-Host "   3. Test the Event Grid webhook endpoint" -ForegroundColor White

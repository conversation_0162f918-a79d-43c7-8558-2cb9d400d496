/**
 * Document Archiving Function
 * Handles document archiving, retention, and lifecycle management
 * Migrated from old-arch/src/document-service/archiving/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { eventService } from '../shared/services/event';

// Archiving types and enums
enum ArchiveStatus {
  ACTIVE = 'ACTIVE',
  ARCHIVED = 'ARCHIVED',
  PENDING_DELETION = 'PENDING_DELETION',
  DELETED = 'DELETED'
}

enum RetentionPolicy {
  DAYS_30 = 'DAYS_30',
  DAYS_90 = 'DAYS_90',
  DAYS_365 = 'DAYS_365',
  YEARS_7 = 'YEARS_7',
  PERMANENT = 'PERMANENT',
  CUSTOM = 'CUSTOM'
}

// Validation schemas
const archiveDocumentSchema = Joi.object({
  documentId: Joi.string().uuid().required(),
  retentionPolicy: Joi.string().valid(...Object.values(RetentionPolicy)).default(RetentionPolicy.YEARS_7),
  customRetentionDays: Joi.number().min(1).max(3650).optional(),
  reason: Joi.string().max(500).optional(),
  preserveMetadata: Joi.boolean().default(true),
  compressContent: Joi.boolean().default(true)
});

const restoreDocumentSchema = Joi.object({
  documentId: Joi.string().uuid().required(),
  reason: Joi.string().max(500).optional(),
  restoreToProject: Joi.string().uuid().optional()
});

interface ArchiveDocumentRequest {
  documentId: string;
  retentionPolicy?: RetentionPolicy;
  customRetentionDays?: number;
  reason?: string;
  preserveMetadata?: boolean;
  compressContent?: boolean;
}

interface DocumentArchive {
  id: string;
  documentId: string;
  originalDocument: any;
  archiveStatus: ArchiveStatus;
  retentionPolicy: RetentionPolicy;
  archivedAt: string;
  expiresAt?: string;
  reason?: string;
  metadata: {
    originalSize: number;
    compressedSize?: number;
    preserveMetadata: boolean;
    compressContent: boolean;
    checksumOriginal: string;
    checksumArchived?: string;
  };
  archivedBy: string;
  organizationId: string;
  tenantId: string;
}

/**
 * Archive document handler
 */
export async function archiveDocument(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;
  logger.info("Archive document started", { correlationId });

  try {
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    const body = await request.json();
    const { error, value } = archiveDocumentSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const archiveRequest: ArchiveDocumentRequest = value;

    // Get document
    const document = await db.readItem('documents', archiveRequest.documentId, archiveRequest.documentId);
    if (!document) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document not found" }
      }, request);
    }

    const documentData = document as any;

    // Check document access
    const hasAccess = await checkDocumentAccess(documentData, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to document" }
      }, request);
    }

    // Check if document is already archived
    if (documentData.status === 'ARCHIVED') {
      return addCorsHeaders({
        status: 409,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document is already archived" }
      }, request);
    }

    // Calculate expiration date
    const expiresAt = calculateExpirationDate(
      archiveRequest.retentionPolicy || RetentionPolicy.YEARS_7,
      archiveRequest.customRetentionDays
    );

    // Create archive record
    const archiveId = uuidv4();
    const now = new Date().toISOString();

    const documentArchive: DocumentArchive = {
      id: archiveId,
      documentId: archiveRequest.documentId,
      originalDocument: documentData,
      archiveStatus: ArchiveStatus.ARCHIVED,
      retentionPolicy: archiveRequest.retentionPolicy || RetentionPolicy.YEARS_7,
      archivedAt: now,
      expiresAt,
      reason: archiveRequest.reason,
      metadata: {
        originalSize: documentData.size || 0,
        preserveMetadata: archiveRequest.preserveMetadata !== false,
        compressContent: archiveRequest.compressContent !== false,
        checksumOriginal: generateChecksum(documentData)
      },
      archivedBy: user.id,
      organizationId: documentData.organizationId,
      tenantId: user.tenantId || user.id
    };

    // Process archiving
    const archiveResult = await processDocumentArchiving(documentArchive);
    documentArchive.metadata.compressedSize = archiveResult.compressedSize;
    documentArchive.metadata.checksumArchived = archiveResult.checksum;

    await db.createItem('document-archives', documentArchive);

    // Update document status
    const updatedDocument = {
      ...documentData,
      id: documentData.id,
      status: 'ARCHIVED',
      archivedAt: now,
      archivedBy: user.id,
      archiveId,
      updatedAt: now
    };

    await db.updateItem('documents', updatedDocument);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "document_archived",
      userId: user.id,
      organizationId: documentData.organizationId,
      projectId: documentData.projectId,
      documentId: archiveRequest.documentId,
      timestamp: now,
      details: {
        archiveId,
        retentionPolicy: documentArchive.retentionPolicy,
        expiresAt,
        reason: archiveRequest.reason,
        originalSize: documentArchive.metadata.originalSize,
        compressedSize: documentArchive.metadata.compressedSize
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'DocumentArchived',
      aggregateId: archiveRequest.documentId,
      aggregateType: 'Document',
      version: 1,
      data: {
        document: updatedDocument,
        archive: documentArchive,
        archivedBy: user.id
      },
      userId: user.id,
      organizationId: documentData.organizationId,
      tenantId: user.tenantId
    });

    logger.info("Document archived successfully", {
      correlationId,
      documentId: archiveRequest.documentId,
      archiveId,
      retentionPolicy: documentArchive.retentionPolicy,
      expiresAt,
      archivedBy: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        archiveId,
        documentId: archiveRequest.documentId,
        status: ArchiveStatus.ARCHIVED,
        retentionPolicy: documentArchive.retentionPolicy,
        archivedAt: now,
        expiresAt,
        compressionRatio: documentArchive.metadata.compressedSize ?
          (documentArchive.metadata.originalSize / documentArchive.metadata.compressedSize).toFixed(2) : null,
        message: "Document archived successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Archive document failed", { correlationId, error: errorMessage });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Restore document handler
 */
export async function restoreDocument(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;
  logger.info("Restore document started", { correlationId });

  try {
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    const body = await request.json();
    const { error, value } = restoreDocumentSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const restoreRequest = value;

    // Get archived document
    const document = await db.readItem('documents', restoreRequest.documentId, restoreRequest.documentId);
    if (!document) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document not found" }
      }, request);
    }

    const documentData = document as any;

    if (documentData.status !== 'ARCHIVED') {
      return addCorsHeaders({
        status: 409,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document is not archived" }
      }, request);
    }

    // Check document access
    const hasAccess = await checkDocumentAccess(documentData, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to document" }
      }, request);
    }

    // Get archive record
    const archive = await db.readItem('document-archives', documentData.archiveId, documentData.archiveId);
    if (!archive) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Archive record not found" }
      }, request);
    }

    const archiveData = archive as any;

    // Process restoration
    const restoreResult = await processDocumentRestoration(archiveData);

    // Update document status
    const now = new Date().toISOString();
    const restoredDocument = {
      ...documentData,
      id: documentData.id,
      status: 'ACTIVE',
      restoredAt: now,
      restoredBy: user.id,
      projectId: restoreRequest.restoreToProject || documentData.projectId,
      updatedAt: now
    };

    await db.updateItem('documents', restoredDocument);

    // Update archive status
    const updatedArchive = {
      ...archiveData,
      id: archiveData.id,
      archiveStatus: ArchiveStatus.ACTIVE,
      restoredAt: now,
      restoredBy: user.id
    };

    await db.updateItem('document-archives', updatedArchive);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "document_restored",
      userId: user.id,
      organizationId: documentData.organizationId,
      projectId: restoredDocument.projectId,
      documentId: restoreRequest.documentId,
      timestamp: now,
      details: {
        archiveId: documentData.archiveId,
        reason: restoreRequest.reason,
        restoredToProject: restoreRequest.restoreToProject,
        originalArchivedAt: archiveData.archivedAt
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'DocumentRestored',
      aggregateId: restoreRequest.documentId,
      aggregateType: 'Document',
      version: 1,
      data: {
        document: restoredDocument,
        archive: updatedArchive,
        restoredBy: user.id
      },
      userId: user.id,
      organizationId: documentData.organizationId,
      tenantId: user.tenantId
    });

    logger.info("Document restored successfully", {
      correlationId,
      documentId: restoreRequest.documentId,
      archiveId: documentData.archiveId,
      restoredBy: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        documentId: restoreRequest.documentId,
        status: 'ACTIVE',
        restoredAt: now,
        projectId: restoredDocument.projectId,
        message: "Document restored successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Restore document failed", { correlationId, error: errorMessage });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkDocumentAccess(document: any, userId: string): Promise<boolean> {
  try {
    if (document.createdBy === userId) return true;

    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [document.organizationId, userId, 'ACTIVE']);

    return memberships.length > 0;
  } catch (error) {
    logger.error('Failed to check document access', { error, documentId: document.id, userId });
    return false;
  }
}

function calculateExpirationDate(retentionPolicy: RetentionPolicy, customDays?: number): string | undefined {
  const now = new Date();
  let expirationDate: Date;

  switch (retentionPolicy) {
    case RetentionPolicy.DAYS_30:
      expirationDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
      break;
    case RetentionPolicy.DAYS_90:
      expirationDate = new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000);
      break;
    case RetentionPolicy.DAYS_365:
      expirationDate = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000);
      break;
    case RetentionPolicy.YEARS_7:
      expirationDate = new Date(now.getTime() + 7 * 365 * 24 * 60 * 60 * 1000);
      break;
    case RetentionPolicy.CUSTOM:
      if (!customDays) throw new Error('Custom retention days required for CUSTOM policy');
      expirationDate = new Date(now.getTime() + customDays * 24 * 60 * 60 * 1000);
      break;
    case RetentionPolicy.PERMANENT:
      return undefined;
    default:
      expirationDate = new Date(now.getTime() + 7 * 365 * 24 * 60 * 60 * 1000);
  }

  return expirationDate.toISOString();
}

function generateChecksum(document: any): string {
  // Simplified checksum generation
  const crypto = require('crypto');
  const content = JSON.stringify(document);
  return crypto.createHash('sha256').update(content).digest('hex');
}

async function processDocumentArchiving(archive: DocumentArchive): Promise<any> {
  try {
    // Simulate compression and archiving process
    const originalSize = archive.metadata.originalSize;
    const compressionRatio = archive.metadata.compressContent ? 0.7 : 1.0;
    const compressedSize = Math.floor(originalSize * compressionRatio);

    // Generate checksum for archived content
    const checksum = generateChecksum(archive.originalDocument);

    logger.info('Document archiving processed', {
      archiveId: archive.id,
      originalSize,
      compressedSize,
      compressionRatio: (originalSize / compressedSize).toFixed(2)
    });

    return {
      compressedSize,
      checksum,
      success: true
    };

  } catch (error) {
    logger.error('Failed to process document archiving', { error, archiveId: archive.id });
    throw error;
  }
}

async function processDocumentRestoration(archive: any): Promise<any> {
  try {
    // Simulate restoration process
    logger.info('Document restoration processed', {
      archiveId: archive.id,
      documentId: archive.documentId
    });

    return {
      success: true,
      restoredSize: archive.metadata.originalSize
    };

  } catch (error) {
    logger.error('Failed to process document restoration', { error, archiveId: archive.id });
    throw error;
  }
}

// Register functions
app.http('document-archive', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'documents/{documentId}/archive',
  handler: archiveDocument
});

app.http('document-restore', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'documents/{documentId}/restore',
  handler: restoreDocument
});

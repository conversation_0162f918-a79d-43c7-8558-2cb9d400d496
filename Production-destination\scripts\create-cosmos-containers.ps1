# Create Cosmos DB Containers Script
# This script creates all required containers for the hepz-backend-production application

$accountName = "DocuContext"
$resourceGroup = "docucontext"
$databaseName = "hepz-backend-production"

Write-Host "Creating Cosmos DB containers for $databaseName..." -ForegroundColor Green

# Create database first (if it doesn't exist)
Write-Host "Creating database: $databaseName" -ForegroundColor Yellow
az cosmosdb sql database create --account-name $accountName --resource-group $resourceGroup --name $databaseName

# Define containers with their partition keys
$containers = @(
    @{ name = "system-settings"; partitionKey = "/type" },
    @{ name = "webhook-endpoints"; partitionKey = "/type" },
    @{ name = "users"; partitionKey = "/id" },
    @{ name = "organizations"; partitionKey = "/id" },
    @{ name = "organization-members"; partitionKey = "/organizationId" },
    @{ name = "organization-invitations"; partitionKey = "/organizationId" },
    @{ name = "documents"; partitionKey = "/organizationId" },
    @{ name = "document-versions"; partitionKey = "/documentId" },
    @{ name = "document-shares"; partitionKey = "/documentId" },
    @{ name = "document-comments"; partitionKey = "/documentId" },
    @{ name = "projects"; partitionKey = "/organizationId" },
    @{ name = "project-members"; partitionKey = "/projectId" },
    @{ name = "project-invitations"; partitionKey = "/projectId" },
    @{ name = "templates"; partitionKey = "/organizationId" },
    @{ name = "project-templates"; partitionKey = "/organizationId" },
    @{ name = "workflow-definitions"; partitionKey = "/organizationId" },
    @{ name = "workflow-executions"; partitionKey = "/organizationId" },
    @{ name = "workflow-templates"; partitionKey = "/organizationId" },
    @{ name = "ai-task-history"; partitionKey = "/organizationId" },
    @{ name = "ai-settings"; partitionKey = "/organizationId" },
    @{ name = "ai-models"; partitionKey = "/organizationId" },
    @{ name = "analytics-events"; partitionKey = "/organizationId" },
    @{ name = "analytics-reports"; partitionKey = "/organizationId" },
    @{ name = "notifications"; partitionKey = "/userId" },
    @{ name = "notification-preferences"; partitionKey = "/userId" },
    @{ name = "notification-tokens"; partitionKey = "/userId" },
    @{ name = "subscriptions"; partitionKey = "/organizationId" },
    @{ name = "subscription-plans"; partitionKey = "/id" },
    @{ name = "logs"; partitionKey = "/level" },
    @{ name = "audit-logs"; partitionKey = "/organizationId" },
    @{ name = "event-store"; partitionKey = "/aggregateId" },
    @{ name = "event-snapshots"; partitionKey = "/aggregateId" },
    @{ name = "roles"; partitionKey = "/organizationId" },
    @{ name = "permissions"; partitionKey = "/organizationId" },
    @{ name = "user-permissions"; partitionKey = "/userId" },
    @{ name = "classification-categories"; partitionKey = "/organizationId" },
    @{ name = "classification-results"; partitionKey = "/documentId" },
    @{ name = "document-relationships"; partitionKey = "/sourceDocumentId" },
    @{ name = "signatures"; partitionKey = "/documentId" },
    @{ name = "tenants"; partitionKey = "/id" },
    @{ name = "health-metrics"; partitionKey = "/type" },
    @{ name = "activities"; partitionKey = "/organizationId" },
    @{ name = "emails"; partitionKey = "/organizationId" },
    @{ name = "email-messages"; partitionKey = "/organizationId" },
    @{ name = "email-templates"; partitionKey = "/organizationId" },
    @{ name = "messages"; partitionKey = "/channelId" },
    @{ name = "channels"; partitionKey = "/organizationId" },
    @{ name = "predictions"; partitionKey = "/organizationId" },
    @{ name = "bi-reports"; partitionKey = "/organizationId" },
    @{ name = "lemonsqueezy-webhook-events"; partitionKey = "/eventId" },
    @{ name = "webhooks"; partitionKey = "/organizationId" },
    @{ name = "data-migrations"; partitionKey = "/tenantId" }
)

# Create each container
foreach ($container in $containers) {
    Write-Host "Creating container: $($container.name) with partition key: $($container.partitionKey)" -ForegroundColor Cyan
    
    try {
        az cosmosdb sql container create `
            --account-name $accountName `
            --resource-group $resourceGroup `
            --database-name $databaseName `
            --name $container.name `
            --partition-key-path $container.partitionKey `
            --throughput 400
        
        Write-Host "✓ Created container: $($container.name)" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Failed to create container: $($container.name) - $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "Cosmos DB container creation completed!" -ForegroundColor Green

/**
 * System Configuration Function
 * Handles system-wide configuration management
 * Migrated from old-arch/src/admin-service/configuration/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';
import { eventService } from '../shared/services/event';

// Configuration types and enums
enum ConfigurationType {
  SYSTEM = 'SYSTEM',
  ORGANIZATION = 'ORGANIZATION',
  USER = 'USER',
  FEATURE = 'FEATURE',
  INTEGRATION = 'INTEGRATION'
}

enum ConfigurationScope {
  GLOBAL = 'GLOBAL',
  TENANT = 'TENANT',
  ORGANIZATION = 'ORGANIZATION',
  USER = 'USER'
}

// Validation schemas
const updateConfigurationSchema = Joi.object({
  key: Joi.string().min(2).max(100).required(),
  value: Joi.any().required(),
  type: Joi.string().valid(...Object.values(ConfigurationType)).required(),
  scope: Joi.string().valid(...Object.values(ConfigurationScope)).required(),
  organizationId: Joi.string().uuid().optional(),
  userId: Joi.string().uuid().optional(),
  description: Joi.string().max(500).optional(),
  encrypted: Joi.boolean().default(false),
  metadata: Joi.object().optional()
});

interface Configuration {
  id: string;
  key: string;
  value: any;
  type: ConfigurationType;
  scope: ConfigurationScope;
  organizationId?: string;
  userId?: string;
  description?: string;
  encrypted: boolean;
  metadata: any;
  version: number;
  createdBy: string;
  createdAt: string;
  updatedBy: string;
  updatedAt: string;
  tenantId: string;
}

/**
 * Get configuration handler
 */
export async function getConfiguration(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;
  logger.info("Get configuration started", { correlationId });

  try {
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;
    const url = new URL(request.url);
    const key = url.searchParams.get('key');
    const scope = url.searchParams.get('scope') as ConfigurationScope || ConfigurationScope.GLOBAL;
    const organizationId = url.searchParams.get('organizationId');

    if (!key) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Configuration key is required" }
      }, request);
    }

    // Check access permissions
    const hasAccess = await checkConfigurationAccess(user, scope, organizationId || undefined);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to configuration" }
      }, request);
    }

    // Get configuration
    const configuration = await getConfigurationValue(key, scope, organizationId || undefined, user.id);

    if (!configuration) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Configuration not found" }
      }, request);
    }

    logger.info("Configuration retrieved successfully", {
      correlationId,
      key,
      scope,
      organizationId,
      requestedBy: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        key: configuration.key,
        value: configuration.value,
        type: configuration.type,
        scope: configuration.scope,
        description: configuration.description,
        version: configuration.version,
        updatedAt: configuration.updatedAt
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get configuration failed", { correlationId, error: errorMessage });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Update configuration handler
 */
export async function updateConfiguration(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;
  logger.info("Update configuration started", { correlationId });

  try {
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Check admin access
    const hasAccess = await checkConfigurationAccess(user, ConfigurationScope.GLOBAL);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to configuration management" }
      }, request);
    }

    const body = await request.json();
    const { error, value } = updateConfigurationSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const configRequest = value;

    // Get existing configuration or create new
    const existing = await getConfigurationValue(
      configRequest.key,
      configRequest.scope,
      configRequest.organizationId,
      configRequest.userId
    );

    const configId = existing?.id || uuidv4();
    const now = new Date().toISOString();

    const configuration: Configuration = {
      id: configId,
      key: configRequest.key,
      value: configRequest.encrypted ? await encryptValue(configRequest.value) : configRequest.value,
      type: configRequest.type,
      scope: configRequest.scope,
      organizationId: configRequest.organizationId,
      userId: configRequest.userId,
      description: configRequest.description,
      encrypted: configRequest.encrypted,
      metadata: configRequest.metadata || {},
      version: existing ? existing.version + 1 : 1,
      createdBy: existing?.createdBy || user.id,
      createdAt: existing?.createdAt || now,
      updatedBy: user.id,
      updatedAt: now,
      tenantId: user.tenantId || user.id
    };

    if (existing) {
      await db.updateItem('configurations', configuration);
    } else {
      await db.createItem('configurations', configuration);
    }

    // Cache configuration for fast access
    await cacheConfiguration(configuration);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: existing ? "configuration_updated" : "configuration_created",
      userId: user.id,
      organizationId: configRequest.organizationId,
      timestamp: now,
      details: {
        configurationKey: configRequest.key,
        configurationType: configRequest.type,
        configurationScope: configRequest.scope,
        encrypted: configRequest.encrypted,
        version: configuration.version
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: existing ? 'ConfigurationUpdated' : 'ConfigurationCreated',
      aggregateId: configId,
      aggregateType: 'Configuration',
      version: configuration.version,
      data: {
        configuration: {
          ...configuration,
          value: configRequest.encrypted ? '[ENCRYPTED]' : configuration.value
        },
        updatedBy: user.id
      },
      userId: user.id,
      organizationId: configRequest.organizationId,
      tenantId: user.tenantId
    });

    logger.info("Configuration updated successfully", {
      correlationId,
      configurationKey: configRequest.key,
      configurationType: configRequest.type,
      configurationScope: configRequest.scope,
      version: configuration.version,
      updatedBy: user.id
    });

    return addCorsHeaders({
      status: existing ? 200 : 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        key: configuration.key,
        type: configuration.type,
        scope: configuration.scope,
        version: configuration.version,
        updatedAt: configuration.updatedAt,
        message: existing ? "Configuration updated successfully" : "Configuration created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Update configuration failed", { correlationId, error: errorMessage });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkConfigurationAccess(user: any, scope: ConfigurationScope, organizationId?: string): Promise<boolean> {
  try {
    // Check if user has admin role
    if (user.roles?.includes('admin') || user.roles?.includes('config_admin')) {
      return true;
    }

    // For organization scope, check organization access
    if (scope === ConfigurationScope.ORGANIZATION && organizationId) {
      const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
      const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, user.id, 'ACTIVE']);

      if (memberships.length > 0) {
        const membership = memberships[0] as any;
        return membership.role === 'OWNER' || membership.role === 'ADMIN';
      }
    }

    // For user scope, check if it's the same user
    if (scope === ConfigurationScope.USER) {
      return true; // Users can access their own configurations
    }

    return false;
  } catch (error) {
    logger.error('Failed to check configuration access', { error, userId: user.id, scope, organizationId });
    return false;
  }
}

async function getConfigurationValue(key: string, scope: ConfigurationScope, organizationId?: string, userId?: string): Promise<Configuration | null> {
  try {
    // Try cache first
    const cacheKey = `config:${scope}:${organizationId || 'global'}:${userId || 'global'}:${key}`;
    const cached = await redis.get(cacheKey);

    if (cached) {
      const config = JSON.parse(cached);
      if (config.encrypted) {
        config.value = await decryptValue(config.value);
      }
      return config;
    }

    // Query database
    let query = 'SELECT * FROM c WHERE c.key = @key AND c.scope = @scope';
    const parameters = [key, scope];

    if (organizationId) {
      query += ' AND c.organizationId = @orgId';
      parameters.push(organizationId);
    }

    if (userId) {
      query += ' AND c.userId = @userId';
      parameters.push(userId);
    }

    const configurations = await db.queryItems('configurations', query, parameters);

    if (configurations.length > 0) {
      const config = configurations[0] as Configuration;

      // Decrypt if encrypted
      if (config.encrypted) {
        config.value = await decryptValue(config.value);
      }

      // Cache for 5 minutes
      await redis.setex(cacheKey, 300, JSON.stringify({
        ...config,
        value: config.encrypted ? config.value : config.value // Store decrypted in cache
      }));

      return config;
    }

    return null;

  } catch (error) {
    logger.error('Failed to get configuration value', { error, key, scope, organizationId, userId });
    return null;
  }
}

async function cacheConfiguration(configuration: Configuration): Promise<void> {
  try {
    const cacheKey = `config:${configuration.scope}:${configuration.organizationId || 'global'}:${configuration.userId || 'global'}:${configuration.key}`;

    await redis.setex(cacheKey, 300, JSON.stringify(configuration));

    // Also add to configuration list cache
    const listCacheKey = `configs:${configuration.scope}:${configuration.organizationId || 'global'}`;
    await redis.sadd(listCacheKey, configuration.key);
    await redis.expire(listCacheKey, 300);

  } catch (error) {
    logger.error('Failed to cache configuration', { error, configId: configuration.id });
  }
}

async function encryptValue(value: any): Promise<string> {
  try {
    // Simplified encryption - in production use proper encryption service
    const crypto = require('crypto');
    const algorithm = 'aes-256-gcm';
    const key = crypto.scryptSync(process.env.CONFIG_ENCRYPTION_KEY || 'default-key', 'salt', 32);
    const iv = crypto.randomBytes(16);

    const cipher = crypto.createCipher(algorithm, key);
    let encrypted = cipher.update(JSON.stringify(value), 'utf8', 'hex');
    encrypted += cipher.final('hex');

    return `${iv.toString('hex')}:${encrypted}`;
  } catch (error) {
    logger.error('Failed to encrypt configuration value', { error });
    return JSON.stringify(value); // Fallback to unencrypted
  }
}

async function decryptValue(encryptedValue: string): Promise<any> {
  try {
    // Simplified decryption - in production use proper encryption service
    const crypto = require('crypto');
    const algorithm = 'aes-256-gcm';
    const key = crypto.scryptSync(process.env.CONFIG_ENCRYPTION_KEY || 'default-key', 'salt', 32);

    const [_, encrypted] = encryptedValue.split(':');

    const decipher = crypto.createDecipher(algorithm, key);
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return JSON.parse(decrypted);
  } catch (error) {
    logger.error('Failed to decrypt configuration value', { error });
    return encryptedValue; // Fallback to encrypted value
  }
}

// Register functions
app.http('config-get', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'config',
  handler: getConfiguration
});

app.http('config-update', {
  methods: ['PUT', 'POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'management/configuration',
  handler: updateConfiguration
});

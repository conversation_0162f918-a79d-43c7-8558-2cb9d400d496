/**
 * Document Specialized Processing Function
 * Handles advanced AI processing for specific document types
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { BlobServiceClient } from "@azure/storage-blob";
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Validation schemas
const specializedProcessingSchema = Joi.object({
  documentId: Joi.string().uuid().required(),
  documentType: Joi.string().valid('invoice', 'receipt', 'contract', 'businessCard', 'idDocument', 'tax', 'healthInsurance').required(),
  workflowOptions: Joi.object({
    autoValidation: Joi.boolean().default(true),
    extractLineItems: Joi.boolean().default(true),
    detectAnomalies: Joi.boolean().default(true),
    generateSummary: Joi.boolean().default(false),
    enableCompliance: Joi.boolean().default(false),
    createWorkflowTasks: Joi.boolean().default(false)
  }).optional(),
  businessRules: Joi.object({
    requiredFields: Joi.array().items(Joi.string()).optional(),
    validationRules: Joi.array().optional(),
    approvalWorkflow: Joi.boolean().default(false),
    notificationSettings: Joi.object().optional()
  }).optional(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().required()
});

interface SpecializedProcessingRequest {
  documentId: string;
  documentType: 'invoice' | 'receipt' | 'contract' | 'businessCard' | 'idDocument' | 'tax' | 'healthInsurance';
  workflowOptions?: {
    autoValidation?: boolean;
    extractLineItems?: boolean;
    detectAnomalies?: boolean;
    generateSummary?: boolean;
    enableCompliance?: boolean;
    createWorkflowTasks?: boolean;
  };
  businessRules?: {
    requiredFields?: string[];
    validationRules?: any[];
    approvalWorkflow?: boolean;
    notificationSettings?: any;
  };
  organizationId: string;
  projectId: string;
}

interface SpecializedProcessingResponse {
  documentId: string;
  processingId: string;
  documentType: string;
  extractedData: {
    structuredFields: Record<string, any>;
    lineItems: any[];
    entities: any[];
    signatures: any[];
    barcodes: any[];
    formulas: any[];
  };
  businessIntelligence: {
    classification: any;
    validation: any;
    anomalies: any[];
    compliance: any;
    insights: any[];
  };
  workflowActions: {
    tasksCreated: any[];
    approvalRequired: boolean;
    notifications: any[];
    nextSteps: string[];
  };
  processingTime: number;
  success: boolean;
}

/**
 * Process specialized document handler
 */
export async function processSpecializedDocument(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Specialized document processing started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = specializedProcessingSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const processingRequest: SpecializedProcessingRequest = value;
    const startTime = Date.now();
    const processingId = uuidv4();

    // Get document
    const document = await db.readItem('documents', processingRequest.documentId, processingRequest.documentId);
    if (!document) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document not found" }
      }, request);
    }

    // Check access permissions
    const hasAccess = (
      (document as any).createdBy === user.id ||
      (document as any).organizationId === user.tenantId ||
      user.roles?.includes('admin')
    );

    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied" }
      }, request);
    }

    // Get document content from blob storage
    const blobServiceClient = new BlobServiceClient(
      process.env.AZURE_STORAGE_CONNECTION_STRING || ""
    );
    const containerClient = blobServiceClient.getContainerClient(
      process.env.DOCUMENT_CONTAINER || "documents"
    );
    const blobClient = containerClient.getBlobClient((document as any).blobName);

    const downloadResponse = await blobClient.download();
    if (!downloadResponse.readableStreamBody) {
      return addCorsHeaders({
        status: 500,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Failed to download document content" }
      }, request);
    }

    const documentBuffer = await streamToBuffer(downloadResponse.readableStreamBody);

    // Perform specialized analysis (simplified implementation)
    const comprehensiveResult = await performComprehensiveAnalysis(
      documentBuffer,
      processingRequest.documentType
    );

    // Process document type specific logic
    const specializedData = await processDocumentTypeSpecificLogic(
      comprehensiveResult,
      processingRequest.documentType,
      processingRequest.workflowOptions || {},
      processingRequest.businessRules || {}
    );

    // Generate workflow actions
    const workflowActions = await generateWorkflowActions(
      specializedData,
      processingRequest.documentType,
      processingRequest.workflowOptions || {},
      user
    );

    // Create specialized processing response
    const response: SpecializedProcessingResponse = {
      documentId: processingRequest.documentId,
      processingId,
      documentType: processingRequest.documentType,
      extractedData: {
        structuredFields: specializedData.structuredFields,
        lineItems: specializedData.lineItems,
        entities: comprehensiveResult.entities || [],
        signatures: comprehensiveResult.signatures || [],
        barcodes: comprehensiveResult.barcodes || [],
        formulas: comprehensiveResult.formulas || []
      },
      businessIntelligence: {
        classification: specializedData.classification,
        validation: specializedData.validation,
        anomalies: specializedData.anomalies,
        compliance: specializedData.compliance,
        insights: specializedData.insights
      },
      workflowActions,
      processingTime: Date.now() - startTime,
      success: true
    };

    // Save specialized processing results
    await db.createItem('specialized-processing-results', {
      id: processingId,
      documentId: processingRequest.documentId,
      userId: user.id,
      organizationId: processingRequest.organizationId,
      projectId: processingRequest.projectId,
      documentType: processingRequest.documentType,
      results: response,
      createdAt: new Date().toISOString(),
      tenantId: user.tenantId
    });

    // Update document with specialized processing info
    const updatedDocument = {
      ...document,
      id: (document as any).id,
      specializedProcessing: {
        type: processingRequest.documentType,
        processingId,
        extractedData: response.extractedData,
        businessIntelligence: response.businessIntelligence,
        processedAt: new Date().toISOString()
      },
      updatedAt: new Date().toISOString(),
      updatedBy: user.id
    };

    await db.updateItem('documents', updatedDocument);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "specialized_document_processing",
      userId: user.id,
      organizationId: processingRequest.organizationId,
      projectId: processingRequest.projectId,
      documentId: processingRequest.documentId,
      processingId,
      timestamp: new Date().toISOString(),
      details: {
        documentType: processingRequest.documentType,
        structuredFieldsExtracted: Object.keys(specializedData.structuredFields).length,
        lineItemsExtracted: specializedData.lineItems.length,
        anomaliesDetected: specializedData.anomalies.length,
        workflowTasksCreated: workflowActions.tasksCreated.length,
        processingTime: response.processingTime
      },
      tenantId: user.tenantId
    });

    logger.info("Specialized document processing completed", {
      correlationId,
      documentId: processingRequest.documentId,
      processingId,
      documentType: processingRequest.documentType,
      processingTime: response.processingTime,
      fieldsExtracted: Object.keys(specializedData.structuredFields).length
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: response
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Specialized document processing failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Perform comprehensive analysis (simplified implementation)
 */
async function performComprehensiveAnalysis(documentBuffer: Buffer, documentType: string): Promise<any> {
  // This is a simplified implementation
  // In production, this would integrate with Azure Document Intelligence

  logger.info("Performing comprehensive analysis", {
    documentSize: documentBuffer.length,
    documentType
  });

  // Simulate analysis results
  return {
    confidence: 0.95,
    keyValuePairs: {
      'Invoice Number': { value: 'INV-2024-001', confidence: 0.98 },
      'Invoice Date': { value: '2024-01-15', confidence: 0.97 },
      'Total Amount': { value: '1,250.00', confidence: 0.99 },
      'Vendor Name': { value: 'Acme Corporation', confidence: 0.96 }
    },
    tables: [],
    entities: [],
    signatures: [],
    barcodes: [],
    formulas: []
  };
}

/**
 * Process document type specific logic
 */
async function processDocumentTypeSpecificLogic(
  comprehensiveResult: any,
  documentType: string,
  workflowOptions: any,
  businessRules: any
): Promise<any> {
  const specializedData = {
    structuredFields: {} as Record<string, any>,
    lineItems: [] as any[],
    classification: { type: documentType, confidence: comprehensiveResult.confidence },
    validation: { isValid: true, errors: [] as string[], warnings: [] as string[] },
    anomalies: [] as any[],
    compliance: {},
    insights: [] as any[]
  };

  // Extract structured fields based on document type
  if (documentType === 'invoice') {
    specializedData.structuredFields = comprehensiveResult.keyValuePairs;

    // Validate required fields
    const requiredFields = businessRules.requiredFields || ['Invoice Number', 'Invoice Date', 'Total Amount'];
    for (const field of requiredFields) {
      if (!specializedData.structuredFields[field]) {
        specializedData.validation.errors.push(`Missing required field: ${field}`);
        specializedData.validation.isValid = false;
      }
    }

    // Detect anomalies
    if (workflowOptions.detectAnomalies) {
      const totalAmount = parseFloat(specializedData.structuredFields['Total Amount']?.value || '0');
      if (totalAmount > 10000) {
        specializedData.anomalies.push({
          type: 'high_amount',
          severity: 'warning',
          message: `Unusually high invoice amount: $${totalAmount}`,
          recommendation: 'Verify amount and consider additional approval'
        });
      }
    }
  }

  return specializedData;
}

/**
 * Generate workflow actions
 */
async function generateWorkflowActions(
  specializedData: any,
  documentType: string,
  options: any,
  user: any
): Promise<any> {
  const workflowActions = {
    tasksCreated: [] as any[],
    approvalRequired: false,
    notifications: [] as any[],
    nextSteps: [] as string[]
  };

  if (options.createWorkflowTasks) {
    if (specializedData.validation.errors.length > 0) {
      workflowActions.tasksCreated.push({
        type: 'validation_review',
        title: `Review ${documentType} Validation Errors`,
        description: `Document has ${specializedData.validation.errors.length} validation errors`,
        assignedTo: user.id,
        priority: 'high'
      });
    }

    if (specializedData.anomalies.length > 0) {
      workflowActions.approvalRequired = true;
      workflowActions.nextSteps.push('Requires manager approval due to detected anomalies');
    }
  }

  return workflowActions;
}

/**
 * Convert stream to buffer
 */
async function streamToBuffer(readableStream: NodeJS.ReadableStream): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    const chunks: Buffer[] = [];
    readableStream.on("data", (data) => {
      chunks.push(data instanceof Buffer ? data : Buffer.from(data));
    });
    readableStream.on("end", () => {
      resolve(Buffer.concat(chunks));
    });
    readableStream.on("error", reject);
  });
}

// Register functions
app.http('document-specialized-processing', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'documents/{id}/specialized-processing',
  handler: processSpecializedDocument
});

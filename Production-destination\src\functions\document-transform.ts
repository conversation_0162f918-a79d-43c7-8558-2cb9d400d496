/**
 * Document Transform Function
 * Handles document transformation operations like format conversion, merging, splitting
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { BlobServiceClient } from "@azure/storage-blob";
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Transformation types enum
enum TransformationType {
  FORMAT_CONVERSION = 'FORMAT_CONVERSION',
  MERGE_DOCUMENTS = 'MERGE_DOCUMENTS',
  SPLIT_DOCUMENT = 'SPLIT_DOCUMENT',
  EXTRACT_PAGES = 'EXTRACT_PAGES',
  ROTATE_PAGES = 'ROTATE_PAGES',
  WATERMARK = 'WATERMARK',
  COMPRESS = 'COMPRESS'
}

// Validation schemas
const transformDocumentSchema = Joi.object({
  transformationType: Joi.string().valid(...Object.values(TransformationType)).required(),
  sourceDocumentIds: Joi.array().items(Joi.string().uuid()).min(1).required(),
  targetFormat: Joi.string().optional(),
  options: Joi.object({
    pages: Joi.array().items(Joi.number().integer().min(1)).optional(),
    pageRanges: Joi.array().items(
      Joi.object({
        start: Joi.number().integer().min(1).required(),
        end: Joi.number().integer().min(1).required()
      })
    ).optional(),
    rotation: Joi.number().valid(90, 180, 270).optional(),
    watermarkText: Joi.string().max(100).optional(),
    watermarkOpacity: Joi.number().min(0.1).max(1.0).optional(),
    compressionLevel: Joi.number().min(1).max(9).optional(),
    mergeOrder: Joi.array().items(Joi.string().uuid()).optional()
  }).optional(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().required(),
  outputName: Joi.string().max(255).optional()
});

interface TransformationResult {
  transformationType: TransformationType;
  sourceDocumentIds: string[];
  transformedDocumentId: string;
  originalTotalSize: number;
  transformedSize: number;
  pageCount: number;
  processingTime: number;
  success: boolean;
}

/**
 * Transform document handler
 */
export async function transformDocument(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Document transformation started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = transformDocumentSchema.validate(body);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { 
      transformationType, 
      sourceDocumentIds, 
      targetFormat, 
      options, 
      organizationId, 
      projectId, 
      outputName 
    } = value;
    const startTime = Date.now();

    // Get and validate source documents
    const sourceDocuments: any[] = [];
    let totalOriginalSize = 0;

    for (const documentId of sourceDocumentIds) {
      const document = await db.readItem('documents', documentId, documentId);
      if (!document) {
        return addCorsHeaders({
          status: 404,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { error: `Document not found: ${documentId}` }
        }, request);
      }

      // Check access permissions
      const hasAccess = (
        (document as any).createdBy === user.id ||
        (document as any).organizationId === user.tenantId ||
        user.roles?.includes('admin')
      );

      if (!hasAccess) {
        return addCorsHeaders({
          status: 403,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { error: `Access denied to document: ${documentId}` }
        }, request);
      }

      sourceDocuments.push(document);
      totalOriginalSize += (document as any).size || 0;
    }

    // Initialize blob service client
    const blobServiceClient = new BlobServiceClient(
      process.env.AZURE_STORAGE_CONNECTION_STRING || ""
    );

    const containerClient = blobServiceClient.getContainerClient(
      process.env.DOCUMENT_CONTAINER || "documents"
    );

    // Download source documents
    const documentBuffers: Buffer[] = [];
    for (const document of sourceDocuments) {
      const blobClient = containerClient.getBlockBlobClient((document as any).blobName);
      const downloadResponse = await blobClient.download(0);
      
      if (!downloadResponse.readableStreamBody) {
        return addCorsHeaders({
          status: 500,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { error: `Failed to download document: ${(document as any).id}` }
        }, request);
      }

      const documentBuffer = await streamToBuffer(downloadResponse.readableStreamBody);
      documentBuffers.push(documentBuffer);
    }

    // Perform transformation
    const transformationResult = await performTransformation(
      documentBuffers,
      sourceDocuments,
      transformationType,
      targetFormat,
      options || {}
    );

    // Save transformed document to blob storage
    const transformedDocumentId = uuidv4();
    const fileExtension = getFileExtension(transformationResult.contentType);
    const transformedBlobName = `${organizationId}/${projectId}/${transformedDocumentId}_transformed.${fileExtension}`;
    const transformedBlobClient = containerClient.getBlockBlobClient(transformedBlobName);

    await transformedBlobClient.upload(
      transformationResult.transformedBuffer, 
      transformationResult.transformedBuffer.length, 
      {
        blobHTTPHeaders: { blobContentType: transformationResult.contentType }
      }
    );

    // Generate output name
    const defaultName = sourceDocuments.length === 1 
      ? `${sourceDocuments[0].name} (Transformed)`
      : `Merged Document (${sourceDocuments.length} files)`;

    // Create transformed document record
    const transformedDocument = {
      id: transformedDocumentId,
      sourceDocumentIds,
      name: outputName || defaultName,
      description: `Transformed document using ${transformationType}`,
      blobName: transformedBlobName,
      contentType: transformationResult.contentType,
      size: transformationResult.transformedBuffer.length,
      organizationId,
      projectId,
      createdBy: user.id,
      createdAt: new Date().toISOString(),
      updatedBy: user.id,
      updatedAt: new Date().toISOString(),
      status: "TRANSFORMED",
      metadata: {
        transformationType,
        sourceDocumentIds,
        originalTotalSize: totalOriginalSize,
        transformedSize: transformationResult.transformedBuffer.length,
        pageCount: transformationResult.pageCount,
        transformedAt: new Date().toISOString(),
        transformedBy: user.id,
        options
      },
      tenantId: user.tenantId
    };

    await db.createItem('documents', transformedDocument);

    // Create transformation record
    const transformation = {
      id: uuidv4(),
      transformationType,
      sourceDocumentIds,
      transformedDocumentId,
      options,
      result: {
        originalTotalSize: totalOriginalSize,
        transformedSize: transformationResult.transformedBuffer.length,
        pageCount: transformationResult.pageCount,
        processingTime: Date.now() - startTime
      },
      createdBy: user.id,
      createdAt: new Date().toISOString(),
      organizationId,
      projectId,
      tenantId: user.tenantId
    };

    await db.createItem('document-transformations', transformation);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "document_transformed",
      userId: user.id,
      organizationId,
      projectId,
      documentId: transformedDocumentId,
      timestamp: new Date().toISOString(),
      details: {
        transformationType,
        sourceDocumentCount: sourceDocumentIds.length,
        originalTotalSize: totalOriginalSize,
        transformedSize: transformationResult.transformedBuffer.length,
        pageCount: transformationResult.pageCount,
        processingTime: Date.now() - startTime
      },
      tenantId: user.tenantId
    });

    const response: TransformationResult = {
      transformationType,
      sourceDocumentIds,
      transformedDocumentId,
      originalTotalSize: totalOriginalSize,
      transformedSize: transformationResult.transformedBuffer.length,
      pageCount: transformationResult.pageCount,
      processingTime: Date.now() - startTime,
      success: true
    };

    logger.info("Document transformed successfully", {
      correlationId,
      transformationType,
      sourceDocumentCount: sourceDocumentIds.length,
      transformedDocumentId,
      userId: user.id,
      processingTime: response.processingTime
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: response
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Document transformation failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Perform document transformation (simplified implementation)
 */
async function performTransformation(
  documentBuffers: Buffer[],
  sourceDocuments: any[],
  transformationType: TransformationType,
  targetFormat?: string,
  options: any = {}
): Promise<any> {
  // This is a simplified implementation
  // In production, this would integrate with PDF processing libraries
  // like pdf-lib, PDFtk, or cloud services
  
  logger.info("Performing document transformation", {
    transformationType,
    documentCount: documentBuffers.length,
    targetFormat
  });

  let transformedBuffer: Buffer;
  let contentType: string;
  let pageCount = 1;

  switch (transformationType) {
    case TransformationType.MERGE_DOCUMENTS:
      // Simulate merging documents
      transformedBuffer = Buffer.concat(documentBuffers);
      contentType = targetFormat || 'application/pdf';
      pageCount = documentBuffers.length; // Simplified
      break;
      
    case TransformationType.FORMAT_CONVERSION:
      // Simulate format conversion
      transformedBuffer = documentBuffers[0];
      contentType = targetFormat || sourceDocuments[0].contentType;
      pageCount = 1;
      break;
      
    case TransformationType.SPLIT_DOCUMENT:
      // Simulate document splitting (return first part)
      transformedBuffer = documentBuffers[0];
      contentType = sourceDocuments[0].contentType;
      pageCount = Math.ceil((documentBuffers[0].length / documentBuffers[0].length) / 2);
      break;
      
    case TransformationType.EXTRACT_PAGES:
      // Simulate page extraction
      transformedBuffer = documentBuffers[0];
      contentType = sourceDocuments[0].contentType;
      pageCount = options.pages?.length || 1;
      break;
      
    case TransformationType.COMPRESS:
      // Simulate compression (reduce buffer size by 20%)
      const compressedSize = Math.floor(documentBuffers[0].length * 0.8);
      transformedBuffer = documentBuffers[0].slice(0, compressedSize);
      contentType = sourceDocuments[0].contentType;
      pageCount = 1;
      break;
      
    default:
      transformedBuffer = documentBuffers[0];
      contentType = sourceDocuments[0].contentType;
      pageCount = 1;
      break;
  }

  return {
    transformedBuffer,
    contentType,
    pageCount
  };
}

/**
 * Get file extension from content type
 */
function getFileExtension(contentType: string): string {
  const extensions: { [key: string]: string } = {
    'application/pdf': 'pdf',
    'image/jpeg': 'jpg',
    'image/png': 'png',
    'image/tiff': 'tiff',
    'application/msword': 'doc',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx'
  };
  
  return extensions[contentType] || 'bin';
}

/**
 * Convert stream to buffer
 */
async function streamToBuffer(readableStream: NodeJS.ReadableStream): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    const chunks: Buffer[] = [];
    readableStream.on("data", (data) => {
      chunks.push(data instanceof Buffer ? data : Buffer.from(data));
    });
    readableStream.on("end", () => {
      resolve(Buffer.concat(chunks));
    });
    readableStream.on("error", reject);
  });
}

// Register functions
app.http('document-transform', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'documents/transform',
  handler: transformDocument
});

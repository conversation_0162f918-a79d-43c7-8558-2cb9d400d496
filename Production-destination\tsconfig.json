{"compilerOptions": {"target": "ES2020", "module": "commonjs", "moduleResolution": "node", "outDir": "dist", "rootDir": "src", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "sourceMap": true, "baseUrl": "src", "paths": {"@shared/*": ["shared/*"]}}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}
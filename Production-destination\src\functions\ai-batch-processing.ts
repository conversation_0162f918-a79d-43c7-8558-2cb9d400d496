/**
 * AI Batch Processing Function
 * Handles batch document processing with AI models
 * Migrated from old-arch/src/ai-service/batch-processing/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
// import { BlobServiceClient } from "@azure/storage-blob"; // Unused for now
import { v4 as uuidv4 } from "uuid";
import Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
// import { notificationService } from '../shared/services/notification'; // Unused for now
import { eventService } from '../shared/services/event';

// Batch processing types and enums
enum BatchStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

enum ProcessingType {
  DOCUMENT_ANALYSIS = 'DOCUMENT_ANALYSIS',
  TEXT_EXTRACTION = 'TEXT_EXTRACTION',
  FORM_PROCESSING = 'FORM_PROCESSING',
  CLASSIFICATION = 'CLASSIFICATION',
  ENTITY_EXTRACTION = 'ENTITY_EXTRACTION'
}

// Validation schemas
const createBatchJobSchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  description: Joi.string().max(500).optional(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional(),
  processingType: Joi.string().valid(...Object.values(ProcessingType)).required(),
  documentIds: Joi.array().items(Joi.string().uuid()).min(1).max(1000).required(),
  configuration: Joi.object({
    modelId: Joi.string().optional(),
    extractTables: Joi.boolean().default(false),
    extractKeyValuePairs: Joi.boolean().default(true),
    extractEntities: Joi.boolean().default(false),
    detectLanguages: Joi.boolean().default(false),
    customPrompt: Joi.string().max(2000).optional(),
    outputFormat: Joi.string().valid('json', 'csv', 'xlsx').default('json'),
    batchSize: Joi.number().min(1).max(100).default(10),
    maxRetries: Joi.number().min(0).max(5).default(3)
  }).optional(),
  priority: Joi.string().valid('low', 'normal', 'high').default('normal'),
  scheduledAt: Joi.string().isoDate().optional(),
  notifyOnCompletion: Joi.boolean().default(true)
});

interface CreateBatchJobRequest {
  name: string;
  description?: string;
  organizationId: string;
  projectId?: string;
  processingType: ProcessingType;
  documentIds: string[];
  configuration?: {
    modelId?: string;
    extractTables?: boolean;
    extractKeyValuePairs?: boolean;
    extractEntities?: boolean;
    detectLanguages?: boolean;
    customPrompt?: string;
    outputFormat?: 'json' | 'csv' | 'xlsx';
    batchSize?: number;
    maxRetries?: number;
  };
  priority?: 'low' | 'normal' | 'high';
  scheduledAt?: string;
  notifyOnCompletion?: boolean;
}

interface BatchJob {
  id: string;
  name: string;
  description?: string;
  organizationId: string;
  projectId?: string;
  processingType: ProcessingType;
  status: BatchStatus;
  documentIds: string[];
  configuration: any;
  priority: string;
  scheduledAt?: string;
  startedAt?: string;
  completedAt?: string;
  progress: {
    total: number;
    processed: number;
    successful: number;
    failed: number;
    percentage: number;
  };
  results: {
    outputBlobName?: string;
    successfulDocuments: string[];
    failedDocuments: Array<{ documentId: string; error: string }>;
    summary: any;
  };
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  tenantId: string;
}

/**
 * Create batch processing job handler
 */
export async function createBatchJob(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create batch job started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createBatchJobSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const batchRequest: CreateBatchJobRequest = value;

    // Check organization access
    const hasAccess = await checkOrganizationAccess(batchRequest.organizationId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Validate document access
    const validDocuments = await validateDocumentAccess(batchRequest.documentIds, user.id, batchRequest.organizationId);
    if (validDocuments.length !== batchRequest.documentIds.length) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: "Some documents are not accessible",
          invalidDocuments: batchRequest.documentIds.filter(id => !validDocuments.includes(id))
        }
      }, request);
    }

    // Check batch processing limits
    const canCreateBatch = await checkBatchProcessingLimits(batchRequest.organizationId, batchRequest.documentIds.length);
    if (!canCreateBatch.allowed) {
      return addCorsHeaders({
        status: 429,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: canCreateBatch.reason }
      }, request);
    }

    // Create batch job
    const batchJobId = uuidv4();
    const now = new Date().toISOString();

    const batchJob: BatchJob = {
      id: batchJobId,
      name: batchRequest.name,
      description: batchRequest.description,
      organizationId: batchRequest.organizationId,
      projectId: batchRequest.projectId,
      processingType: batchRequest.processingType,
      status: batchRequest.scheduledAt ? BatchStatus.PENDING : BatchStatus.PROCESSING,
      documentIds: batchRequest.documentIds,
      configuration: {
        ...batchRequest.configuration,
        batchSize: batchRequest.configuration?.batchSize || 10,
        maxRetries: batchRequest.configuration?.maxRetries || 3,
        outputFormat: batchRequest.configuration?.outputFormat || 'json'
      },
      priority: batchRequest.priority || 'normal',
      scheduledAt: batchRequest.scheduledAt,
      startedAt: batchRequest.scheduledAt ? undefined : now,
      progress: {
        total: batchRequest.documentIds.length,
        processed: 0,
        successful: 0,
        failed: 0,
        percentage: 0
      },
      results: {
        successfulDocuments: [],
        failedDocuments: [],
        summary: {}
      },
      createdBy: user.id,
      createdAt: now,
      updatedAt: now,
      tenantId: user.tenantId || user.id
    };

    await db.createItem('batch-jobs', batchJob);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "batch_job_created",
      userId: user.id,
      organizationId: batchRequest.organizationId,
      projectId: batchRequest.projectId,
      timestamp: now,
      details: {
        batchJobId,
        batchJobName: batchRequest.name,
        processingType: batchRequest.processingType,
        documentCount: batchRequest.documentIds.length,
        priority: batchRequest.priority,
        isScheduled: !!batchRequest.scheduledAt
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'BatchJobCreated',
      aggregateId: batchJobId,
      aggregateType: 'BatchJob',
      version: 1,
      data: {
        batchJob,
        createdBy: user.id
      },
      userId: user.id,
      organizationId: batchRequest.organizationId,
      tenantId: user.tenantId
    });

    // Start processing if not scheduled
    if (!batchRequest.scheduledAt) {
      // Process asynchronously
      processBatchJobAsync(batchJob);
    }

    logger.info("Batch job created successfully", {
      correlationId,
      batchJobId,
      batchJobName: batchRequest.name,
      processingType: batchRequest.processingType,
      documentCount: batchRequest.documentIds.length,
      createdBy: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: batchJobId,
        name: batchRequest.name,
        processingType: batchRequest.processingType,
        status: batchJob.status,
        documentCount: batchRequest.documentIds.length,
        priority: batchRequest.priority,
        scheduledAt: batchRequest.scheduledAt,
        estimatedDuration: estimateProcessingDuration(batchRequest.documentIds.length, batchRequest.processingType),
        createdAt: now,
        message: "Batch job created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create batch job failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Get batch job status handler
 */
export async function getBatchJobStatus(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const batchJobId = request.params.batchJobId;

  logger.info("Get batch job status started", { correlationId, batchJobId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    if (!batchJobId) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Batch job ID is required" }
      }, request);
    }

    // Get batch job
    const batchJob = await db.readItem('batch-jobs', batchJobId, batchJobId);
    if (!batchJob) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Batch job not found" }
      }, request);
    }

    const batchJobData = batchJob as any;

    // Check access
    const hasAccess = await checkOrganizationAccess(batchJobData.organizationId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to batch job" }
      }, request);
    }

    logger.info("Batch job status retrieved successfully", {
      correlationId,
      batchJobId,
      status: batchJobData.status,
      progress: batchJobData.progress.percentage,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: batchJobData.id,
        name: batchJobData.name,
        processingType: batchJobData.processingType,
        status: batchJobData.status,
        progress: batchJobData.progress,
        results: batchJobData.results,
        createdAt: batchJobData.createdAt,
        startedAt: batchJobData.startedAt,
        completedAt: batchJobData.completedAt,
        estimatedCompletion: estimateCompletionTime(batchJobData)
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get batch job status failed", {
      correlationId,
      batchJobId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkOrganizationAccess(organizationId: string, userId: string): Promise<boolean> {
  try {
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
    return memberships.length > 0;
  } catch (error) {
    logger.error('Failed to check organization access', { error, organizationId, userId });
    return false;
  }
}

async function validateDocumentAccess(documentIds: string[], userId: string, organizationId: string): Promise<string[]> {
  try {
    const validDocuments: string[] = [];

    for (const documentId of documentIds) {
      const document = await db.readItem('documents', documentId, documentId);
      if (document && (document as any).organizationId === organizationId) {
        validDocuments.push(documentId);
      }
    }

    return validDocuments;
  } catch (error) {
    logger.error('Failed to validate document access', { error, documentIds, userId });
    return [];
  }
}

async function checkBatchProcessingLimits(organizationId: string, documentCount: number): Promise<{ allowed: boolean; reason?: string }> {
  try {
    // Get organization to check tier
    const organization = await db.readItem('organizations', organizationId, organizationId);
    if (!organization) {
      return { allowed: false, reason: 'Organization not found' };
    }

    const orgData = organization as any;
    const tier = orgData.tier || 'FREE';

    // Define tier limits
    const limits: { [key: string]: { maxDocuments: number; maxConcurrentJobs: number } } = {
      'FREE': { maxDocuments: 10, maxConcurrentJobs: 1 },
      'PROFESSIONAL': { maxDocuments: 100, maxConcurrentJobs: 5 },
      'ENTERPRISE': { maxDocuments: 1000, maxConcurrentJobs: 20 }
    };

    const limit = limits[tier] || limits['FREE'];

    // Check document count limit
    if (documentCount > limit.maxDocuments) {
      return {
        allowed: false,
        reason: `Document count exceeds tier limit of ${limit.maxDocuments}`
      };
    }

    // Check concurrent jobs limit
    const activeJobsQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.status IN (@processing, @pending)';
    const activeJobsResult = await db.queryItems('batch-jobs', activeJobsQuery, [organizationId, BatchStatus.PROCESSING, BatchStatus.PENDING]);
    const activeJobs = Number(activeJobsResult[0]) || 0;

    if (activeJobs >= limit.maxConcurrentJobs) {
      return {
        allowed: false,
        reason: `Maximum concurrent jobs limit reached (${limit.maxConcurrentJobs})`
      };
    }

    return { allowed: true };

  } catch (error) {
    logger.error('Failed to check batch processing limits', { error, organizationId, documentCount });
    return { allowed: false, reason: 'Failed to check limits' };
  }
}

function estimateProcessingDuration(documentCount: number, processingType: ProcessingType): string {
  // Simplified estimation - in production, use historical data
  const baseTimePerDocument = {
    [ProcessingType.TEXT_EXTRACTION]: 30, // seconds
    [ProcessingType.DOCUMENT_ANALYSIS]: 60,
    [ProcessingType.FORM_PROCESSING]: 45,
    [ProcessingType.CLASSIFICATION]: 20,
    [ProcessingType.ENTITY_EXTRACTION]: 40
  };

  const totalSeconds = documentCount * (baseTimePerDocument[processingType] || 30);
  const minutes = Math.ceil(totalSeconds / 60);

  if (minutes < 60) {
    return `${minutes} minutes`;
  } else {
    const hours = Math.ceil(minutes / 60);
    return `${hours} hours`;
  }
}

function estimateCompletionTime(batchJob: any): string | null {
  if (batchJob.status === BatchStatus.COMPLETED || batchJob.status === BatchStatus.FAILED) {
    return null;
  }

  if (batchJob.progress.processed === 0) {
    return estimateProcessingDuration(batchJob.progress.total, batchJob.processingType);
  }

  // Calculate based on current progress
  const startTime = new Date(batchJob.startedAt).getTime();
  const now = Date.now();
  const elapsedMs = now - startTime;
  const progressRate = batchJob.progress.processed / (elapsedMs / 1000); // documents per second
  const remainingDocuments = batchJob.progress.total - batchJob.progress.processed;
  const estimatedRemainingSeconds = remainingDocuments / progressRate;

  const minutes = Math.ceil(estimatedRemainingSeconds / 60);
  return `${minutes} minutes`;
}

async function processBatchJobAsync(batchJob: BatchJob): Promise<void> {
  // This would be implemented as a separate background process
  // For now, we'll just log that processing has started
  logger.info('Batch job processing started asynchronously', {
    batchJobId: batchJob.id,
    documentCount: batchJob.documentIds.length,
    processingType: batchJob.processingType
  });
}

// Register functions
app.http('batch-job-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'ai/batch-jobs',
  handler: createBatchJob
});

app.http('batch-job-status', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'ai/batch-jobs/{batchJobId}/status',
  handler: getBatchJobStatus
});

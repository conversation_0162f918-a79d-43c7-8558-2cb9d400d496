/**
 * Security Monitoring Function
 * Handles security monitoring, threat detection, and incident response
 * Migrated from old-arch/src/security-service/monitoring/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as <PERSON><PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { notificationService, NotificationChannelType } from '../shared/services/notification';
import { eventService } from '../shared/services/event';

// Security monitoring types and enums
enum ThreatType {
  BRUTE_FORCE_ATTACK = 'BRUTE_FORCE_ATTACK',
  SUSPICIOUS_LOGIN = 'SUSPICIOUS_LOGIN',
  UNUSUAL_ACCESS_PATTERN = 'UNUSUAL_ACCESS_PATTERN',
  DATA_EXFILTRATION = 'DATA_EXFILTRATION',
  PRIVILEGE_ESCALATION = 'PRIVILEGE_ESCALATION',
  MALWARE_DETECTED = 'MALWARE_DETECTED',
  PHISHING_ATTEMPT = 'PHISHING_ATTEMPT',
  UNAUTHORIZED_ACCESS = 'UNAUTHORIZED_ACCESS',
  ANOMALOUS_BEHAVIOR = 'ANOMALOUS_BEHAVIOR',
  COMPLIANCE_VIOLATION = 'COMPLIANCE_VIOLATION'
}

enum ThreatSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

enum IncidentStatus {
  OPEN = 'OPEN',
  INVESTIGATING = 'INVESTIGATING',
  CONTAINED = 'CONTAINED',
  RESOLVED = 'RESOLVED',
  CLOSED = 'CLOSED'
}

enum MonitoringRule {
  FAILED_LOGIN_THRESHOLD = 'FAILED_LOGIN_THRESHOLD',
  UNUSUAL_LOCATION = 'UNUSUAL_LOCATION',
  BULK_DOWNLOAD = 'BULK_DOWNLOAD',
  AFTER_HOURS_ACCESS = 'AFTER_HOURS_ACCESS',
  PRIVILEGE_CHANGE = 'PRIVILEGE_CHANGE',
  SENSITIVE_DATA_ACCESS = 'SENSITIVE_DATA_ACCESS'
}

// Validation schemas
const createSecurityIncidentSchema = Joi.object({
  threatType: Joi.string().valid(...Object.values(ThreatType)).required(),
  severity: Joi.string().valid(...Object.values(ThreatSeverity)).required(),
  title: Joi.string().min(5).max(200).required(),
  description: Joi.string().min(10).max(2000).required(),
  affectedResources: Joi.array().items(Joi.object({
    type: Joi.string().required(),
    id: Joi.string().required(),
    name: Joi.string().optional()
  })).optional(),
  indicators: Joi.array().items(Joi.object({
    type: Joi.string().required(),
    value: Joi.string().required(),
    confidence: Joi.number().min(0).max(1).required()
  })).optional(),
  metadata: Joi.object().optional()
});

const updateIncidentStatusSchema = Joi.object({
  incidentId: Joi.string().uuid().required(),
  status: Joi.string().valid(...Object.values(IncidentStatus)).required(),
  notes: Joi.string().max(1000).optional(),
  assignedTo: Joi.string().uuid().optional(),
  resolution: Joi.string().max(2000).optional()
});

interface CreateSecurityIncidentRequest {
  threatType: ThreatType;
  severity: ThreatSeverity;
  title: string;
  description: string;
  affectedResources?: Array<{
    type: string;
    id: string;
    name?: string;
  }>;
  indicators?: Array<{
    type: string;
    value: string;
    confidence: number;
  }>;
  metadata?: any;
}

interface SecurityIncident {
  id: string;
  threatType: ThreatType;
  severity: ThreatSeverity;
  status: IncidentStatus;
  title: string;
  description: string;
  affectedResources: any[];
  indicators: any[];
  metadata: any;
  detectedAt: string;
  reportedBy: string;
  assignedTo?: string;
  resolvedAt?: string;
  resolution?: string;
  organizationId?: string;
  tenantId: string;
}

interface ThreatDetectionResult {
  threatDetected: boolean;
  threatType?: ThreatType;
  severity?: ThreatSeverity;
  confidence: number;
  indicators: Array<{
    type: string;
    value: string;
    confidence: number;
  }>;
  recommendations: string[];
}

/**
 * Create security incident handler
 */
export async function createSecurityIncident(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create security incident started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Check security access
    const hasAccess = await checkSecurityAccess(user);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to security functions" }
      }, request);
    }

    // Validate request body
    const body = await request.json();
    const { error, value } = createSecurityIncidentSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const incidentRequest: CreateSecurityIncidentRequest = value;

    // Create security incident
    const incidentId = uuidv4();
    const now = new Date().toISOString();

    const incident: SecurityIncident = {
      id: incidentId,
      threatType: incidentRequest.threatType,
      severity: incidentRequest.severity,
      status: IncidentStatus.OPEN,
      title: incidentRequest.title,
      description: incidentRequest.description,
      affectedResources: incidentRequest.affectedResources || [],
      indicators: incidentRequest.indicators || [],
      metadata: {
        ...incidentRequest.metadata,
        reportedBy: user.email,
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      },
      detectedAt: now,
      reportedBy: user.id,
      organizationId: user.organizationId || user.tenantId || user.id,
      tenantId: user.tenantId || user.id
    };

    await db.createItem('security-incidents', incident);

    // Create audit log entry
    await db.createItem('audit-logs', {
      id: uuidv4(),
      eventType: 'SECURITY_INCIDENT_CREATED',
      userId: user.id,
      organizationId: user.organizationId || user.tenantId || user.id,
      description: `Security incident created: ${incidentRequest.title}`,
      severity: incidentRequest.severity,
      resourceType: 'security-incident',
      resourceId: incidentId,
      details: {
        threatType: incidentRequest.threatType,
        severity: incidentRequest.severity,
        affectedResourceCount: incidentRequest.affectedResources?.length || 0
      },
      timestamp: now,
      ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
      tenantId: user.tenantId
    });

    // Send notifications for high/critical incidents
    if (incidentRequest.severity === ThreatSeverity.HIGH || incidentRequest.severity === ThreatSeverity.CRITICAL) {
      await notifySecurityTeam(incident);
    }

    // Trigger automated response if applicable
    await triggerAutomatedResponse(incident);

    // Publish domain event
    await eventService.publishEvent({
      type: 'SecurityIncidentCreated',
      aggregateId: incidentId,
      aggregateType: 'SecurityIncident',
      version: 1,
      data: {
        incident,
        reportedBy: user.id
      },
      userId: user.id,
      organizationId: user.organizationId || user.tenantId || user.id,
      tenantId: user.tenantId || user.id
    });

    logger.info("Security incident created successfully", {
      correlationId,
      incidentId,
      threatType: incidentRequest.threatType,
      severity: incidentRequest.severity,
      reportedBy: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: incidentId,
        threatType: incidentRequest.threatType,
        severity: incidentRequest.severity,
        status: IncidentStatus.OPEN,
        detectedAt: now,
        message: "Security incident created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create security incident failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Analyze security threats handler
 */
export async function analyzeSecurityThreats(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Analyze security threats started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Check security access
    const hasAccess = await checkSecurityAccess(user);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to security functions" }
      }, request);
    }

    // Perform threat analysis
    const threatAnalysis = await performThreatAnalysis(user.organizationId || user.tenantId || user.id);

    // Get recent security incidents
    const recentIncidents = await getRecentSecurityIncidents(user.organizationId || user.tenantId || user.id);

    // Get security metrics
    const securityMetrics = await getSecurityMetrics(user.organizationId || user.tenantId || user.id);

    logger.info("Security threat analysis completed", {
      correlationId,
      threatsDetected: threatAnalysis.length,
      recentIncidents: recentIncidents.length,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        threatAnalysis,
        recentIncidents,
        securityMetrics,
        analysisTimestamp: new Date().toISOString(),
        summary: {
          totalThreats: threatAnalysis.length,
          highSeverityThreats: threatAnalysis.filter(t => t.severity === ThreatSeverity.HIGH || t.severity === ThreatSeverity.CRITICAL).length,
          openIncidents: recentIncidents.filter(i => i.status === IncidentStatus.OPEN).length,
          riskScore: calculateRiskScore(threatAnalysis, recentIncidents)
        }
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Analyze security threats failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkSecurityAccess(user: any): Promise<boolean> {
  try {
    // Check if user has security role
    if (user.roles?.includes('security') || user.roles?.includes('admin')) {
      return true;
    }

    // Check organization-level security permissions
    if (user.organizationId) {
      const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
      const memberships = await db.queryItems('organization-members', membershipQuery, [user.organizationId, user.id, 'ACTIVE']);

      if (memberships.length > 0) {
        const membership = memberships[0] as any;
        return membership.role === 'OWNER' || membership.role === 'ADMIN';
      }
    }

    return false;
  } catch (error) {
    logger.error('Failed to check security access', { error, userId: user.id });
    return false;
  }
}

async function performThreatAnalysis(organizationId: string): Promise<ThreatDetectionResult[]> {
  try {
    const threats: ThreatDetectionResult[] = [];

    // Analyze failed login attempts
    const failedLogins = await analyzeFailedLogins(organizationId);
    if (failedLogins.threatDetected) {
      threats.push(failedLogins);
    }

    // Analyze unusual access patterns
    const unusualAccess = await analyzeUnusualAccessPatterns(organizationId);
    if (unusualAccess.threatDetected) {
      threats.push(unusualAccess);
    }

    // Analyze bulk downloads
    const bulkDownloads = await analyzeBulkDownloads(organizationId);
    if (bulkDownloads.threatDetected) {
      threats.push(bulkDownloads);
    }

    // Analyze privilege escalations
    const privilegeEscalations = await analyzePrivilegeEscalations(organizationId);
    if (privilegeEscalations.threatDetected) {
      threats.push(privilegeEscalations);
    }

    return threats;

  } catch (error) {
    logger.error('Failed to perform threat analysis', { error, organizationId });
    return [];
  }
}

async function analyzeFailedLogins(organizationId: string): Promise<ThreatDetectionResult> {
  try {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();
    const query = 'SELECT * FROM c WHERE c.eventType = @eventType AND c.organizationId = @orgId AND c.timestamp >= @since';
    const failedLogins = await db.queryItems('audit-logs', query, ['USER_LOGIN_FAILED', organizationId, oneHourAgo]);

    const threshold = 10; // Failed logins per hour
    const threatDetected = failedLogins.length >= threshold;

    return {
      threatDetected,
      threatType: threatDetected ? ThreatType.BRUTE_FORCE_ATTACK : undefined,
      severity: threatDetected ? ThreatSeverity.HIGH : undefined,
      confidence: threatDetected ? Math.min(failedLogins.length / threshold, 1) : 0,
      indicators: threatDetected ? [
        {
          type: 'failed_login_count',
          value: failedLogins.length.toString(),
          confidence: 0.9
        }
      ] : [],
      recommendations: threatDetected ? [
        'Implement account lockout policies',
        'Enable multi-factor authentication',
        'Monitor IP addresses for blocking'
      ] : []
    };

  } catch (error) {
    logger.error('Failed to analyze failed logins', { error, organizationId });
    return {
      threatDetected: false,
      confidence: 0,
      indicators: [],
      recommendations: []
    };
  }
}

async function analyzeUnusualAccessPatterns(organizationId: string): Promise<ThreatDetectionResult> {
  try {
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
    const query = 'SELECT * FROM c WHERE c.eventType = @eventType AND c.organizationId = @orgId AND c.timestamp >= @since';
    const logins = await db.queryItems('audit-logs', query, ['USER_LOGIN', organizationId, twentyFourHoursAgo]);

    // Analyze for unusual patterns (simplified)
    const unusualPatterns = logins.filter((login: any) => {
      const hour = new Date(login.timestamp).getHours();
      return hour < 6 || hour > 22; // After hours access
    });

    const threatDetected = unusualPatterns.length > 5;

    return {
      threatDetected,
      threatType: threatDetected ? ThreatType.UNUSUAL_ACCESS_PATTERN : undefined,
      severity: threatDetected ? ThreatSeverity.MEDIUM : undefined,
      confidence: threatDetected ? 0.7 : 0,
      indicators: threatDetected ? [
        {
          type: 'after_hours_access',
          value: unusualPatterns.length.toString(),
          confidence: 0.7
        }
      ] : [],
      recommendations: threatDetected ? [
        'Review after-hours access policies',
        'Implement time-based access controls',
        'Monitor user behavior patterns'
      ] : []
    };

  } catch (error) {
    logger.error('Failed to analyze unusual access patterns', { error, organizationId });
    return {
      threatDetected: false,
      confidence: 0,
      indicators: [],
      recommendations: []
    };
  }
}

async function analyzeBulkDownloads(organizationId: string): Promise<ThreatDetectionResult> {
  try {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();
    const query = 'SELECT * FROM c WHERE c.eventType = @eventType AND c.organizationId = @orgId AND c.timestamp >= @since';
    const downloads = await db.queryItems('audit-logs', query, ['DOCUMENT_DOWNLOADED', organizationId, oneHourAgo]);

    // Group by user and count downloads
    const downloadsByUser = downloads.reduce((acc: any, download: any) => {
      acc[download.userId] = (acc[download.userId] || 0) + 1;
      return acc;
    }, {});

    const bulkDownloadThreshold = 20;
    const suspiciousUsers = Object.entries(downloadsByUser as Record<string, number>).filter(([_, count]) => count >= bulkDownloadThreshold);

    const threatDetected = suspiciousUsers.length > 0;

    return {
      threatDetected,
      threatType: threatDetected ? ThreatType.DATA_EXFILTRATION : undefined,
      severity: threatDetected ? ThreatSeverity.HIGH : undefined,
      confidence: threatDetected ? 0.8 : 0,
      indicators: threatDetected ? suspiciousUsers.map(([userId, count]) => ({
        type: 'bulk_download',
        value: `User ${userId}: ${count} downloads`,
        confidence: 0.8
      })) : [],
      recommendations: threatDetected ? [
        'Investigate suspicious download activity',
        'Implement download rate limiting',
        'Review data loss prevention policies'
      ] : []
    };

  } catch (error) {
    logger.error('Failed to analyze bulk downloads', { error, organizationId });
    return {
      threatDetected: false,
      confidence: 0,
      indicators: [],
      recommendations: []
    };
  }
}

async function analyzePrivilegeEscalations(organizationId: string): Promise<ThreatDetectionResult> {
  try {
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
    const query = 'SELECT * FROM c WHERE c.eventType IN (@event1, @event2) AND c.organizationId = @orgId AND c.timestamp >= @since';
    const privilegeChanges = await db.queryItems('audit-logs', query, ['PERMISSION_GRANTED', 'ROLE_ASSIGNED', organizationId, twentyFourHoursAgo]);

    const threatDetected = privilegeChanges.length > 10; // Threshold for suspicious privilege changes

    return {
      threatDetected,
      threatType: threatDetected ? ThreatType.PRIVILEGE_ESCALATION : undefined,
      severity: threatDetected ? ThreatSeverity.MEDIUM : undefined,
      confidence: threatDetected ? 0.6 : 0,
      indicators: threatDetected ? [
        {
          type: 'privilege_changes',
          value: privilegeChanges.length.toString(),
          confidence: 0.6
        }
      ] : [],
      recommendations: threatDetected ? [
        'Review recent privilege changes',
        'Implement approval workflows for privilege escalation',
        'Monitor administrative actions'
      ] : []
    };

  } catch (error) {
    logger.error('Failed to analyze privilege escalations', { error, organizationId });
    return {
      threatDetected: false,
      confidence: 0,
      indicators: [],
      recommendations: []
    };
  }
}

async function getRecentSecurityIncidents(organizationId: string): Promise<any[]> {
  try {
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();
    const query = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.detectedAt >= @since ORDER BY c.detectedAt DESC';
    return await db.queryItems('security-incidents', query, [organizationId, sevenDaysAgo]);
  } catch (error) {
    logger.error('Failed to get recent security incidents', { error, organizationId });
    return [];
  }
}

async function getSecurityMetrics(organizationId: string): Promise<any> {
  try {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();

    // Get various security metrics
    const [
      totalIncidents,
      resolvedIncidents,
      failedLogins,
      successfulLogins
    ] = await Promise.all([
      db.queryItems('security-incidents', 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.detectedAt >= @since', [organizationId, thirtyDaysAgo]),
      db.queryItems('security-incidents', 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.status = @status AND c.detectedAt >= @since', [organizationId, IncidentStatus.RESOLVED, thirtyDaysAgo]),
      db.queryItems('audit-logs', 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.eventType = @eventType AND c.timestamp >= @since', [organizationId, 'USER_LOGIN_FAILED', thirtyDaysAgo]),
      db.queryItems('audit-logs', 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.eventType = @eventType AND c.timestamp >= @since', [organizationId, 'USER_LOGIN', thirtyDaysAgo])
    ]);

    return {
      totalIncidents: Number(totalIncidents[0]) || 0,
      resolvedIncidents: Number(resolvedIncidents[0]) || 0,
      failedLogins: Number(failedLogins[0]) || 0,
      successfulLogins: Number(successfulLogins[0]) || 0,
      resolutionRate: Number(totalIncidents[0]) > 0 ? Number(resolvedIncidents[0]) / Number(totalIncidents[0]) : 0,
      loginSuccessRate: (Number(successfulLogins[0]) + Number(failedLogins[0])) > 0 ?
        Number(successfulLogins[0]) / (Number(successfulLogins[0]) + Number(failedLogins[0])) : 0
    };

  } catch (error) {
    logger.error('Failed to get security metrics', { error, organizationId });
    return {
      totalIncidents: 0,
      resolvedIncidents: 0,
      failedLogins: 0,
      successfulLogins: 0,
      resolutionRate: 0,
      loginSuccessRate: 0
    };
  }
}

function calculateRiskScore(threats: ThreatDetectionResult[], incidents: any[]): number {
  try {
    let riskScore = 0;

    // Add risk based on detected threats
    threats.forEach(threat => {
      if (threat.severity === ThreatSeverity.CRITICAL) riskScore += 40;
      else if (threat.severity === ThreatSeverity.HIGH) riskScore += 25;
      else if (threat.severity === ThreatSeverity.MEDIUM) riskScore += 15;
      else riskScore += 5;
    });

    // Add risk based on open incidents
    const openIncidents = incidents.filter(i => i.status === IncidentStatus.OPEN);
    riskScore += openIncidents.length * 10;

    // Normalize to 0-100 scale
    return Math.min(riskScore, 100);

  } catch (error) {
    logger.error('Failed to calculate risk score', { error });
    return 0;
  }
}

async function notifySecurityTeam(incident: SecurityIncident): Promise<void> {
  try {
    // Send notification to security team
    await notificationService.sendNotification({
      userId: 'security-team', // This would be resolved to actual security team members
      type: 'security_incident',
      title: `Security Incident: ${incident.title}`,
      message: `A ${incident.severity} severity security incident has been detected: ${incident.description}`,
      resourceId: incident.id,
      resourceType: 'security-incident',
      metadata: {
        incidentId: incident.id,
        threatType: incident.threatType,
        severity: incident.severity
      },
      channels: [NotificationChannelType.EMAIL, NotificationChannelType.SLACK], // Send via multiple channels for critical incidents
      priority: incident.severity === ThreatSeverity.CRITICAL ? 'urgent' : 'high'
    });

  } catch (error) {
    logger.error('Failed to notify security team', { error, incidentId: incident.id });
  }
}

async function triggerAutomatedResponse(incident: SecurityIncident): Promise<void> {
  try {
    // Implement automated response based on threat type
    switch (incident.threatType) {
      case ThreatType.BRUTE_FORCE_ATTACK:
        // Could trigger IP blocking, account lockout, etc.
        logger.info('Automated response triggered for brute force attack', { incidentId: incident.id });
        break;

      case ThreatType.DATA_EXFILTRATION:
        // Could trigger access revocation, download blocking, etc.
        logger.info('Automated response triggered for data exfiltration', { incidentId: incident.id });
        break;

      default:
        logger.info('No automated response configured for threat type', {
          incidentId: incident.id,
          threatType: incident.threatType
        });
    }

  } catch (error) {
    logger.error('Failed to trigger automated response', { error, incidentId: incident.id });
  }
}

// Register functions
app.http('security-incident-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'security/incidents',
  handler: createSecurityIncident
});

app.http('security-threats-analyze', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'security/threats/analyze',
  handler: analyzeSecurityThreats
});

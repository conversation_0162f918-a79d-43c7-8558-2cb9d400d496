/**
 * Analytics Function
 * Handles analytics and reporting operations
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Validation schemas
const getAnalyticsSchema = Joi.object({
  type: Joi.string().valid('documents', 'workflows', 'users', 'activities', 'overview').required(),
  period: Joi.string().valid('day', 'week', 'month', 'quarter', 'year').default('month'),
  startDate: Joi.date().iso().optional(),
  endDate: Joi.date().iso().optional(),
  organizationId: Joi.string().uuid().optional(),
  projectId: Joi.string().uuid().optional(),
  groupBy: Joi.string().valid('day', 'week', 'month', 'type', 'status', 'user').optional()
});

interface AnalyticsRequest {
  type: 'documents' | 'workflows' | 'users' | 'activities' | 'overview';
  period: 'day' | 'week' | 'month' | 'quarter' | 'year';
  startDate?: string;
  endDate?: string;
  organizationId?: string;
  projectId?: string;
  groupBy?: string;
}

/**
 * Get analytics data handler
 */
export async function getAnalytics(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Get analytics started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate query parameters
    const queryParams = Object.fromEntries(request.query.entries());
    const { error, value } = getAnalyticsSchema.validate(queryParams);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const analyticsRequest: AnalyticsRequest = value;

    // Calculate date range
    const dateRange = calculateDateRange(analyticsRequest.period, analyticsRequest.startDate, analyticsRequest.endDate);

    // Get analytics data based on type
    let analyticsData;
    switch (analyticsRequest.type) {
      case 'documents':
        analyticsData = await getDocumentAnalytics(dateRange, analyticsRequest, user);
        break;
      case 'workflows':
        analyticsData = await getWorkflowAnalytics(dateRange, analyticsRequest, user);
        break;
      case 'users':
        analyticsData = await getUserAnalytics(dateRange, analyticsRequest, user);
        break;
      case 'activities':
        analyticsData = await getActivityAnalytics(dateRange, analyticsRequest, user);
        break;
      case 'overview':
        analyticsData = await getOverviewAnalytics(dateRange, analyticsRequest, user);
        break;
      default:
        return addCorsHeaders({
          status: 400,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { error: "Invalid analytics type" }
        }, request);
    }

    logger.info("Analytics data retrieved successfully", {
      correlationId,
      type: analyticsRequest.type,
      period: analyticsRequest.period,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        type: analyticsRequest.type,
        period: analyticsRequest.period,
        dateRange,
        data: analyticsData,
        generatedAt: new Date().toISOString()
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get analytics failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Calculate date range based on period
 */
function calculateDateRange(period: string, startDate?: string, endDate?: string): { start: string; end: string } {
  const now = new Date();
  const end = endDate ? new Date(endDate) : now;
  let start: Date;

  if (startDate) {
    start = new Date(startDate);
  } else {
    switch (period) {
      case 'day':
        start = new Date(end.getTime() - 24 * 60 * 60 * 1000);
        break;
      case 'week':
        start = new Date(end.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        start = new Date(end.getFullYear(), end.getMonth() - 1, end.getDate());
        break;
      case 'quarter':
        start = new Date(end.getFullYear(), end.getMonth() - 3, end.getDate());
        break;
      case 'year':
        start = new Date(end.getFullYear() - 1, end.getMonth(), end.getDate());
        break;
      default:
        start = new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000);
    }
  }

  return {
    start: start.toISOString(),
    end: end.toISOString()
  };
}

/**
 * Get document analytics
 */
async function getDocumentAnalytics(dateRange: any, request: AnalyticsRequest, user: any): Promise<any> {
  let query = 'SELECT * FROM c WHERE c.createdAt >= @startDate AND c.createdAt <= @endDate';
  const parameters = [dateRange.start, dateRange.end];

  // Add tenant isolation
  if (user.tenantId) {
    query += ' AND (c.organizationId = @tenantId OR c.createdBy = @userId)';
    parameters.push(user.tenantId, user.id);
  }

  // Add filters
  if (request.organizationId) {
    query += ' AND c.organizationId = @organizationId';
    parameters.push(request.organizationId);
  }

  if (request.projectId) {
    query += ' AND c.projectId = @projectId';
    parameters.push(request.projectId);
  }

  const documents = await db.queryItems('documents', query, parameters);

  // Calculate metrics
  const totalDocuments = documents.length;
  const documentsByType = documents.reduce((acc: any, doc: any) => {
    const type = doc.contentType || 'unknown';
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {});

  const documentsByStatus = documents.reduce((acc: any, doc: any) => {
    const status = doc.status || 'unknown';
    acc[status] = (acc[status] || 0) + 1;
    return acc;
  }, {});

  const averageSize = documents.length > 0
    ? documents.reduce((sum: number, doc: any) => sum + (doc.size || 0), 0) / documents.length
    : 0;

  return {
    totalDocuments,
    documentsByType,
    documentsByStatus,
    averageSize: Math.round(averageSize),
    recentDocuments: documents.slice(0, 10).map((doc: any) => ({
      id: doc.id,
      name: doc.name,
      type: doc.contentType,
      size: doc.size,
      createdAt: doc.createdAt
    }))
  };
}

/**
 * Get workflow analytics
 */
async function getWorkflowAnalytics(dateRange: any, request: AnalyticsRequest, user: any): Promise<any> {
  let query = 'SELECT * FROM c WHERE c.createdAt >= @startDate AND c.createdAt <= @endDate';
  const parameters = [dateRange.start, dateRange.end];

  // Add tenant isolation
  if (user.tenantId) {
    query += ' AND (c.organizationId = @tenantId OR c.createdBy = @userId)';
    parameters.push(user.tenantId, user.id);
  }

  const workflows = await db.queryItems('workflows', query, parameters);

  const totalWorkflows = workflows.length;
  const workflowsByStatus = workflows.reduce((acc: any, workflow: any) => {
    const status = workflow.status || 'unknown';
    acc[status] = (acc[status] || 0) + 1;
    return acc;
  }, {});

  const completedWorkflows = workflows.filter((w: any) => w.status === 'COMPLETED');
  const averageCompletionTime = completedWorkflows.length > 0
    ? completedWorkflows.reduce((sum: number, w: any) => {
        const start = new Date(w.startedAt || w.createdAt);
        const end = new Date(w.completedAt);
        return sum + (end.getTime() - start.getTime());
      }, 0) / completedWorkflows.length
    : 0;

  return {
    totalWorkflows,
    workflowsByStatus,
    completedWorkflows: completedWorkflows.length,
    averageCompletionTimeHours: Math.round(averageCompletionTime / (1000 * 60 * 60)),
    activeWorkflows: workflows.filter((w: any) => w.status === 'ACTIVE').length
  };
}

/**
 * Get user analytics
 */
async function getUserAnalytics(dateRange: any, request: AnalyticsRequest, user: any): Promise<any> {
  // Get user activity from activities
  let query = 'SELECT * FROM c WHERE c.timestamp >= @startDate AND c.timestamp <= @endDate';
  const parameters = [dateRange.start, dateRange.end];

  // Add tenant isolation
  if (user.tenantId) {
    query += ' AND c.tenantId = @tenantId';
    parameters.push(user.tenantId);
  }

  const activities = await db.queryItems('activities', query, parameters);

  const userActivity = activities.reduce((acc: any, activity: any) => {
    const userId = activity.userId;
    if (!acc[userId]) {
      acc[userId] = { count: 0, types: {} };
    }
    acc[userId].count++;
    acc[userId].types[activity.type] = (acc[userId].types[activity.type] || 0) + 1;
    return acc;
  }, {});

  const mostActiveUsers = Object.entries(userActivity as Record<string, any>)
    .sort(([,a]: any, [,b]: any) => b.count - a.count)
    .slice(0, 10)
    .map(([userId, data]: any) => ({ userId, ...data }));

  return {
    totalActivities: activities.length,
    uniqueActiveUsers: Object.keys(userActivity as Record<string, any>).length,
    mostActiveUsers,
    activityByType: activities.reduce((acc: any, activity: any) => {
      acc[activity.type] = (acc[activity.type] || 0) + 1;
      return acc;
    }, {})
  };
}

/**
 * Get activity analytics
 */
async function getActivityAnalytics(dateRange: any, request: AnalyticsRequest, user: any): Promise<any> {
  let query = 'SELECT * FROM c WHERE c.timestamp >= @startDate AND c.timestamp <= @endDate';
  const parameters = [dateRange.start, dateRange.end];

  // Add tenant isolation
  if (user.tenantId) {
    query += ' AND c.tenantId = @tenantId';
    parameters.push(user.tenantId);
  }

  const activities = await db.queryItems('activities', query, parameters);

  const activitiesByType = activities.reduce((acc: any, activity: any) => {
    acc[activity.type] = (acc[activity.type] || 0) + 1;
    return acc;
  }, {});

  const activitiesByDay = activities.reduce((acc: any, activity: any) => {
    const day = activity.timestamp.split('T')[0];
    acc[day] = (acc[day] || 0) + 1;
    return acc;
  }, {});

  return {
    totalActivities: activities.length,
    activitiesByType,
    activitiesByDay,
    recentActivities: activities.slice(0, 20).map((activity: any) => ({
      type: activity.type,
      userId: activity.userId,
      timestamp: activity.timestamp,
      details: activity.details
    }))
  };
}

/**
 * Get overview analytics
 */
async function getOverviewAnalytics(dateRange: any, request: AnalyticsRequest, user: any): Promise<any> {
  const [documents, workflows, activities] = await Promise.all([
    getDocumentAnalytics(dateRange, request, user),
    getWorkflowAnalytics(dateRange, request, user),
    getActivityAnalytics(dateRange, request, user)
  ]);

  return {
    summary: {
      totalDocuments: documents.totalDocuments,
      totalWorkflows: workflows.totalWorkflows,
      totalActivities: activities.totalActivities,
      activeWorkflows: workflows.activeWorkflows
    },
    documents: {
      byType: documents.documentsByType,
      byStatus: documents.documentsByStatus
    },
    workflows: {
      byStatus: workflows.workflowsByStatus,
      averageCompletionTimeHours: workflows.averageCompletionTimeHours
    },
    activities: {
      byType: activities.activitiesByType,
      trend: activities.activitiesByDay
    }
  };
}

// Register functions
app.http('analytics-get', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'analytics',
  handler: getAnalytics
});

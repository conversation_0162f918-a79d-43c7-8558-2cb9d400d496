/**
 * Project Analytics Function
 * Handles project analytics, reporting, and performance metrics
 * Migrated from old-arch/src/project-service/analytics/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Analytics types and enums
enum AnalyticsPeriod {
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  QUARTER = 'quarter',
  YEAR = 'year'
}

enum MetricType {
  DOCUMENTS = 'documents',
  WORKFLOWS = 'workflows',
  MEMBERS = 'members',
  ACTIVITIES = 'activities',
  STORAGE = 'storage'
}

// Validation schema
const getProjectAnalyticsSchema = Joi.object({
  projectId: Joi.string().uuid().required(),
  period: Joi.string().valid(...Object.values(AnalyticsPeriod)).default(AnalyticsPeriod.MONTH),
  startDate: Joi.string().isoDate().optional(),
  endDate: Joi.string().isoDate().optional(),
  metrics: Joi.array().items(Joi.string().valid(...Object.values(MetricType))).optional(),
  includeComparisons: Joi.boolean().default(true),
  includeTrends: Joi.boolean().default(true),
  includeBreakdowns: Joi.boolean().default(true)
});

interface ProjectAnalyticsRequest {
  projectId: string;
  period: AnalyticsPeriod;
  startDate?: string;
  endDate?: string;
  metrics?: MetricType[];
  includeComparisons?: boolean;
  includeTrends?: boolean;
  includeBreakdowns?: boolean;
}

interface ProjectAnalyticsResponse {
  projectId: string;
  projectName: string;
  period: AnalyticsPeriod;
  dateRange: {
    start: string;
    end: string;
  };
  summary: {
    totalDocuments: number;
    totalWorkflows: number;
    totalMembers: number;
    totalActivities: number;
    storageUsedMB: number;
    activeUsers: number;
    completedWorkflows: number;
    averageProcessingTime: number;
  };
  trends?: {
    documentTrend: Array<{
      date: string;
      count: number;
      uploads: number;
      processed: number;
    }>;
    workflowTrend: Array<{
      date: string;
      started: number;
      completed: number;
      failed: number;
    }>;
    activityTrend: Array<{
      date: string;
      activities: number;
      uniqueUsers: number;
    }>;
    memberTrend: Array<{
      date: string;
      totalMembers: number;
      activeMembers: number;
      newMembers: number;
    }>;
  };
  breakdowns?: {
    documentsByType: Array<{
      type: string;
      count: number;
      percentage: number;
    }>;
    workflowsByStatus: Array<{
      status: string;
      count: number;
      percentage: number;
    }>;
    activitiesByType: Array<{
      type: string;
      count: number;
      percentage: number;
    }>;
    membersByRole: Array<{
      role: string;
      count: number;
      percentage: number;
    }>;
  };
  comparisons?: {
    previousPeriod: {
      documents: { current: number; previous: number; change: number };
      workflows: { current: number; previous: number; change: number };
      activities: { current: number; previous: number; change: number };
      members: { current: number; previous: number; change: number };
    };
  };
  performance: {
    averageDocumentProcessingTime: number;
    averageWorkflowCompletionTime: number;
    userEngagementScore: number;
    projectHealthScore: number;
  };
}

/**
 * Get project analytics handler
 */
export async function getProjectAnalytics(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Get project analytics started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Parse query parameters
    const url = new URL(request.url);
    const queryParams = {
      projectId: url.searchParams.get('projectId') || '',
      period: url.searchParams.get('period') || AnalyticsPeriod.MONTH,
      startDate: url.searchParams.get('startDate') || undefined,
      endDate: url.searchParams.get('endDate') || undefined,
      metrics: url.searchParams.get('metrics')?.split(',') || undefined,
      includeComparisons: url.searchParams.get('includeComparisons') !== 'false',
      includeTrends: url.searchParams.get('includeTrends') !== 'false',
      includeBreakdowns: url.searchParams.get('includeBreakdowns') !== 'false'
    };

    // Validate request
    const { error, value } = getProjectAnalyticsSchema.validate(queryParams);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const analyticsRequest: ProjectAnalyticsRequest = value;

    // Verify project exists and user has access
    const project = await db.readItem('projects', analyticsRequest.projectId, analyticsRequest.projectId);
    if (!project) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Project not found" }
      }, request);
    }

    // Check if user has analytics access
    const hasAccess = await checkProjectAnalyticsAccess(analyticsRequest.projectId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to project analytics" }
      }, request);
    }

    const projectData = project as any;

    // Calculate date range
    const dateRange = calculateDateRange(analyticsRequest.period, analyticsRequest.startDate, analyticsRequest.endDate);

    // Get analytics data
    const analyticsData = await getProjectAnalyticsData(analyticsRequest, dateRange);

    const response: ProjectAnalyticsResponse = {
      projectId: analyticsRequest.projectId,
      projectName: projectData.name,
      period: analyticsRequest.period,
      dateRange,
      summary: analyticsData.summary,
      trends: analyticsRequest.includeTrends ? analyticsData.trends : undefined,
      breakdowns: analyticsRequest.includeBreakdowns ? analyticsData.breakdowns : undefined,
      comparisons: analyticsRequest.includeComparisons ? analyticsData.comparisons : undefined,
      performance: analyticsData.performance
    };

    logger.info("Project analytics retrieved successfully", {
      correlationId,
      projectId: analyticsRequest.projectId,
      period: analyticsRequest.period,
      totalDocuments: analyticsData.summary.totalDocuments,
      totalWorkflows: analyticsData.summary.totalWorkflows,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: response
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get project analytics failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Calculate date range based on period
 */
function calculateDateRange(period: AnalyticsPeriod, startDate?: string, endDate?: string): { start: string; end: string } {
  const now = new Date();
  const end = endDate ? new Date(endDate) : now;
  let start: Date;

  if (startDate) {
    start = new Date(startDate);
  } else {
    switch (period) {
      case AnalyticsPeriod.DAY:
        start = new Date(end.getTime() - 24 * 60 * 60 * 1000); // 1 day ago
        break;
      case AnalyticsPeriod.WEEK:
        start = new Date(end.getTime() - 7 * 24 * 60 * 60 * 1000); // 1 week ago
        break;
      case AnalyticsPeriod.MONTH:
        start = new Date(end.getFullYear(), end.getMonth() - 1, end.getDate()); // 1 month ago
        break;
      case AnalyticsPeriod.QUARTER:
        start = new Date(end.getFullYear(), end.getMonth() - 3, end.getDate()); // 3 months ago
        break;
      case AnalyticsPeriod.YEAR:
        start = new Date(end.getFullYear() - 1, end.getMonth(), end.getDate()); // 1 year ago
        break;
      default:
        start = new Date(end.getFullYear(), end.getMonth() - 1, end.getDate()); // Default to 1 month
    }
  }

  return {
    start: start.toISOString(),
    end: end.toISOString()
  };
}

/**
 * Check project analytics access
 */
async function checkProjectAnalyticsAccess(projectId: string, userId: string): Promise<boolean> {
  try {
    // Check if user is a member of the project
    const membershipQuery = 'SELECT * FROM c WHERE c.projectId = @projectId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('project-members', membershipQuery, [projectId, userId, 'ACTIVE']);
    
    return memberships.length > 0;
  } catch (error) {
    logger.error('Failed to check project analytics access', { error, projectId, userId });
    return false;
  }
}

/**
 * Get project analytics data
 */
async function getProjectAnalyticsData(
  request: ProjectAnalyticsRequest,
  dateRange: { start: string; end: string }
): Promise<any> {
  try {
    // Build base queries
    const baseDocumentQuery = 'SELECT * FROM c WHERE c.projectId = @projectId AND c.createdAt >= @startDate AND c.createdAt <= @endDate';
    const baseWorkflowQuery = 'SELECT * FROM c WHERE c.projectId = @projectId AND c.createdAt >= @startDate AND c.createdAt <= @endDate';
    const baseActivityQuery = 'SELECT * FROM c WHERE c.projectId = @projectId AND c.timestamp >= @startDate AND c.timestamp <= @endDate';
    const baseMemberQuery = 'SELECT * FROM c WHERE c.projectId = @projectId AND c.status = @status';

    const parameters = [request.projectId, dateRange.start, dateRange.end];

    // Get data from different collections
    const [documents, workflows, activities, members] = await Promise.all([
      db.queryItems('documents', baseDocumentQuery, parameters),
      db.queryItems('workflow-executions', baseWorkflowQuery, parameters),
      db.queryItems('activities', baseActivityQuery, parameters),
      db.queryItems('project-members', baseMemberQuery, [request.projectId, 'ACTIVE'])
    ]);

    // Calculate summary statistics
    const summary = calculateSummaryStatistics(documents, workflows, activities, members);

    // Calculate trends if requested
    const trends = request.includeTrends ? calculateTrends(documents, workflows, activities, members, dateRange) : undefined;

    // Calculate breakdowns if requested
    const breakdowns = request.includeBreakdowns ? calculateBreakdowns(documents, workflows, activities, members) : undefined;

    // Calculate comparisons if requested
    const comparisons = request.includeComparisons ? await calculateComparisons(request, dateRange) : undefined;

    // Calculate performance metrics
    const performance = calculatePerformanceMetrics(documents, workflows, activities, members);

    return {
      summary,
      trends,
      breakdowns,
      comparisons,
      performance
    };

  } catch (error) {
    logger.error('Failed to get project analytics data', { error, request });
    throw error;
  }
}

/**
 * Calculate summary statistics
 */
function calculateSummaryStatistics(documents: any[], workflows: any[], activities: any[], members: any[]): any {
  const totalDocuments = documents.length;
  const totalWorkflows = workflows.length;
  const totalMembers = members.length;
  const totalActivities = activities.length;

  // Calculate storage used (simplified)
  const storageUsedMB = Math.round(documents.reduce((sum, doc) => sum + (doc.size || 0), 0) / (1024 * 1024));

  // Calculate active users (users with activities in the period)
  const activeUserIds = new Set(activities.map(a => a.userId));
  const activeUsers = activeUserIds.size;

  // Calculate completed workflows
  const completedWorkflows = workflows.filter(w => w.status === 'COMPLETED').length;

  // Calculate average processing time for completed workflows
  const completedWithDuration = workflows.filter(w => 
    w.status === 'COMPLETED' && w.startedAt && w.completedAt
  );

  const totalDuration = completedWithDuration.reduce((sum, w) => {
    const duration = new Date(w.completedAt).getTime() - new Date(w.startedAt).getTime();
    return sum + duration;
  }, 0);

  const averageProcessingTime = completedWithDuration.length > 0 
    ? Math.round(totalDuration / completedWithDuration.length / 1000) // Convert to seconds
    : 0;

  return {
    totalDocuments,
    totalWorkflows,
    totalMembers,
    totalActivities,
    storageUsedMB,
    activeUsers,
    completedWorkflows,
    averageProcessingTime
  };
}

/**
 * Calculate trends
 */
function calculateTrends(documents: any[], workflows: any[], activities: any[], members: any[], dateRange: any): any {
  // Group data by date
  const dailyStats: { [date: string]: any } = {};

  // Initialize daily stats
  const start = new Date(dateRange.start);
  const end = new Date(dateRange.end);
  for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
    const dateKey = d.toISOString().split('T')[0];
    dailyStats[dateKey] = {
      documents: { count: 0, uploads: 0, processed: 0 },
      workflows: { started: 0, completed: 0, failed: 0 },
      activities: { activities: 0, uniqueUsers: new Set() },
      members: { totalMembers: 0, activeMembers: new Set(), newMembers: 0 }
    };
  }

  // Process documents
  documents.forEach(doc => {
    const date = new Date(doc.createdAt).toISOString().split('T')[0];
    if (dailyStats[date]) {
      dailyStats[date].documents.count++;
      dailyStats[date].documents.uploads++;
      if (doc.status === 'PROCESSED') {
        dailyStats[date].documents.processed++;
      }
    }
  });

  // Process workflows
  workflows.forEach(workflow => {
    const date = new Date(workflow.createdAt).toISOString().split('T')[0];
    if (dailyStats[date]) {
      dailyStats[date].workflows.started++;
      if (workflow.status === 'COMPLETED') {
        dailyStats[date].workflows.completed++;
      } else if (workflow.status === 'FAILED') {
        dailyStats[date].workflows.failed++;
      }
    }
  });

  // Process activities
  activities.forEach(activity => {
    const date = new Date(activity.timestamp).toISOString().split('T')[0];
    if (dailyStats[date]) {
      dailyStats[date].activities.activities++;
      dailyStats[date].activities.uniqueUsers.add(activity.userId);
    }
  });

  // Convert to arrays
  const documentTrend = Object.entries(dailyStats).map(([date, stats]) => ({
    date,
    count: (stats as any).documents.count,
    uploads: (stats as any).documents.uploads,
    processed: (stats as any).documents.processed
  }));

  const workflowTrend = Object.entries(dailyStats).map(([date, stats]) => ({
    date,
    started: (stats as any).workflows.started,
    completed: (stats as any).workflows.completed,
    failed: (stats as any).workflows.failed
  }));

  const activityTrend = Object.entries(dailyStats).map(([date, stats]) => ({
    date,
    activities: (stats as any).activities.activities,
    uniqueUsers: (stats as any).activities.uniqueUsers.size
  }));

  const memberTrend = Object.entries(dailyStats).map(([date, stats]) => ({
    date,
    totalMembers: members.length, // Simplified - would need historical data
    activeMembers: (stats as any).activities.uniqueUsers.size,
    newMembers: 0 // Simplified - would need member creation dates
  }));

  return {
    documentTrend,
    workflowTrend,
    activityTrend,
    memberTrend
  };
}

/**
 * Calculate breakdowns
 */
function calculateBreakdowns(documents: any[], workflows: any[], activities: any[], members: any[]): any {
  // Documents by type
  const documentTypes: { [type: string]: number } = {};
  documents.forEach(doc => {
    const type = doc.contentType?.split('/')[0] || 'unknown';
    documentTypes[type] = (documentTypes[type] || 0) + 1;
  });

  const documentsByType = Object.entries(documentTypes).map(([type, count]) => ({
    type,
    count,
    percentage: Math.round((count / documents.length) * 100)
  }));

  // Workflows by status
  const workflowStatuses: { [status: string]: number } = {};
  workflows.forEach(workflow => {
    const status = workflow.status || 'unknown';
    workflowStatuses[status] = (workflowStatuses[status] || 0) + 1;
  });

  const workflowsByStatus = Object.entries(workflowStatuses).map(([status, count]) => ({
    status,
    count,
    percentage: Math.round((count / workflows.length) * 100)
  }));

  // Activities by type
  const activityTypes: { [type: string]: number } = {};
  activities.forEach(activity => {
    const type = activity.type || 'unknown';
    activityTypes[type] = (activityTypes[type] || 0) + 1;
  });

  const activitiesByType = Object.entries(activityTypes).map(([type, count]) => ({
    type,
    count,
    percentage: Math.round((count / activities.length) * 100)
  }));

  // Members by role
  const memberRoles: { [role: string]: number } = {};
  members.forEach(member => {
    const role = member.role || 'unknown';
    memberRoles[role] = (memberRoles[role] || 0) + 1;
  });

  const membersByRole = Object.entries(memberRoles).map(([role, count]) => ({
    role,
    count,
    percentage: Math.round((count / members.length) * 100)
  }));

  return {
    documentsByType,
    workflowsByStatus,
    activitiesByType,
    membersByRole
  };
}

/**
 * Calculate comparisons with previous period
 */
async function calculateComparisons(request: ProjectAnalyticsRequest, dateRange: any): Promise<any> {
  // Calculate previous period date range
  const periodLength = new Date(dateRange.end).getTime() - new Date(dateRange.start).getTime();
  const previousStart = new Date(new Date(dateRange.start).getTime() - periodLength);
  const previousEnd = new Date(dateRange.start);

  const previousDateRange = {
    start: previousStart.toISOString(),
    end: previousEnd.toISOString()
  };

  // Get previous period data (simplified)
  const previousData = await getProjectAnalyticsData(
    { ...request, includeComparisons: false, includeTrends: false, includeBreakdowns: false },
    previousDateRange
  );

  const currentData = await getProjectAnalyticsData(
    { ...request, includeComparisons: false, includeTrends: false, includeBreakdowns: false },
    dateRange
  );

  return {
    previousPeriod: {
      documents: {
        current: currentData.summary.totalDocuments,
        previous: previousData.summary.totalDocuments,
        change: currentData.summary.totalDocuments - previousData.summary.totalDocuments
      },
      workflows: {
        current: currentData.summary.totalWorkflows,
        previous: previousData.summary.totalWorkflows,
        change: currentData.summary.totalWorkflows - previousData.summary.totalWorkflows
      },
      activities: {
        current: currentData.summary.totalActivities,
        previous: previousData.summary.totalActivities,
        change: currentData.summary.totalActivities - previousData.summary.totalActivities
      },
      members: {
        current: currentData.summary.totalMembers,
        previous: previousData.summary.totalMembers,
        change: currentData.summary.totalMembers - previousData.summary.totalMembers
      }
    }
  };
}

/**
 * Calculate performance metrics
 */
function calculatePerformanceMetrics(documents: any[], workflows: any[], activities: any[], members: any[]): any {
  // Average document processing time (simplified)
  const processedDocs = documents.filter(d => d.processedAt && d.createdAt);
  const avgDocProcessingTime = processedDocs.length > 0 
    ? processedDocs.reduce((sum, doc) => {
        const time = new Date(doc.processedAt).getTime() - new Date(doc.createdAt).getTime();
        return sum + time;
      }, 0) / processedDocs.length / 1000 // Convert to seconds
    : 0;

  // Average workflow completion time
  const completedWorkflows = workflows.filter(w => w.completedAt && w.startedAt);
  const avgWorkflowCompletionTime = completedWorkflows.length > 0
    ? completedWorkflows.reduce((sum, workflow) => {
        const time = new Date(workflow.completedAt).getTime() - new Date(workflow.startedAt).getTime();
        return sum + time;
      }, 0) / completedWorkflows.length / 1000 // Convert to seconds
    : 0;

  // User engagement score (activities per active user)
  const activeUsers = new Set(activities.map(a => a.userId)).size;
  const userEngagementScore = activeUsers > 0 ? Math.round((activities.length / activeUsers) * 10) / 10 : 0;

  // Project health score (simplified calculation)
  const completionRate = workflows.length > 0 ? workflows.filter(w => w.status === 'COMPLETED').length / workflows.length : 1;
  const activityRate = members.length > 0 ? activeUsers / members.length : 0;
  const projectHealthScore = Math.round((completionRate * 0.6 + activityRate * 0.4) * 100);

  return {
    averageDocumentProcessingTime: Math.round(avgDocProcessingTime),
    averageWorkflowCompletionTime: Math.round(avgWorkflowCompletionTime),
    userEngagementScore,
    projectHealthScore
  };
}

// Register functions
app.http('project-analytics', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'projects/analytics',
  handler: getProjectAnalytics
});

/**
 * Workflow Automation Function
 * Handles advanced workflow automation, triggers, and conditions
 * Migrated from old-arch/src/workflow-service/automation/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';
import { eventService } from '../shared/services/event';

// Workflow automation types and enums
enum TriggerType {
  DOCUMENT_UPLOADED = 'DOCUMENT_UPLOADED',
  DOCUMENT_PROCESSED = 'DOCUMENT_PROCESSED',
  DOCUMENT_SHARED = 'DOCUMENT_SHARED',
  USER_CREATED = 'USER_CREATED',
  WORKFLOW_COMPLETED = 'WORKFLOW_COMPLETED',
  SCHEDULE = 'SCHEDULE',
  WEBHOOK = 'WEBHOOK',
  EMAIL_RECEIVED = 'EMAIL_RECEIVED',
  FORM_SUBMITTED = 'FORM_SUBMITTED',
  CUSTOM_EVENT = 'CUSTOM_EVENT'
}

enum ActionType {
  SEND_EMAIL = 'SEND_EMAIL',
  CREATE_DOCUMENT = 'CREATE_DOCUMENT',
  START_WORKFLOW = 'START_WORKFLOW',
  SEND_NOTIFICATION = 'SEND_NOTIFICATION',
  UPDATE_DATABASE = 'UPDATE_DATABASE',
  CALL_WEBHOOK = 'CALL_WEBHOOK',
  GENERATE_REPORT = 'GENERATE_REPORT',
  ARCHIVE_DOCUMENT = 'ARCHIVE_DOCUMENT',
  ASSIGN_TASK = 'ASSIGN_TASK',
  CUSTOM_SCRIPT = 'CUSTOM_SCRIPT'
}

enum ConditionOperator {
  EQUALS = 'EQUALS',
  NOT_EQUALS = 'NOT_EQUALS',
  CONTAINS = 'CONTAINS',
  NOT_CONTAINS = 'NOT_CONTAINS',
  GREATER_THAN = 'GREATER_THAN',
  LESS_THAN = 'LESS_THAN',
  IN = 'IN',
  NOT_IN = 'NOT_IN',
  REGEX = 'REGEX'
}

enum AutomationStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  PAUSED = 'PAUSED',
  ERROR = 'ERROR'
}

// Validation schemas
const createAutomationSchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  description: Joi.string().max(500).optional(),
  organizationId: Joi.string().uuid().required(),
  trigger: Joi.object({
    type: Joi.string().valid(...Object.values(TriggerType)).required(),
    config: Joi.object({
      eventType: Joi.string().optional(),
      schedule: Joi.string().optional(), // cron expression
      webhookUrl: Joi.string().uri().optional(),
      filters: Joi.object().optional()
    }).optional()
  }).required(),
  conditions: Joi.array().items(Joi.object({
    field: Joi.string().required(),
    operator: Joi.string().valid(...Object.values(ConditionOperator)).required(),
    value: Joi.any().required(),
    logicalOperator: Joi.string().valid('AND', 'OR').default('AND')
  })).optional(),
  actions: Joi.array().items(Joi.object({
    type: Joi.string().valid(...Object.values(ActionType)).required(),
    config: Joi.object({
      template: Joi.string().optional(),
      recipients: Joi.array().items(Joi.string()).optional(),
      webhookUrl: Joi.string().uri().optional(),
      workflowId: Joi.string().uuid().optional(),
      documentTemplate: Joi.string().optional(),
      script: Joi.string().optional(),
      parameters: Joi.object().optional()
    }).required(),
    order: Joi.number().min(1).required(),
    continueOnError: Joi.boolean().default(false)
  })).min(1).required(),
  settings: Joi.object({
    enabled: Joi.boolean().default(true),
    maxExecutions: Joi.number().min(1).optional(),
    retryAttempts: Joi.number().min(0).max(5).default(3),
    timeout: Joi.number().min(1).max(300).default(60) // seconds
  }).optional()
});

const executeAutomationSchema = Joi.object({
  automationId: Joi.string().uuid().required(),
  triggerData: Joi.object().required(),
  context: Joi.object({
    userId: Joi.string().uuid().optional(),
    organizationId: Joi.string().uuid().optional(),
    correlationId: Joi.string().optional()
  }).optional()
});

interface CreateAutomationRequest {
  name: string;
  description?: string;
  organizationId: string;
  trigger: {
    type: TriggerType;
    config?: {
      eventType?: string;
      schedule?: string;
      webhookUrl?: string;
      filters?: any;
    };
  };
  conditions?: Array<{
    field: string;
    operator: ConditionOperator;
    value: any;
    logicalOperator?: string;
  }>;
  actions: Array<{
    type: ActionType;
    config: {
      template?: string;
      recipients?: string[];
      webhookUrl?: string;
      workflowId?: string;
      documentTemplate?: string;
      script?: string;
      parameters?: any;
    };
    order: number;
    continueOnError?: boolean;
  }>;
  settings?: {
    enabled?: boolean;
    maxExecutions?: number;
    retryAttempts?: number;
    timeout?: number;
  };
}

interface WorkflowAutomation {
  id: string;
  name: string;
  description?: string;
  organizationId: string;
  trigger: any;
  conditions: any[];
  actions: any[];
  settings: {
    enabled: boolean;
    maxExecutions?: number;
    retryAttempts: number;
    timeout: number;
  };
  status: AutomationStatus;
  statistics: {
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    lastExecuted?: string;
    lastSuccess?: string;
    lastFailure?: string;
  };
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  tenantId: string;
}

interface AutomationExecution {
  id: string;
  automationId: string;
  triggerData: any;
  context: any;
  status: 'RUNNING' | 'COMPLETED' | 'FAILED' | 'TIMEOUT';
  startedAt: string;
  completedAt?: string;
  duration?: number;
  results: Array<{
    actionType: ActionType;
    actionOrder: number;
    status: 'SUCCESS' | 'FAILED' | 'SKIPPED';
    result?: any;
    error?: string;
    duration: number;
  }>;
  error?: string;
  tenantId: string;
}

/**
 * Create automation handler
 */
export async function createAutomation(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;
  logger.info("Create automation started", { correlationId });

  try {
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    const body = await request.json();
    const { error, value } = createAutomationSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const automationRequest: CreateAutomationRequest = value;

    // Check organization access
    const hasAccess = await checkOrganizationAccess(automationRequest.organizationId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Check automation permissions
    const hasAutomationAccess = await checkAutomationAccess(user, automationRequest.organizationId);
    if (!hasAutomationAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to workflow automation" }
      }, request);
    }

    // Create automation
    const automationId = uuidv4();
    const now = new Date().toISOString();

    const automation: WorkflowAutomation = {
      id: automationId,
      name: automationRequest.name,
      description: automationRequest.description,
      organizationId: automationRequest.organizationId,
      trigger: automationRequest.trigger,
      conditions: automationRequest.conditions || [],
      actions: automationRequest.actions.sort((a, b) => a.order - b.order),
      settings: {
        enabled: true,
        retryAttempts: 3,
        timeout: 60,
        ...automationRequest.settings
      },
      status: AutomationStatus.ACTIVE,
      statistics: {
        totalExecutions: 0,
        successfulExecutions: 0,
        failedExecutions: 0
      },
      createdBy: user.id,
      createdAt: now,
      updatedAt: now,
      tenantId: user.tenantId || user.id
    };

    await db.createItem('workflow-automations', automation);

    // Register automation trigger
    await registerAutomationTrigger(automation);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "automation_created",
      userId: user.id,
      organizationId: automationRequest.organizationId,
      timestamp: now,
      details: {
        automationId,
        automationName: automationRequest.name,
        triggerType: automationRequest.trigger.type,
        actionCount: automationRequest.actions.length,
        conditionCount: automationRequest.conditions?.length || 0
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'AutomationCreated',
      aggregateId: automationId,
      aggregateType: 'WorkflowAutomation',
      version: 1,
      data: {
        automation,
        createdBy: user.id
      },
      userId: user.id,
      organizationId: automationRequest.organizationId,
      tenantId: user.tenantId
    });

    logger.info("Automation created successfully", {
      correlationId,
      automationId,
      automationName: automationRequest.name,
      triggerType: automationRequest.trigger.type,
      actionCount: automationRequest.actions.length,
      createdBy: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        automationId,
        name: automationRequest.name,
        triggerType: automationRequest.trigger.type,
        actionCount: automationRequest.actions.length,
        status: AutomationStatus.ACTIVE,
        createdAt: now,
        message: "Automation created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create automation failed", { correlationId, error: errorMessage });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Execute automation handler
 */
export async function executeAutomation(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;
  const startTime = Date.now();
  logger.info("Execute automation started", { correlationId });

  try {
    const body = await request.json();
    const { error, value } = executeAutomationSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const executeRequest = value;

    // Get automation
    const automation = await db.readItem('workflow-automations', executeRequest.automationId, executeRequest.automationId);
    if (!automation) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Automation not found" }
      }, request);
    }

    const automationData = automation as WorkflowAutomation;

    // Check if automation is enabled
    if (!automationData.settings.enabled || automationData.status !== AutomationStatus.ACTIVE) {
      return addCorsHeaders({
        status: 409,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Automation is not active" }
      }, request);
    }

    // Execute automation
    const execution = await processAutomationExecution(automationData, executeRequest.triggerData, executeRequest.context);

    // Update automation statistics
    await updateAutomationStatistics(automationData.id, execution.status === 'COMPLETED');

    const duration = Date.now() - startTime;

    logger.info("Automation executed", {
      correlationId,
      automationId: executeRequest.automationId,
      executionId: execution.id,
      status: execution.status,
      duration,
      actionResults: execution.results.length
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        executionId: execution.id,
        automationId: executeRequest.automationId,
        status: execution.status,
        duration: execution.duration,
        results: execution.results,
        completedAt: execution.completedAt,
        message: "Automation executed successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Execute automation failed", { correlationId, error: errorMessage });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkOrganizationAccess(organizationId: string, userId: string): Promise<boolean> {
  try {
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
    return memberships.length > 0;
  } catch (error) {
    logger.error('Failed to check organization access', { error, organizationId, userId });
    return false;
  }
}

async function checkAutomationAccess(user: any, organizationId: string): Promise<boolean> {
  try {
    // Check if user has admin or automation role
    if (user.roles?.includes('admin') || user.roles?.includes('automation_admin')) {
      return true;
    }

    // Check organization-level permissions
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, user.id, 'ACTIVE']);

    if (memberships.length > 0) {
      const membership = memberships[0] as any;
      return membership.role === 'OWNER' || membership.role === 'ADMIN';
    }

    return false;
  } catch (error) {
    logger.error('Failed to check automation access', { error, userId: user.id, organizationId });
    return false;
  }
}

async function registerAutomationTrigger(automation: WorkflowAutomation): Promise<void> {
  try {
    // Register trigger in cache for quick lookup
    const triggerKey = `automation_trigger:${automation.trigger.type}:${automation.organizationId}`;
    await redis.sadd(triggerKey, automation.id);
    await redis.expire(triggerKey, 86400); // 24 hours

    // For scheduled triggers, register with scheduler
    if (automation.trigger.type === TriggerType.SCHEDULE && automation.trigger.config?.schedule) {
      await registerScheduledTrigger(automation);
    }

    logger.info('Automation trigger registered', {
      automationId: automation.id,
      triggerType: automation.trigger.type
    });

  } catch (error) {
    logger.error('Failed to register automation trigger', { error, automationId: automation.id });
  }
}

async function registerScheduledTrigger(automation: WorkflowAutomation): Promise<void> {
  try {
    // In production, this would integrate with a job scheduler
    const scheduleKey = `scheduled_automation:${automation.id}`;
    await redis.hset(scheduleKey, {
      automationId: automation.id,
      schedule: automation.trigger.config?.schedule || '',
      nextRun: calculateNextRun(automation.trigger.config?.schedule || ''),
      enabled: automation.settings.enabled.toString()
    });
    await redis.expire(scheduleKey, 86400 * 7); // 7 days

    logger.info('Scheduled trigger registered', {
      automationId: automation.id,
      schedule: automation.trigger.config?.schedule
    });

  } catch (error) {
    logger.error('Failed to register scheduled trigger', { error, automationId: automation.id });
  }
}

function calculateNextRun(cronExpression: string): string {
  // Simplified next run calculation - in production use a proper cron library
  const now = new Date();
  const nextRun = new Date(now.getTime() + 60 * 60 * 1000); // 1 hour from now
  return nextRun.toISOString();
}

async function processAutomationExecution(automation: WorkflowAutomation, triggerData: any, context: any): Promise<AutomationExecution> {
  const executionId = uuidv4();
  const startTime = Date.now();
  const now = new Date().toISOString();

  const execution: AutomationExecution = {
    id: executionId,
    automationId: automation.id,
    triggerData,
    context: context || {},
    status: 'RUNNING',
    startedAt: now,
    results: [],
    tenantId: automation.tenantId
  };

  try {
    // Store execution record
    await db.createItem('automation-executions', execution);

    // Check conditions
    const conditionsPass = await evaluateConditions(automation.conditions, triggerData, context);
    if (!conditionsPass) {
      execution.status = 'COMPLETED';
      execution.completedAt = new Date().toISOString();
      execution.duration = Date.now() - startTime;

      await db.updateItem('automation-executions', execution);
      return execution;
    }

    // Execute actions
    for (const action of automation.actions) {
      const actionStartTime = Date.now();

      try {
        const actionResult = await executeAction(action, triggerData, context);

        execution.results.push({
          actionType: action.type,
          actionOrder: action.order,
          status: 'SUCCESS',
          result: actionResult,
          duration: Date.now() - actionStartTime
        });

      } catch (actionError) {
        const errorMessage = actionError instanceof Error ? actionError.message : String(actionError);

        execution.results.push({
          actionType: action.type,
          actionOrder: action.order,
          status: 'FAILED',
          error: errorMessage,
          duration: Date.now() - actionStartTime
        });

        if (!action.continueOnError) {
          throw actionError;
        }
      }
    }

    execution.status = 'COMPLETED';
    execution.completedAt = new Date().toISOString();
    execution.duration = Date.now() - startTime;

  } catch (error) {
    execution.status = 'FAILED';
    execution.error = error instanceof Error ? error.message : String(error);
    execution.completedAt = new Date().toISOString();
    execution.duration = Date.now() - startTime;

    logger.error('Automation execution failed', {
      executionId,
      automationId: automation.id,
      error: execution.error
    });
  }

  // Update execution record
  await db.updateItem('automation-executions', execution);

  return execution;
}

async function evaluateConditions(conditions: any[], triggerData: any, context: any): Promise<boolean> {
  if (!conditions || conditions.length === 0) {
    return true; // No conditions means always execute
  }

  try {
    let result = true;
    let currentLogicalOperator = 'AND';

    for (const condition of conditions) {
      const conditionResult = evaluateCondition(condition, triggerData, context);

      if (currentLogicalOperator === 'AND') {
        result = result && conditionResult;
      } else {
        result = result || conditionResult;
      }

      currentLogicalOperator = condition.logicalOperator || 'AND';
    }

    return result;

  } catch (error) {
    logger.error('Failed to evaluate conditions', { error, conditions });
    return false;
  }
}

function evaluateCondition(condition: any, triggerData: any, context: any): boolean {
  try {
    const fieldValue = getFieldValue(condition.field, triggerData, context);
    const expectedValue = condition.value;

    switch (condition.operator) {
      case ConditionOperator.EQUALS:
        return fieldValue === expectedValue;
      case ConditionOperator.NOT_EQUALS:
        return fieldValue !== expectedValue;
      case ConditionOperator.CONTAINS:
        return String(fieldValue).includes(String(expectedValue));
      case ConditionOperator.NOT_CONTAINS:
        return !String(fieldValue).includes(String(expectedValue));
      case ConditionOperator.GREATER_THAN:
        return Number(fieldValue) > Number(expectedValue);
      case ConditionOperator.LESS_THAN:
        return Number(fieldValue) < Number(expectedValue);
      case ConditionOperator.IN:
        return Array.isArray(expectedValue) && expectedValue.includes(fieldValue);
      case ConditionOperator.NOT_IN:
        return Array.isArray(expectedValue) && !expectedValue.includes(fieldValue);
      case ConditionOperator.REGEX:
        const regex = new RegExp(String(expectedValue));
        return regex.test(String(fieldValue));
      default:
        return false;
    }

  } catch (error) {
    logger.error('Failed to evaluate condition', { error, condition });
    return false;
  }
}

function getFieldValue(field: string, triggerData: any, context: any): any {
  try {
    // Support nested field access with dot notation
    const fieldParts = field.split('.');
    let value = triggerData;

    for (const part of fieldParts) {
      if (value && typeof value === 'object' && part in value) {
        value = value[part];
      } else {
        // Try context if not found in trigger data
        value = context;
        for (const contextPart of fieldParts) {
          if (value && typeof value === 'object' && contextPart in value) {
            value = value[contextPart];
          } else {
            return undefined;
          }
        }
        break;
      }
    }

    return value;

  } catch (error) {
    logger.error('Failed to get field value', { error, field });
    return undefined;
  }
}

async function executeAction(action: any, triggerData: any, context: any): Promise<any> {
  try {
    switch (action.type) {
      case ActionType.SEND_EMAIL:
        return await executeSendEmailAction(action, triggerData, context);
      case ActionType.SEND_NOTIFICATION:
        return await executeSendNotificationAction(action, triggerData, context);
      case ActionType.START_WORKFLOW:
        return await executeStartWorkflowAction(action, triggerData, context);
      case ActionType.CALL_WEBHOOK:
        return await executeCallWebhookAction(action, triggerData, context);
      case ActionType.CREATE_DOCUMENT:
        return await executeCreateDocumentAction(action, triggerData, context);
      default:
        throw new Error(`Unsupported action type: ${action.type}`);
    }

  } catch (error) {
    logger.error('Failed to execute action', { error, actionType: action.type });
    throw error;
  }
}

async function executeSendEmailAction(action: any, triggerData: any, context: any): Promise<any> {
  // Mock email sending
  logger.info('Email action executed', {
    recipients: action.config.recipients,
    template: action.config.template
  });

  return {
    type: 'email_sent',
    recipients: action.config.recipients,
    success: true
  };
}

async function executeSendNotificationAction(action: any, triggerData: any, context: any): Promise<any> {
  // Mock notification sending
  logger.info('Notification action executed', {
    recipients: action.config.recipients,
    template: action.config.template
  });

  return {
    type: 'notification_sent',
    recipients: action.config.recipients,
    success: true
  };
}

async function executeStartWorkflowAction(action: any, triggerData: any, context: any): Promise<any> {
  // Mock workflow start
  logger.info('Workflow action executed', {
    workflowId: action.config.workflowId,
    parameters: action.config.parameters
  });

  return {
    type: 'workflow_started',
    workflowId: action.config.workflowId,
    executionId: uuidv4(),
    success: true
  };
}

async function executeCallWebhookAction(action: any, triggerData: any, context: any): Promise<any> {
  // Mock webhook call
  logger.info('Webhook action executed', {
    webhookUrl: action.config.webhookUrl,
    parameters: action.config.parameters
  });

  return {
    type: 'webhook_called',
    url: action.config.webhookUrl,
    statusCode: 200,
    success: true
  };
}

async function executeCreateDocumentAction(action: any, triggerData: any, context: any): Promise<any> {
  // Mock document creation
  const documentId = uuidv4();

  logger.info('Document creation action executed', {
    documentTemplate: action.config.documentTemplate,
    documentId
  });

  return {
    type: 'document_created',
    documentId,
    template: action.config.documentTemplate,
    success: true
  };
}

async function updateAutomationStatistics(automationId: string, success: boolean): Promise<void> {
  try {
    const statsKey = `automation_stats:${automationId}`;

    await redis.hincrby(statsKey, 'totalExecutions', 1);

    if (success) {
      await redis.hincrby(statsKey, 'successfulExecutions', 1);
      await redis.hset(statsKey, 'lastSuccess', new Date().toISOString());
    } else {
      await redis.hincrby(statsKey, 'failedExecutions', 1);
      await redis.hset(statsKey, 'lastFailure', new Date().toISOString());
    }

    await redis.hset(statsKey, 'lastExecuted', new Date().toISOString());
    await redis.expire(statsKey, 86400 * 30); // 30 days

  } catch (error) {
    logger.error('Failed to update automation statistics', { error, automationId });
  }
}

// Register functions
app.http('automation-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'automations',
  handler: createAutomation
});

app.http('automation-execute', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'automations/execute',
  handler: executeAutomation
});

/**
 * Dashboard Management Function
 * Handles dashboard creation, customization, and widget management
 * Migrated from old-arch/src/analytics-service/dashboard/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { eventService } from '../shared/services/event';

// Dashboard types and enums
enum WidgetType {
  CHART = 'CHART',
  TABLE = 'TABLE',
  METRIC = 'METRIC',
  LIST = 'LIST',
  MAP = 'MAP',
  GAUGE = 'GAUGE',
  PROGRESS = 'PROGRESS',
  TEXT = 'TEXT'
}

enum ChartType {
  LINE = 'LINE',
  BAR = 'BAR',
  PIE = 'PIE',
  DOUGHNUT = 'DOUGHNUT',
  AREA = 'AREA',
  SCATTER = 'SCATTER',
  HEATMAP = 'HEATMAP'
}

enum DashboardVisibility {
  PRIVATE = 'PRIVATE',
  ORGANIZATION = 'ORGANIZATION',
  PROJECT = 'PROJECT',
  PUBLIC = 'PUBLIC'
}

// Validation schemas
const createDashboardSchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  description: Joi.string().max(500).optional(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional(),
  visibility: Joi.string().valid(...Object.values(DashboardVisibility)).default(DashboardVisibility.PRIVATE),
  layout: Joi.object({
    columns: Joi.number().min(1).max(12).default(12),
    rows: Joi.number().min(1).max(20).default(10),
    gap: Joi.number().min(0).max(50).default(16)
  }).optional(),
  widgets: Joi.array().items(Joi.object({
    id: Joi.string().optional(),
    type: Joi.string().valid(...Object.values(WidgetType)).required(),
    title: Joi.string().min(1).max(100).required(),
    position: Joi.object({
      x: Joi.number().min(0).required(),
      y: Joi.number().min(0).required(),
      width: Joi.number().min(1).max(12).required(),
      height: Joi.number().min(1).max(10).required()
    }).required(),
    configuration: Joi.object({
      chartType: Joi.string().valid(...Object.values(ChartType)).optional(),
      dataSource: Joi.string().required(),
      filters: Joi.object().optional(),
      refreshInterval: Joi.number().min(30).max(3600).default(300),
      showLegend: Joi.boolean().default(true),
      showGrid: Joi.boolean().default(true),
      colors: Joi.array().items(Joi.string()).optional()
    }).required()
  })).optional(),
  settings: Joi.object({
    autoRefresh: Joi.boolean().default(true),
    refreshInterval: Joi.number().min(60).max(3600).default(300),
    allowExport: Joi.boolean().default(true),
    allowSharing: Joi.boolean().default(false),
    theme: Joi.string().valid('light', 'dark', 'auto').default('light')
  }).optional()
});

const updateWidgetSchema = Joi.object({
  dashboardId: Joi.string().uuid().required(),
  widgetId: Joi.string().uuid().required(),
  title: Joi.string().min(1).max(100).optional(),
  position: Joi.object({
    x: Joi.number().min(0).optional(),
    y: Joi.number().min(0).optional(),
    width: Joi.number().min(1).max(12).optional(),
    height: Joi.number().min(1).max(10).optional()
  }).optional(),
  configuration: Joi.object().optional()
});

interface CreateDashboardRequest {
  name: string;
  description?: string;
  organizationId: string;
  projectId?: string;
  visibility?: DashboardVisibility;
  layout?: {
    columns?: number;
    rows?: number;
    gap?: number;
  };
  widgets?: Array<{
    id?: string;
    type: WidgetType;
    title: string;
    position: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
    configuration: {
      chartType?: ChartType;
      dataSource: string;
      filters?: any;
      refreshInterval?: number;
      showLegend?: boolean;
      showGrid?: boolean;
      colors?: string[];
    };
  }>;
  settings?: {
    autoRefresh?: boolean;
    refreshInterval?: number;
    allowExport?: boolean;
    allowSharing?: boolean;
    theme?: string;
  };
}

interface Dashboard {
  id: string;
  name: string;
  description?: string;
  organizationId: string;
  projectId?: string;
  visibility: DashboardVisibility;
  layout: {
    columns: number;
    rows: number;
    gap: number;
  };
  widgets: Array<{
    id: string;
    type: WidgetType;
    title: string;
    position: any;
    configuration: any;
    data?: any;
    lastUpdated?: string;
  }>;
  settings: {
    autoRefresh: boolean;
    refreshInterval: number;
    allowExport: boolean;
    allowSharing: boolean;
    theme: string;
  };
  createdBy: string;
  createdAt: string;
  updatedBy: string;
  updatedAt: string;
  lastViewedAt?: string;
  viewCount: number;
  tenantId: string;
}

/**
 * Create dashboard handler
 */
export async function createDashboard(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create dashboard started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createDashboardSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const dashboardRequest: CreateDashboardRequest = value;

    // Check organization access
    const hasAccess = await checkOrganizationAccess(dashboardRequest.organizationId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Check dashboard creation limits
    const canCreate = await checkDashboardCreationLimits(dashboardRequest.organizationId);
    if (!canCreate.allowed) {
      return addCorsHeaders({
        status: 429,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: canCreate.reason }
      }, request);
    }

    // Create dashboard
    const dashboardId = uuidv4();
    const now = new Date().toISOString();

    // Process widgets
    const widgets = (dashboardRequest.widgets || []).map(widget => ({
      ...widget,
      id: widget.id || uuidv4(),
      configuration: {
        refreshInterval: 300,
        showLegend: true,
        showGrid: true,
        ...widget.configuration
      }
    }));

    const dashboard: Dashboard = {
      id: dashboardId,
      name: dashboardRequest.name,
      description: dashboardRequest.description,
      organizationId: dashboardRequest.organizationId,
      projectId: dashboardRequest.projectId,
      visibility: dashboardRequest.visibility || DashboardVisibility.PRIVATE,
      layout: {
        columns: 12,
        rows: 10,
        gap: 16,
        ...dashboardRequest.layout
      },
      widgets,
      settings: {
        autoRefresh: true,
        refreshInterval: 300,
        allowExport: true,
        allowSharing: false,
        theme: 'light',
        ...dashboardRequest.settings
      },
      createdBy: user.id,
      createdAt: now,
      updatedBy: user.id,
      updatedAt: now,
      viewCount: 0,
      tenantId: user.tenantId || user.id
    };

    await db.createItem('dashboards', dashboard);

    // Load initial widget data
    await loadWidgetData(dashboard);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "dashboard_created",
      userId: user.id,
      organizationId: dashboardRequest.organizationId,
      projectId: dashboardRequest.projectId,
      timestamp: now,
      details: {
        dashboardId,
        dashboardName: dashboardRequest.name,
        widgetCount: widgets.length,
        visibility: dashboard.visibility
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'DashboardCreated',
      aggregateId: dashboardId,
      aggregateType: 'Dashboard',
      version: 1,
      data: {
        dashboard,
        createdBy: user.id
      },
      userId: user.id,
      organizationId: dashboardRequest.organizationId,
      tenantId: user.tenantId
    });

    logger.info("Dashboard created successfully", {
      correlationId,
      dashboardId,
      dashboardName: dashboardRequest.name,
      widgetCount: widgets.length,
      createdBy: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: dashboardId,
        name: dashboardRequest.name,
        visibility: dashboard.visibility,
        widgetCount: widgets.length,
        createdAt: now,
        message: "Dashboard created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create dashboard failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Get dashboard handler
 */
export async function getDashboard(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const dashboardId = request.params.dashboardId;

  logger.info("Get dashboard started", { correlationId, dashboardId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    if (!dashboardId) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Dashboard ID is required" }
      }, request);
    }

    // Get dashboard
    const dashboard = await db.readItem('dashboards', dashboardId, dashboardId);
    if (!dashboard) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Dashboard not found" }
      }, request);
    }

    const dashboardData = dashboard as any;

    // Check access
    const hasAccess = await checkDashboardAccess(dashboardData, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to dashboard" }
      }, request);
    }

    // Update view count and last viewed
    const updatedDashboard = {
      ...dashboardData,
      id: dashboardId,
      viewCount: (dashboardData.viewCount || 0) + 1,
      lastViewedAt: new Date().toISOString()
    };
    await db.updateItem('dashboards', updatedDashboard);

    // Refresh widget data if auto-refresh is enabled
    if (dashboardData.settings?.autoRefresh) {
      await refreshWidgetData(dashboardData);
    }

    logger.info("Dashboard retrieved successfully", {
      correlationId,
      dashboardId,
      dashboardName: dashboardData.name,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        ...dashboardData,
        viewCount: updatedDashboard.viewCount,
        lastViewedAt: updatedDashboard.lastViewedAt
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get dashboard failed", {
      correlationId,
      dashboardId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkOrganizationAccess(organizationId: string, userId: string): Promise<boolean> {
  try {
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
    return memberships.length > 0;
  } catch (error) {
    logger.error('Failed to check organization access', { error, organizationId, userId });
    return false;
  }
}

async function checkDashboardCreationLimits(organizationId: string): Promise<{ allowed: boolean; reason?: string }> {
  try {
    // Get organization to check tier
    const organization = await db.readItem('organizations', organizationId, organizationId);
    if (!organization) {
      return { allowed: false, reason: 'Organization not found' };
    }

    const orgData = organization as any;
    const tier = orgData.tier || 'FREE';

    // Define tier limits
    const limits: { [key: string]: { maxDashboards: number } } = {
      'FREE': { maxDashboards: 3 },
      'PROFESSIONAL': { maxDashboards: 25 },
      'ENTERPRISE': { maxDashboards: -1 } // Unlimited
    };

    const limit = limits[tier] || limits['FREE'];

    if (limit.maxDashboards === -1) {
      return { allowed: true };
    }

    // Check current dashboard count
    const dashboardCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId';
    const countResult = await db.queryItems('dashboards', dashboardCountQuery, [organizationId]);
    const currentCount = Number(countResult[0]) || 0;

    if (currentCount >= limit.maxDashboards) {
      return {
        allowed: false,
        reason: `Dashboard limit reached (${limit.maxDashboards})`
      };
    }

    return { allowed: true };

  } catch (error) {
    logger.error('Failed to check dashboard creation limits', { error, organizationId });
    return { allowed: false, reason: 'Failed to check limits' };
  }
}

async function checkDashboardAccess(dashboard: any, userId: string): Promise<boolean> {
  try {
    // Check if user is the owner
    if (dashboard.createdBy === userId) {
      return true;
    }

    // Check visibility
    if (dashboard.visibility === DashboardVisibility.PUBLIC) {
      return true;
    }

    // Check organization membership
    if (dashboard.visibility === DashboardVisibility.ORGANIZATION) {
      const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
      const memberships = await db.queryItems('organization-members', membershipQuery, [dashboard.organizationId, userId, 'ACTIVE']);
      return memberships.length > 0;
    }

    // Check project membership
    if (dashboard.visibility === DashboardVisibility.PROJECT && dashboard.projectId) {
      const membershipQuery = 'SELECT * FROM c WHERE c.projectId = @projectId AND c.userId = @userId AND c.status = @status';
      const memberships = await db.queryItems('project-members', membershipQuery, [dashboard.projectId, userId, 'ACTIVE']);
      return memberships.length > 0;
    }

    return false;
  } catch (error) {
    logger.error('Failed to check dashboard access', { error, dashboardId: dashboard.id, userId });
    return false;
  }
}

async function loadWidgetData(dashboard: Dashboard): Promise<void> {
  try {
    for (const widget of dashboard.widgets) {
      widget.data = await getWidgetData(widget, dashboard.organizationId);
      widget.lastUpdated = new Date().toISOString();
    }

    // Update dashboard with widget data
    await db.updateItem('dashboards', dashboard);
  } catch (error) {
    logger.error('Failed to load widget data', { error, dashboardId: dashboard.id });
  }
}

async function refreshWidgetData(dashboard: any): Promise<void> {
  try {
    let hasUpdates = false;

    for (const widget of dashboard.widgets) {
      const lastUpdated = new Date(widget.lastUpdated || 0);
      const refreshInterval = widget.configuration?.refreshInterval || 300;
      const shouldRefresh = Date.now() - lastUpdated.getTime() > refreshInterval * 1000;

      if (shouldRefresh) {
        widget.data = await getWidgetData(widget, dashboard.organizationId);
        widget.lastUpdated = new Date().toISOString();
        hasUpdates = true;
      }
    }

    if (hasUpdates) {
      await db.updateItem('dashboards', dashboard);
    }
  } catch (error) {
    logger.error('Failed to refresh widget data', { error, dashboardId: dashboard.id });
  }
}

async function getWidgetData(widget: any, organizationId: string): Promise<any> {
  try {
    // Simplified widget data generation based on data source
    const dataSource = widget.configuration.dataSource;

    switch (dataSource) {
      case 'documents':
        return await getDocumentMetrics(organizationId);
      case 'users':
        return await getUserMetrics(organizationId);
      case 'workflows':
        return await getWorkflowMetrics(organizationId);
      case 'activities':
        return await getActivityMetrics(organizationId);
      default:
        return { message: 'No data available' };
    }
  } catch (error) {
    logger.error('Failed to get widget data', { error, widgetId: widget.id });
    return { error: 'Failed to load data' };
  }
}

async function getDocumentMetrics(organizationId: string): Promise<any> {
  // Simplified metrics - in production, this would query actual data
  return {
    totalDocuments: 1250,
    documentsThisMonth: 85,
    averageProcessingTime: 45,
    topCategories: [
      { name: 'Contracts', count: 320 },
      { name: 'Reports', count: 280 },
      { name: 'Invoices', count: 195 }
    ]
  };
}

async function getUserMetrics(organizationId: string): Promise<any> {
  return {
    totalUsers: 45,
    activeUsers: 38,
    newUsersThisMonth: 5,
    topContributors: [
      { name: 'John Smith', documents: 25 },
      { name: 'Jane Doe', documents: 18 },
      { name: 'Bob Johnson', documents: 15 }
    ]
  };
}

async function getWorkflowMetrics(organizationId: string): Promise<any> {
  return {
    totalWorkflows: 125,
    activeWorkflows: 23,
    completedThisMonth: 45,
    averageCompletionTime: 3.2
  };
}

async function getActivityMetrics(organizationId: string): Promise<any> {
  return {
    totalActivities: 2850,
    activitiesToday: 125,
    mostActiveHour: 14,
    activityTrend: [
      { date: '2024-01-01', count: 45 },
      { date: '2024-01-02', count: 52 },
      { date: '2024-01-03', count: 38 }
    ]
  };
}

// Register functions
app.http('dashboard-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'dashboards',
  handler: createDashboard
});

app.http('dashboard-get', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'dashboards/{dashboardId}',
  handler: getDashboard
});

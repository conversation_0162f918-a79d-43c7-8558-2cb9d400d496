/**
 * AI Intelligent Search Function
 * Handles AI-powered intelligent search with semantic understanding
 * Migrated from old-arch/src/ai-service/intelligent-search/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Search types and interfaces
enum SearchMode {
  BASIC = 'basic',
  SEMANTIC = 'semantic',
  HYBRID = 'hybrid',
  VECTOR = 'vector'
}

enum SearchScope {
  ALL = 'all',
  DOCUMENTS = 'documents',
  WORKFLOWS = 'workflows',
  PROJECTS = 'projects',
  USERS = 'users'
}

// Validation schema
const intelligentSearchSchema = Joi.object({
  query: Joi.string().min(1).max(1000).required(),
  mode: Joi.string().valid(...Object.values(SearchMode)).default(SearchMode.HYBRID),
  scope: Joi.string().valid(...Object.values(SearchScope)).default(SearchScope.ALL),
  filters: Joi.object({
    organizationId: Joi.string().uuid().optional(),
    projectId: Joi.string().uuid().optional(),
    documentType: Joi.string().optional(),
    createdBy: Joi.string().uuid().optional(),
    dateRange: Joi.object({
      start: Joi.string().isoDate().optional(),
      end: Joi.string().isoDate().optional()
    }).optional(),
    tags: Joi.array().items(Joi.string()).optional(),
    categories: Joi.array().items(Joi.string()).optional()
  }).optional(),
  options: Joi.object({
    includeContent: Joi.boolean().default(false),
    highlightMatches: Joi.boolean().default(true),
    enablePersonalization: Joi.boolean().default(true),
    maxResults: Joi.number().min(1).max(100).default(20),
    enableSuggestions: Joi.boolean().default(true),
    enableFacets: Joi.boolean().default(true)
  }).optional(),
  pagination: Joi.object({
    page: Joi.number().min(1).default(1),
    limit: Joi.number().min(1).max(100).default(20)
  }).optional()
});

interface IntelligentSearchRequest {
  query: string;
  mode: SearchMode;
  scope: SearchScope;
  filters?: {
    organizationId?: string;
    projectId?: string;
    documentType?: string;
    createdBy?: string;
    dateRange?: {
      start?: string;
      end?: string;
    };
    tags?: string[];
    categories?: string[];
  };
  options?: {
    includeContent?: boolean;
    highlightMatches?: boolean;
    enablePersonalization?: boolean;
    maxResults?: number;
    enableSuggestions?: boolean;
    enableFacets?: boolean;
  };
  pagination?: {
    page?: number;
    limit?: number;
  };
}

interface SearchResult {
  id: string;
  type: string;
  title: string;
  description?: string;
  content?: string;
  highlights?: string[];
  score: number;
  metadata: any;
  url?: string;
}

interface IntelligentSearchResponse {
  query: string;
  mode: SearchMode;
  scope: SearchScope;
  results: SearchResult[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
  facets?: {
    [key: string]: { value: string; count: number }[];
  };
  suggestions?: string[];
  semanticInsights?: {
    intent: string;
    entities: string[];
    concepts: string[];
    relatedQueries: string[];
  };
  processingTime: number;
  success: boolean;
}

/**
 * Intelligent search handler
 */
export async function intelligentSearch(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Intelligent search started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Parse and validate request
    let searchRequest: IntelligentSearchRequest;

    if (request.method === 'GET') {
      // Handle GET request with query parameters
      const url = new URL(request.url);
      searchRequest = {
        query: url.searchParams.get('query') || '',
        mode: (url.searchParams.get('mode') as SearchMode) || SearchMode.HYBRID,
        scope: (url.searchParams.get('scope') as SearchScope) || SearchScope.ALL,
        filters: {},
        options: {},
        pagination: {
          page: parseInt(url.searchParams.get('page') || '1'),
          limit: parseInt(url.searchParams.get('limit') || '20')
        }
      };
    } else {
      // Handle POST request with JSON body
      const body = await request.json();
      const { error, value } = intelligentSearchSchema.validate(body);

      if (error) {
        return addCorsHeaders({
          status: 400,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: {
            error: 'Validation Error',
            message: error.details.map(d => d.message).join(', ')
          }
        }, request);
      }

      searchRequest = value;
    }

    if (!searchRequest.query.trim()) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Search query is required" }
      }, request);
    }

    const startTime = Date.now();

    // Get user personalization settings if enabled
    let personalizationSettings = null;
    if (searchRequest.options?.enablePersonalization) {
      const settingsQuery = 'SELECT * FROM c WHERE c.userId = @userId';
      const settings = await db.queryItems('user-personalization', settingsQuery, [user.id]);
      personalizationSettings = settings.length > 0 ? settings[0] : null;
    }

    // Perform intelligent search
    const searchResults = await performIntelligentSearch(
      searchRequest,
      user,
      personalizationSettings
    );

    // Record search analytics
    await recordSearchAnalytics(searchRequest, user, searchResults);

    const processingTime = Date.now() - startTime;

    const response: IntelligentSearchResponse = {
      ...searchResults,
      processingTime,
      success: true
    };

    logger.info("Intelligent search completed successfully", {
      correlationId,
      query: searchRequest.query,
      mode: searchRequest.mode,
      scope: searchRequest.scope,
      resultCount: searchResults.results.length,
      processingTime,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: response
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Intelligent search failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Perform intelligent search with AI capabilities
 */
async function performIntelligentSearch(
  searchRequest: IntelligentSearchRequest,
  user: any,
  personalizationSettings: any
): Promise<Omit<IntelligentSearchResponse, 'processingTime' | 'success'>> {
  const { query, mode, scope, filters, options, pagination } = searchRequest;
  const page = pagination?.page || 1;
  const limit = pagination?.limit || 20;

  // Build search query based on scope and filters
  let searchQuery = '';
  let queryParameters: any[] = [];

  // Add tenant isolation
  searchQuery = 'SELECT * FROM c WHERE c.tenantId = @tenantId';
  queryParameters.push({ name: '@tenantId', value: user.tenantId });

  // Add scope filtering
  if (scope !== SearchScope.ALL) {
    switch (scope) {
      case SearchScope.DOCUMENTS:
        searchQuery += ' AND c.type = @type';
        queryParameters.push({ name: '@type', value: 'document' });
        break;
      case SearchScope.WORKFLOWS:
        searchQuery += ' AND c.type = @type';
        queryParameters.push({ name: '@type', value: 'workflow' });
        break;
      case SearchScope.PROJECTS:
        searchQuery += ' AND c.type = @type';
        queryParameters.push({ name: '@type', value: 'project' });
        break;
      case SearchScope.USERS:
        searchQuery += ' AND c.type = @type';
        queryParameters.push({ name: '@type', value: 'user' });
        break;
    }
  }

  // Add text search (simplified implementation)
  if (query.trim()) {
    searchQuery += ' AND (CONTAINS(LOWER(c.name), LOWER(@query)) OR CONTAINS(LOWER(c.description), LOWER(@query)) OR CONTAINS(LOWER(c.content), LOWER(@query)))';
    queryParameters.push({ name: '@query', value: query });
  }

  // Add filters
  if (filters?.organizationId) {
    searchQuery += ' AND c.organizationId = @organizationId';
    queryParameters.push({ name: '@organizationId', value: filters.organizationId });
  }

  if (filters?.projectId) {
    searchQuery += ' AND c.projectId = @projectId';
    queryParameters.push({ name: '@projectId', value: filters.projectId });
  }

  if (filters?.createdBy) {
    searchQuery += ' AND c.createdBy = @createdBy';
    queryParameters.push({ name: '@createdBy', value: filters.createdBy });
  }

  // Add date range filter
  if (filters?.dateRange?.start) {
    searchQuery += ' AND c.createdAt >= @startDate';
    queryParameters.push({ name: '@startDate', value: filters.dateRange.start });
  }

  if (filters?.dateRange?.end) {
    searchQuery += ' AND c.createdAt <= @endDate';
    queryParameters.push({ name: '@endDate', value: filters.dateRange.end });
  }

  // Add ordering (simplified)
  searchQuery += ' ORDER BY c.createdAt DESC';

  // Execute search across relevant containers
  const containers = getSearchContainers(scope);
  let allResults: any[] = [];

  for (const containerName of containers) {
    try {
      const results = await db.queryItems(containerName, searchQuery, queryParameters);
      allResults = allResults.concat(results.map(item => ({ ...(item as any), _container: containerName })));
    } catch (error) {
      logger.warn(`Failed to search in container ${containerName}`, { error });
    }
  }

  // Apply AI-powered ranking and filtering based on mode
  const rankedResults = await applyIntelligentRanking(allResults, query, mode, personalizationSettings);

  // Apply pagination
  const offset = (page - 1) * limit;
  const paginatedResults = rankedResults.slice(offset, offset + limit);

  // Convert to search results format
  const searchResults: SearchResult[] = paginatedResults.map(item => ({
    id: item.id,
    type: item._container || item.type || 'unknown',
    title: item.name || item.title || 'Untitled',
    description: item.description || '',
    content: options?.includeContent ? (item.content || item.extractedText || '') : undefined,
    highlights: options?.highlightMatches ? generateHighlights(item, query) : undefined,
    score: item._score || 0.5,
    metadata: {
      createdAt: item.createdAt,
      createdBy: item.createdBy,
      organizationId: item.organizationId,
      projectId: item.projectId,
      tags: item.tags,
      categories: item.categories
    },
    url: generateItemUrl(item)
  }));

  // Generate facets if enabled
  const facets = options?.enableFacets ? generateFacets(allResults) : undefined;

  // Generate suggestions if enabled
  const suggestions = options?.enableSuggestions ? generateSuggestions(query, allResults) : undefined;

  // Generate semantic insights based on mode
  const semanticInsights = mode === SearchMode.SEMANTIC || mode === SearchMode.HYBRID
    ? generateSemanticInsights(query)
    : undefined;

  return {
    query,
    mode,
    scope,
    results: searchResults,
    total: rankedResults.length,
    page,
    limit,
    hasMore: offset + limit < rankedResults.length,
    facets,
    suggestions,
    semanticInsights
  };
}

/**
 * Get search containers based on scope
 */
function getSearchContainers(scope: SearchScope): string[] {
  switch (scope) {
    case SearchScope.DOCUMENTS:
      return ['documents'];
    case SearchScope.WORKFLOWS:
      return ['workflows'];
    case SearchScope.PROJECTS:
      return ['projects'];
    case SearchScope.USERS:
      return ['users'];
    case SearchScope.ALL:
    default:
      return ['documents', 'workflows', 'projects', 'users'];
  }
}

/**
 * Apply intelligent ranking based on search mode
 */
async function applyIntelligentRanking(
  results: any[],
  query: string,
  mode: SearchMode,
  personalizationSettings: any
): Promise<any[]> {
  // Simplified implementation - in production, this would use AI models for ranking

  return results.map(item => {
    let score = 0.5; // Base score

    // Text relevance scoring (simplified)
    const text = `${item.name || ''} ${item.description || ''} ${item.content || ''}`.toLowerCase();
    const queryTerms = query.toLowerCase().split(/\s+/);

    for (const term of queryTerms) {
      if (text.includes(term)) {
        score += 0.2;
      }
    }

    // Apply personalization boost if available
    if (personalizationSettings && mode !== SearchMode.BASIC) {
      if (personalizationSettings.preferredCategories?.includes(item.category)) {
        score += 0.1;
      }
      if (personalizationSettings.preferredAuthors?.includes(item.createdBy)) {
        score += 0.1;
      }
    }

    // Recency boost
    const daysSinceCreated = (Date.now() - new Date(item.createdAt).getTime()) / (1000 * 60 * 60 * 24);
    if (daysSinceCreated < 7) {
      score += 0.1;
    }

    return { ...item, _score: Math.min(score, 1.0) };
  }).sort((a, b) => b._score - a._score);
}

/**
 * Generate highlights for search results
 */
function generateHighlights(item: any, query: string): string[] {
  const highlights: string[] = [];
  const queryTerms = query.toLowerCase().split(/\s+/);

  const text = item.description || item.content || '';
  if (text) {
    for (const term of queryTerms) {
      const index = text.toLowerCase().indexOf(term);
      if (index !== -1) {
        const start = Math.max(0, index - 50);
        const end = Math.min(text.length, index + term.length + 50);
        const highlight = text.substring(start, end);
        highlights.push(`...${highlight}...`);
      }
    }
  }

  return highlights.slice(0, 3); // Limit to 3 highlights
}

/**
 * Generate facets for search results
 */
function generateFacets(results: any[]): { [key: string]: { value: string; count: number }[] } {
  const facets: { [key: string]: { [key: string]: number } } = {};

  // Generate type facets
  facets.type = {};
  facets.category = {};
  facets.createdBy = {};

  for (const item of results) {
    // Type facet
    const type = item._container || item.type || 'unknown';
    facets.type[type] = (facets.type[type] || 0) + 1;

    // Category facet
    if (item.category) {
      facets.category[item.category] = (facets.category[item.category] || 0) + 1;
    }

    // Author facet
    if (item.createdBy) {
      facets.createdBy[item.createdBy] = (facets.createdBy[item.createdBy] || 0) + 1;
    }
  }

  // Convert to required format
  const formattedFacets: { [key: string]: { value: string; count: number }[] } = {};

  for (const [facetName, facetValues] of Object.entries(facets)) {
    formattedFacets[facetName] = Object.entries(facetValues)
      .map(([value, count]) => ({ value, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10); // Limit to top 10
  }

  return formattedFacets;
}

/**
 * Generate search suggestions
 */
function generateSuggestions(query: string, results: any[]): string[] {
  // Simplified implementation - in production, this would use AI models
  const suggestions: string[] = [];

  // Extract common terms from results
  const terms = new Set<string>();
  for (const item of results.slice(0, 10)) {
    const text = `${item.name || ''} ${item.description || ''}`.toLowerCase();
    const words = text.split(/\s+/).filter(word => word.length > 3);
    words.forEach(word => terms.add(word));
  }

  // Generate suggestions based on query
  const queryWords = query.toLowerCase().split(/\s+/);
  for (const term of Array.from(terms).slice(0, 5)) {
    if (!queryWords.includes(term)) {
      suggestions.push(`${query} ${term}`);
    }
  }

  return suggestions.slice(0, 3);
}

/**
 * Generate semantic insights
 */
function generateSemanticInsights(query: string): {
  intent: string;
  entities: string[];
  concepts: string[];
  relatedQueries: string[];
} {
  // Simplified implementation - in production, this would use NLP models
  return {
    intent: 'search',
    entities: [],
    concepts: query.split(/\s+/).filter(word => word.length > 3),
    relatedQueries: [
      `${query} examples`,
      `${query} tutorial`,
      `${query} best practices`
    ]
  };
}

/**
 * Generate URL for search result item
 */
function generateItemUrl(item: any): string {
  const baseUrl = process.env.FRONTEND_BASE_URL || 'https://app.docucontext.com';
  const type = item._container || item.type;

  switch (type) {
    case 'documents':
      return `${baseUrl}/documents/${item.id}`;
    case 'workflows':
      return `${baseUrl}/workflows/${item.id}`;
    case 'projects':
      return `${baseUrl}/projects/${item.id}`;
    case 'users':
      return `${baseUrl}/users/${item.id}`;
    default:
      return `${baseUrl}/search?id=${item.id}`;
  }
}

/**
 * Record search analytics
 */
async function recordSearchAnalytics(
  searchRequest: IntelligentSearchRequest,
  user: any,
  searchResults: any
): Promise<void> {
  try {
    const analytics = {
      id: uuidv4(),
      userId: user.id,
      query: searchRequest.query,
      mode: searchRequest.mode,
      scope: searchRequest.scope,
      resultCount: searchResults.results.length,
      timestamp: new Date().toISOString(),
      organizationId: user.tenantId,
      tenantId: user.tenantId
    };

    await db.createItem('search-analytics', analytics);
  } catch (error) {
    logger.warn('Failed to record search analytics', { error });
  }
}

// Register functions
app.http('ai-intelligent-search', {
  methods: ['GET', 'POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'search/intelligent',
  handler: intelligentSearch
});

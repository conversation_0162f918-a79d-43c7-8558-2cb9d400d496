/**
 * User Personalization Function
 * Handles user personalization settings for search and content preferences
 * Migrated from old-arch/src/user-service/personalization/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Validation schemas
const updatePersonalizationSchema = Joi.object({
  personalizationStrength: Joi.number().min(0).max(1).optional(),
  defaultSearchMode: Joi.string().valid('basic', 'advanced', 'semantic', 'hybrid').optional(),
  preferredSortField: Joi.string().valid('relevance', 'date', 'name', 'size', 'type').optional(),
  preferredSortDirection: Joi.string().valid('asc', 'desc').optional(),
  defaultResultsPerPage: Joi.number().min(5).max(100).optional(),
  preferredCategories: Joi.array().items(Joi.string()).optional(),
  preferredContentTypes: Joi.array().items(Joi.string()).optional(),
  preferredAuthors: <AUTHORS>
  preferredTags: Joi.array().items(Joi.string()).optional(),
  useFacetedSearch: Joi.boolean().optional(),
  defaultFacets: Joi.array().items(Joi.string()).optional(),
  clusterResults: Joi.boolean().optional(),
  maxClusters: Joi.number().min(2).max(20).optional(),
  minClusterSize: Joi.number().min(2).max(10).optional(),
  additionalSettings: Joi.object().optional()
});

interface PersonalizationSettings {
  id: string;
  userId: string;
  personalizationStrength: number; // 0-1
  defaultSearchMode: 'basic' | 'advanced' | 'semantic' | 'hybrid';
  preferredSortField: string;
  preferredSortDirection: 'asc' | 'desc';
  defaultResultsPerPage: number;
  preferredCategories: string[];
  preferredContentTypes: string[];
  preferredAuthors: <AUTHORS>
  preferredTags: string[];
  useFacetedSearch: boolean;
  defaultFacets: string[];
  clusterResults: boolean;
  maxClusters: number;
  minClusterSize: number;
  additionalSettings: any;
  createdAt: string;
  updatedAt: string;
}

/**
 * Get user personalization settings handler
 */
export async function getUserPersonalization(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Get user personalization started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Get user's personalization settings
    const settingsQuery = 'SELECT * FROM c WHERE c.userId = @userId';
    const settings = await db.queryItems('user-personalization', settingsQuery, [user.id]);

    let userSettings: PersonalizationSettings;

    if (settings.length === 0) {
      // Create default settings
      userSettings = {
        id: uuidv4(),
        userId: user.id,
        personalizationStrength: 0.5,
        defaultSearchMode: "hybrid",
        preferredSortField: "relevance",
        preferredSortDirection: "desc",
        defaultResultsPerPage: 10,
        preferredCategories: [],
        preferredContentTypes: [],
        preferredAuthors: <AUTHORS>
        preferredTags: [],
        useFacetedSearch: true,
        defaultFacets: ["category", "contentType", "author"],
        clusterResults: false,
        maxClusters: 5,
        minClusterSize: 3,
        additionalSettings: {},
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      await db.createItem('user-personalization', userSettings);
    } else {
      userSettings = settings[0] as PersonalizationSettings;
    }

    // Get recent search history for enhanced personalization
    const searchHistoryQuery = 'SELECT TOP 10 * FROM c WHERE c.userId = @userId ORDER BY c.timestamp DESC';
    const recentSearches = await db.queryItems('search-history', searchHistoryQuery, [user.id]);

    // Get popular categories from analytics
    const analyticsQuery = `
      SELECT c.category, COUNT(1) as count
      FROM c
      WHERE c.userId = @userId
      GROUP BY c.category
      ORDER BY count DESC
      OFFSET 0 LIMIT 5
    `;
    const popularCategories = await db.queryItems('search-analytics', analyticsQuery, [user.id]);

    // Enhance settings with additional data
    const enhancedSettings = {
      ...userSettings,
      additionalSettings: {
        ...userSettings.additionalSettings,
        recentSearches,
        popularCategories
      }
    };

    logger.info("User personalization retrieved successfully", {
      correlationId,
      userId: user.id,
      personalizationStrength: userSettings.personalizationStrength
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: enhancedSettings
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get user personalization failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Update user personalization settings handler
 */
export async function updateUserPersonalization(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Update user personalization started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = updatePersonalizationSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const updates = value;

    // Get existing settings
    const settingsQuery = 'SELECT * FROM c WHERE c.userId = @userId';
    const settings = await db.queryItems('user-personalization', settingsQuery, [user.id]);

    let userSettings: PersonalizationSettings;

    if (settings.length === 0) {
      // Create new settings with updates
      userSettings = {
        id: uuidv4(),
        userId: user.id,
        personalizationStrength: updates.personalizationStrength || 0.5,
        defaultSearchMode: updates.defaultSearchMode || "hybrid",
        preferredSortField: updates.preferredSortField || "relevance",
        preferredSortDirection: updates.preferredSortDirection || "desc",
        defaultResultsPerPage: updates.defaultResultsPerPage || 10,
        preferredCategories: updates.preferredCategories || [],
        preferredContentTypes: updates.preferredContentTypes || [],
        preferredAuthors: <AUTHORS>
        preferredTags: updates.preferredTags || [],
        useFacetedSearch: updates.useFacetedSearch !== undefined ? updates.useFacetedSearch : true,
        defaultFacets: updates.defaultFacets || ["category", "contentType", "author"],
        clusterResults: updates.clusterResults || false,
        maxClusters: updates.maxClusters || 5,
        minClusterSize: updates.minClusterSize || 3,
        additionalSettings: updates.additionalSettings || {},
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      await db.createItem('user-personalization', userSettings);
    } else {
      // Update existing settings
      userSettings = {
        ...settings[0] as PersonalizationSettings,
        ...updates,
        updatedAt: new Date().toISOString()
      };

      await db.updateItem('user-personalization', userSettings);
    }

    // Get enhanced data for response
    const searchHistoryQuery = 'SELECT TOP 10 * FROM c WHERE c.userId = @userId ORDER BY c.timestamp DESC';
    const recentSearches = await db.queryItems('search-history', searchHistoryQuery, [user.id]);

    const analyticsQuery = `
      SELECT c.category, COUNT(1) as count
      FROM c
      WHERE c.userId = @userId
      GROUP BY c.category
      ORDER BY count DESC
      OFFSET 0 LIMIT 5
    `;
    const popularCategories = await db.queryItems('search-analytics', analyticsQuery, [user.id]);

    const enhancedSettings = {
      ...userSettings,
      additionalSettings: {
        ...userSettings.additionalSettings,
        recentSearches,
        popularCategories
      }
    };

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "user_personalization_updated",
      userId: user.id,
      organizationId: user.tenantId,
      timestamp: new Date().toISOString(),
      details: {
        updatedFields: Object.keys(updates),
        personalizationStrength: userSettings.personalizationStrength
      },
      tenantId: user.tenantId
    });

    logger.info("User personalization updated successfully", {
      correlationId,
      userId: user.id,
      updatedFields: Object.keys(updates)
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: enhancedSettings
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Update user personalization failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

// Register functions
app.http('user-personalization-get', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'users/personalization',
  handler: getUserPersonalization
});

app.http('user-personalization-update', {
  methods: ['PATCH', 'OPTIONS'],
  authLevel: 'function',
  route: 'users/personalization/update',
  handler: updateUserPersonalization
});

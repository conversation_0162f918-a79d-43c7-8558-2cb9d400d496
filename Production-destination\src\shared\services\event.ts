/**
 * Event Service
 * Handles domain events and event publishing/subscribing
 */

import { logger } from '../utils/logger';
import { db } from './database';
import { v4 as uuidv4 } from 'uuid';

export interface DomainEvent {
  id: string;
  type: string;
  aggregateId: string;
  aggregateType: string;
  version: number;
  data: any;
  metadata?: any;
  timestamp: string;
  userId?: string;
  organizationId?: string;
  tenantId?: string;
}

export interface EventHandler {
  eventType: string;
  handle(event: DomainEvent): Promise<void>;
}

export interface EventSubscription {
  id: string;
  eventType: string;
  handler: EventHandler;
  isActive: boolean;
}

export class EventService {
  private handlers: Map<string, EventHandler[]> = new Map();
  private subscriptions: Map<string, EventSubscription> = new Map();

  /**
   * Publish a domain event
   */
  async publishEvent(event: Omit<DomainEvent, 'id' | 'timestamp'>): Promise<string> {
    try {
      const domainEvent: DomainEvent = {
        ...event,
        id: uuidv4(),
        timestamp: new Date().toISOString()
      };

      // Store event in event store
      await this.storeEvent(domainEvent);

      // Process event handlers
      await this.processEvent(domainEvent);

      logger.info('Event published successfully', {
        eventId: domainEvent.id,
        eventType: domainEvent.type,
        aggregateId: domainEvent.aggregateId,
        aggregateType: domainEvent.aggregateType
      });

      return domainEvent.id;

    } catch (error) {
      logger.error('Failed to publish event', { error, event });
      throw error;
    }
  }

  /**
   * Subscribe to events
   */
  subscribe(eventType: string, handler: EventHandler): string {
    const subscriptionId = uuidv4();

    if (!this.handlers.has(eventType)) {
      this.handlers.set(eventType, []);
    }

    this.handlers.get(eventType)!.push(handler);

    const subscription: EventSubscription = {
      id: subscriptionId,
      eventType,
      handler,
      isActive: true
    };

    this.subscriptions.set(subscriptionId, subscription);

    logger.info('Event subscription created', {
      subscriptionId,
      eventType,
      handlerName: handler.constructor.name
    });

    return subscriptionId;
  }

  /**
   * Unsubscribe from events
   */
  unsubscribe(subscriptionId: string): boolean {
    const subscription = this.subscriptions.get(subscriptionId);

    if (!subscription) {
      return false;
    }

    const handlers = this.handlers.get(subscription.eventType);
    if (handlers) {
      const index = handlers.indexOf(subscription.handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }

    this.subscriptions.delete(subscriptionId);

    logger.info('Event subscription removed', {
      subscriptionId,
      eventType: subscription.eventType
    });

    return true;
  }

  /**
   * Store event in event store
   */
  private async storeEvent(event: DomainEvent): Promise<void> {
    try {
      await db.createItem('events', event);
    } catch (error) {
      logger.error('Failed to store event', { error, eventId: event.id });
      throw error;
    }
  }

  /**
   * Process event by calling all registered handlers
   */
  private async processEvent(event: DomainEvent): Promise<void> {
    const handlers = this.handlers.get(event.type) || [];

    if (handlers.length === 0) {
      logger.debug('No handlers registered for event type', { eventType: event.type });
      return;
    }

    const promises = handlers.map(async (handler) => {
      try {
        await handler.handle(event);
        logger.debug('Event handler completed successfully', {
          eventId: event.id,
          eventType: event.type,
          handlerName: handler.constructor.name
        });
      } catch (error) {
        logger.error('Event handler failed', {
          error,
          eventId: event.id,
          eventType: event.type,
          handlerName: handler.constructor.name
        });
        // Don't throw here to prevent one handler failure from affecting others
      }
    });

    await Promise.allSettled(promises);
  }

  /**
   * Get events for an aggregate
   */
  async getEventsForAggregate(aggregateId: string, aggregateType: string): Promise<DomainEvent[]> {
    try {
      const query = 'SELECT * FROM c WHERE c.aggregateId = @aggregateId AND c.aggregateType = @aggregateType ORDER BY c.version ASC';
      const events = await db.queryItems('events', query, [aggregateId, aggregateType]);

      return events as DomainEvent[];
    } catch (error) {
      logger.error('Failed to get events for aggregate', { error, aggregateId, aggregateType });
      throw error;
    }
  }

  /**
   * Get events by type
   */
  async getEventsByType(eventType: string, limit: number = 100): Promise<DomainEvent[]> {
    try {
      const query = 'SELECT * FROM c WHERE c.type = @eventType ORDER BY c.timestamp DESC OFFSET 0 LIMIT @limit';
      const events = await db.queryItems('events', query, [eventType, limit]);

      return events as DomainEvent[];
    } catch (error) {
      logger.error('Failed to get events by type', { error, eventType });
      throw error;
    }
  }

  /**
   * Replay events for an aggregate
   */
  async replayEvents(aggregateId: string, aggregateType: string, fromVersion?: number): Promise<void> {
    try {
      let query = 'SELECT * FROM c WHERE c.aggregateId = @aggregateId AND c.aggregateType = @aggregateType';
      const parameters = [aggregateId, aggregateType];

      if (fromVersion !== undefined) {
        query += ' AND c.version >= @param1';
        parameters.push(fromVersion.toString());
      }

      query += ' ORDER BY c.version ASC';

      const events = await db.queryItems('events', query, parameters);

      for (const event of events) {
        await this.processEvent(event as DomainEvent);
      }

      logger.info('Events replayed successfully', {
        aggregateId,
        aggregateType,
        eventCount: events.length,
        fromVersion
      });

    } catch (error) {
      logger.error('Failed to replay events', { error, aggregateId, aggregateType, fromVersion });
      throw error;
    }
  }
}

// Document Events
export class DocumentEventService {
  constructor(private eventService: EventService) {}

  async documentCreated(documentId: string, document: any, userId: string, organizationId: string, tenantId: string): Promise<void> {
    await this.eventService.publishEvent({
      type: 'DocumentCreated',
      aggregateId: documentId,
      aggregateType: 'Document',
      version: 1,
      data: {
        document,
        createdBy: userId
      },
      userId,
      organizationId,
      tenantId
    });
  }

  async documentUpdated(documentId: string, changes: any, userId: string, organizationId: string, tenantId: string): Promise<void> {
    await this.eventService.publishEvent({
      type: 'DocumentUpdated',
      aggregateId: documentId,
      aggregateType: 'Document',
      version: 1, // This should be incremented based on current version
      data: {
        changes,
        updatedBy: userId
      },
      userId,
      organizationId,
      tenantId
    });
  }

  async documentDeleted(documentId: string, userId: string, organizationId: string, tenantId: string): Promise<void> {
    await this.eventService.publishEvent({
      type: 'DocumentDeleted',
      aggregateId: documentId,
      aggregateType: 'Document',
      version: 1,
      data: {
        deletedBy: userId
      },
      userId,
      organizationId,
      tenantId
    });
  }

  async documentShared(documentId: string, shareDetails: any, userId: string, organizationId: string, tenantId: string): Promise<void> {
    await this.eventService.publishEvent({
      type: 'DocumentShared',
      aggregateId: documentId,
      aggregateType: 'Document',
      version: 1,
      data: {
        shareDetails,
        sharedBy: userId
      },
      userId,
      organizationId,
      tenantId
    });
  }

  async documentSigned(documentId: string, signatureDetails: any, userId: string, organizationId: string, tenantId: string): Promise<void> {
    await this.eventService.publishEvent({
      type: 'DocumentSigned',
      aggregateId: documentId,
      aggregateType: 'Document',
      version: 1,
      data: {
        signatureDetails,
        signedBy: userId
      },
      userId,
      organizationId,
      tenantId
    });
  }
}

// Workflow Events
export class WorkflowEventService {
  constructor(private eventService: EventService) {}

  async workflowCreated(workflowId: string, workflow: any, userId: string, organizationId: string, tenantId: string): Promise<void> {
    await this.eventService.publishEvent({
      type: 'WorkflowCreated',
      aggregateId: workflowId,
      aggregateType: 'Workflow',
      version: 1,
      data: {
        workflow,
        createdBy: userId
      },
      userId,
      organizationId,
      tenantId
    });
  }

  async workflowStarted(workflowId: string, executionId: string, userId: string, organizationId: string, tenantId: string): Promise<void> {
    await this.eventService.publishEvent({
      type: 'WorkflowStarted',
      aggregateId: workflowId,
      aggregateType: 'Workflow',
      version: 1,
      data: {
        executionId,
        startedBy: userId
      },
      userId,
      organizationId,
      tenantId
    });
  }

  async workflowCompleted(workflowId: string, executionId: string, result: any, userId: string, organizationId: string, tenantId: string): Promise<void> {
    await this.eventService.publishEvent({
      type: 'WorkflowCompleted',
      aggregateId: workflowId,
      aggregateType: 'Workflow',
      version: 1,
      data: {
        executionId,
        result,
        completedBy: userId
      },
      userId,
      organizationId,
      tenantId
    });
  }
}

// Export singleton instances
export const eventService = new EventService();
export const documentEventService = new DocumentEventService(eventService);
export const workflowEventService = new WorkflowEventService(eventService);
